variable "infrastructure_spec_file" {
  description = "Path to the JSON infrastructure specification file. The default assumes the file exists at 'infrastructure/vm-spec.json' relative to the root module. Override this variable or set TF_VAR_infrastructure_spec_file environment variable if your file is elsewhere."
  type        = string
  default     = "infrastructure/vm-spec_test.json"
}

variable "guest_id" {
  description = "The vSphere guest_id for the VM (e.g., windows9Server64Guest, ubuntu64Guest, etc.)"
  # Common values: windows9Server64Guest, ubuntu64Guest, centos7_64Guest, rhel7_64Guest, etc.
  # See: https://developer.vmware.com/docs/11749/vsphere-automation-api-rest/structures/com/vmware/vcenter/ovf/guest_os_type-structure/
  type    = string
  default = "windows9Server64Guest"
}

variable "vsphere_user" {
  description = "vSphere username"
  type        = string
}

variable "vsphere_password" {
  description = "vSphere password"
  type        = string
  sensitive   = true
}

variable "vsphere_server" {
  description = "vSphere server hostname (must be set by the user; no default provided)"
  type        = string
}

variable "allow_unverified_ssl" {
  description = "Allow unverified SSL certificates"
  type        = bool
  default     = true
}