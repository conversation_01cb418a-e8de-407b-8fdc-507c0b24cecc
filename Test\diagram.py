def validate_ip_request(dct, comments, poc_allowed_objects, ip_subnets):
    """
    Validates IP address request based on the provided parameters.
    
    Args:
        dct: Dictionary containing ip_range, object_description, object_type, object_name
        comments: List to append validation comments
        poc_allowed_objects: List of allowed object types for POC range
        ip_subnets: Dictionary mapping IP ranges to subnet descriptions
    
    Returns:
        tuple: (iprequest_valid, allocate_ip)
    """
    iprequest_valid = False
    allocate_ip = False
    
    if (dct['ip_range'].upper() == "NONE"):
        comments.append("No IP Address requested")
        iprequest_valid = True
    # Set Object Name create
    elif (dct['ip_range'].upper() == "OTHER"):
        comments.append("IP Address Allocation must be done manually")
        iprequest_valid = True
    else:
        # Need to action IP Request, set allocate to true
        allocate_ip = True
        if (dct['object_description'] == ""):
            comments.append("Value expected for Object Description.")
        # Validate POC IP Request
        elif dct['ip_range'].upper() == "POC":
            if (dct['object_type'].upper() == "CUSTOM") and (not "POC" in dct['object_name'].upper()):
                comments.append("POCxxxxxx expected as CUSTOM Object Name.")
            elif not dct['object_type'].upper() in poc_allowed_objects:
                comments.append(f"Only {poc_allowed_objects} allowed in {ip_subnets[dct['ip_range'].upper()]} IPAM address range.")
            else:
                comments.append(f"Issuing IPAM address in {ip_subnets[dct['ip_range'].upper()]} range")
                iprequest_valid = True
        else:                
            if (dct['object_type'].upper() == "CUSTOM") and ("POC" in dct['object_name'].upper()):
                comments.append(f"{dct['object_name'].upper()} invalid for {dct['ip_range'].upper()} IP Range")
            else:
                comments.append(f"Issuing IPAM address in {ip_subnets[dct['ip_range'].upper()]} range")
                iprequest_valid = True
    
    return iprequest_valid, allocate_ip


# Example usage:
if __name__ == "__main__":
    # Example data structures
    poc_allowed_objects = ['SERVER', 'WORKSTATION', 'CUSTOM']
    ip_subnets = {
        'POC': 'POC Development Network',
        'PROD': 'Production Network',
        'DEV': 'Development Network'
    }
    
    comments = []
    
    # Example test case
    test_request = {
        'ip_range': 'POC',
        'object_description': 'Test server',
        'object_type': 'CUSTOM',
        'object_name': 'POC001'
    }
    
    valid, allocate = validate_ip_request(test_request, comments, poc_allowed_objects, ip_subnets)
    print(f"Valid: {valid}, Allocate: {allocate}")
    print(f"Comments: {comments}")