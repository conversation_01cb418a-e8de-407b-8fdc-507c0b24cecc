# AWS Image Builder Component: Install Visual C++ Redistributables
# This component installs multiple versions of Visual C++ Redistributables

name: win-server-vcredist
description: Install Visual C++ Redistributables (2013, 2015-2022) for x86 and x64
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: CheckExistingVCRedist
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Checking for existing Visual C++ Redistributables..."
        
        # Check installed programs for VC++ redistributables
        $vcRedistPrograms = Get-WmiObject -Class Win32_Product | Where-Object { 
            $_.Name -like "*Visual C++*" -or $_.Name -like "*Microsoft Visual C++*" 
        } | Select-Object Name, Version | Sort-Object Name
        
        if ($vcRedistPrograms) {
            Write-Host "Found existing Visual C++ Redistributables:"
            $vcRedistPrograms | Format-Table -AutoSize
        } else {
            Write-Host "No Visual C++ Redistributables found via WMI"
        }
        
        # Also check registry for more comprehensive detection
        $registryPaths = @(
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*",
            "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*"
        )
        
        $vcRedistRegistry = @()
        foreach ($path in $registryPaths) {
            $vcRedistRegistry += Get-ItemProperty $path -ErrorAction SilentlyContinue | 
                Where-Object { $_.DisplayName -like "*Visual C++*" } |
                Select-Object DisplayName, DisplayVersion
        }
        
        if ($vcRedistRegistry) {
            Write-Host "`nVisual C++ Redistributables found in registry:"
            $vcRedistRegistry | Sort-Object DisplayName | Format-Table -AutoSize
        }

  - name: PrepareInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Preparing Visual C++ Redistributables installation..."
        
        # Create temp directory
        $tempDir = "C:\temp\vcredist"
        if (!(Test-Path $tempDir)) {
            New-Item -ItemType Directory -Path $tempDir -Force
            Write-Host "Created temp directory: $tempDir"
        }
        
        # Define download URLs for Visual C++ Redistributables
        $vcRedistDownloads = @{
            "VC2013_x86" = "https://aka.ms/highdpimfc2013x86enu"
            "VC2013_x64" = "https://aka.ms/highdpimfc2013x64enu"
            "VC2015-2022_x86" = "https://aka.ms/vs/17/release/vc_redist.x86.exe"
            "VC2015-2022_x64" = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
        }
        
        Write-Host "Download URLs prepared for Visual C++ Redistributables"
        $vcRedistDownloads.GetEnumerator() | ForEach-Object {
            Write-Host "  $($_.Key): $($_.Value)"
        }

  - name: DownloadVCRedist2013
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Downloading Visual C++ 2013 Redistributables..."
        
        $tempDir = "C:\temp\vcredist"
        
        # Download VC++ 2013 x86
        $vc2013x86Url = "https://aka.ms/highdpimfc2013x86enu"
        $vc2013x86Path = "$tempDir\vcredist_2013_x86.exe"
        
        Write-Host "Downloading VC++ 2013 x86..."
        try {
            Invoke-WebRequest -Uri $vc2013x86Url -OutFile $vc2013x86Path -UseBasicParsing -TimeoutSec 300
            Write-Host "VC++ 2013 x86 download completed"
        } catch {
            Write-Warning "Failed to download VC++ 2013 x86: $($_.Exception.Message)"
        }
        
        # Download VC++ 2013 x64
        $vc2013x64Url = "https://aka.ms/highdpimfc2013x64enu"
        $vc2013x64Path = "$tempDir\vcredist_2013_x64.exe"
        
        Write-Host "Downloading VC++ 2013 x64..."
        try {
            Invoke-WebRequest -Uri $vc2013x64Url -OutFile $vc2013x64Path -UseBasicParsing -TimeoutSec 300
            Write-Host "VC++ 2013 x64 download completed"
        } catch {
            Write-Warning "Failed to download VC++ 2013 x64: $($_.Exception.Message)"
        }

  - name: DownloadVCRedist2015Plus
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Downloading Visual C++ 2015-2022 Redistributables..."
        
        $tempDir = "C:\temp\vcredist"
        
        # Download VC++ 2015-2022 x86
        $vc2015x86Url = "https://aka.ms/vs/17/release/vc_redist.x86.exe"
        $vc2015x86Path = "$tempDir\vcredist_2015-2022_x86.exe"
        
        Write-Host "Downloading VC++ 2015-2022 x86..."
        try {
            Invoke-WebRequest -Uri $vc2015x86Url -OutFile $vc2015x86Path -UseBasicParsing -TimeoutSec 300
            Write-Host "VC++ 2015-2022 x86 download completed"
        } catch {
            Write-Warning "Failed to download VC++ 2015-2022 x86: $($_.Exception.Message)"
        }
        
        # Download VC++ 2015-2022 x64
        $vc2015x64Url = "https://aka.ms/vs/17/release/vc_redist.x64.exe"
        $vc2015x64Path = "$tempDir\vcredist_2015-2022_x64.exe"
        
        Write-Host "Downloading VC++ 2015-2022 x64..."
        try {
            Invoke-WebRequest -Uri $vc2015x64Url -OutFile $vc2015x64Path -UseBasicParsing -TimeoutSec 300
            Write-Host "VC++ 2015-2022 x64 download completed"
        } catch {
            Write-Warning "Failed to download VC++ 2015-2022 x64: $($_.Exception.Message)"
        }

  - name: InstallVCRedist2013
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing Visual C++ 2013 Redistributables..."
        
        $tempDir = "C:\temp\vcredist"
        
        # Install VC++ 2013 x86
        $vc2013x86Path = "$tempDir\vcredist_2013_x86.exe"
        if (Test-Path $vc2013x86Path) {
            Write-Host "Installing VC++ 2013 x86..."
            $process = Start-Process -FilePath $vc2013x86Path -ArgumentList "/install", "/quiet", "/norestart" -Wait -PassThru
            Write-Host "VC++ 2013 x86 installation completed with exit code: $($process.ExitCode)"
        } else {
            Write-Warning "VC++ 2013 x86 installer not found"
        }
        
        # Install VC++ 2013 x64
        $vc2013x64Path = "$tempDir\vcredist_2013_x64.exe"
        if (Test-Path $vc2013x64Path) {
            Write-Host "Installing VC++ 2013 x64..."
            $process = Start-Process -FilePath $vc2013x64Path -ArgumentList "/install", "/quiet", "/norestart" -Wait -PassThru
            Write-Host "VC++ 2013 x64 installation completed with exit code: $($process.ExitCode)"
        } else {
            Write-Warning "VC++ 2013 x64 installer not found"
        }

  - name: InstallVCRedist2015Plus
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing Visual C++ 2015-2022 Redistributables..."
        
        $tempDir = "C:\temp\vcredist"
        
        # Install VC++ 2015-2022 x86
        $vc2015x86Path = "$tempDir\vcredist_2015-2022_x86.exe"
        if (Test-Path $vc2015x86Path) {
            Write-Host "Installing VC++ 2015-2022 x86..."
            $process = Start-Process -FilePath $vc2015x86Path -ArgumentList "/install", "/quiet", "/norestart" -Wait -PassThru
            Write-Host "VC++ 2015-2022 x86 installation completed with exit code: $($process.ExitCode)"
        } else {
            Write-Warning "VC++ 2015-2022 x86 installer not found"
        }
        
        # Install VC++ 2015-2022 x64
        $vc2015x64Path = "$tempDir\vcredist_2015-2022_x64.exe"
        if (Test-Path $vc2015x64Path) {
            Write-Host "Installing VC++ 2015-2022 x64..."
            $process = Start-Process -FilePath $vc2015x64Path -ArgumentList "/install", "/quiet", "/norestart" -Wait -PassThru
            Write-Host "VC++ 2015-2022 x64 installation completed with exit code: $($process.ExitCode)"
        } else {
            Write-Warning "VC++ 2015-2022 x64 installer not found"
        }

  - name: VerifyInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Verifying Visual C++ Redistributables installation..."
        
        # Check registry for installed VC++ redistributables
        $registryPaths = @(
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*",
            "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*"
        )
        
        $installedVCRedist = @()
        foreach ($path in $registryPaths) {
            $installedVCRedist += Get-ItemProperty $path -ErrorAction SilentlyContinue | 
                Where-Object { $_.DisplayName -like "*Visual C++*" } |
                Select-Object DisplayName, DisplayVersion, Publisher
        }
        
        if ($installedVCRedist) {
            Write-Host "SUCCESS: Visual C++ Redistributables found:"
            $installedVCRedist | Sort-Object DisplayName | Format-Table -AutoSize
        } else {
            Write-Warning "No Visual C++ Redistributables found in registry"
        }
        
        # Check for specific expected versions
        $expectedVersions = @("2013", "2015", "2017", "2019", "2022")
        $foundVersions = @()
        
        foreach ($version in $expectedVersions) {
            $found = $installedVCRedist | Where-Object { $_.DisplayName -like "*$version*" }
            if ($found) {
                $foundVersions += $version
                Write-Host "✓ Visual C++ $version Redistributable found"
            }
        }
        
        Write-Host "`nInstallation Summary:"
        Write-Host "Expected versions: $($expectedVersions -join ', ')"
        Write-Host "Found versions: $($foundVersions -join ', ')"

  - name: Cleanup
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Cleaning up installation files..."
        
        $tempDir = "C:\temp\vcredist"
        if (Test-Path $tempDir) {
            try {
                Remove-Item $tempDir -Recurse -Force
                Write-Host "Cleanup completed successfully"
            } catch {
                Write-Warning "Failed to clean up temp directory: $($_.Exception.Message)"
            }
        }

- name: validate
  steps:
  - name: ValidateVCRedistInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Final validation of Visual C++ Redistributables installation..."
        
        # Check registry for all installed VC++ redistributables
        $registryPaths = @(
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*",
            "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*"
        )
        
        $allVCRedist = @()
        foreach ($path in $registryPaths) {
            $allVCRedist += Get-ItemProperty $path -ErrorAction SilentlyContinue | 
                Where-Object { $_.DisplayName -like "*Visual C++*" } |
                Select-Object DisplayName, DisplayVersion, Publisher, InstallDate
        }
        
        if ($allVCRedist.Count -eq 0) {
            Write-Error "VALIDATION FAILED: No Visual C++ Redistributables found"
            exit 1
        }
        
        Write-Host "VALIDATION SUCCESS: Found $($allVCRedist.Count) Visual C++ Redistributable(s)"
        Write-Host "`nInstalled Visual C++ Redistributables:"
        $allVCRedist | Sort-Object DisplayName | Format-Table DisplayName, DisplayVersion, InstallDate -AutoSize
        
        # Check for both x86 and x64 versions
        $x86Count = ($allVCRedist | Where-Object { $_.DisplayName -like "*x86*" }).Count
        $x64Count = ($allVCRedist | Where-Object { $_.DisplayName -like "*x64*" }).Count
        
        Write-Host "`nArchitecture Summary:"
        Write-Host "x86 (32-bit) redistributables: $x86Count"
        Write-Host "x64 (64-bit) redistributables: $x64Count"
        
        if ($x64Count -gt 0) {
            Write-Host "✓ x64 redistributables installed (recommended for 64-bit applications)"
        }
        
        if ($x86Count -gt 0) {
            Write-Host "✓ x86 redistributables installed (required for 32-bit applications)"
        }
