# AWS Image Builder Pipeline: Windows Server 2022 Base Image Pipeline
# This pipeline orchestrates the base image building process

name: WindowsServer2022BasePipeline
description: Pipeline for building Windows Server 2022 base AMI with .NET 4.8, registry optimizations, and firewall rules for business deployment

# Pipeline configuration
imageRecipeArn: "arn:aws:imagebuilder:af-south-1:123456789012:image-recipe/windowsserver2022base/1.0.0" # Replace with your image recipe ARN
infrastructureConfigurationArn: "arn:aws:imagebuilder:af-south-1:123456789012:infrastructure-configuration/windowsserver2022buildinfrastructure" # Replace with your infrastructure config ARN
distributionConfigurationArn: "arn:aws:imagebuilder:af-south-1:123456789012:distribution-configuration/windowsserver2022distribution" # Replace with your distribution config ARN

# Image testing configuration
imageTestsConfiguration:
  imageTestsEnabled: true
  timeoutMinutes: 720 # 12 hours timeout for comprehensive testing

# Schedule configuration (optional)
schedule:
  scheduleExpression: "cron(0 2 ? * SUN *)" # Run every Sunday at 2 AM UTC
  pipelineExecutionStartCondition: "EXPRESSION_MATCH_AND_DEPENDENCY_UPDATES_AVAILABLE"

# Enhanced logging
enhancedImageMetadataEnabled: true

# Pipeline tags
tags:
  Name: WindowsServer2022BasePipeline
  Environment: Production
  OS: Windows Server 2022
  Purpose: Base AMI Creation
  Schedule: Weekly
  Owner: Infrastructure Team
  Components: ".NET Framework 4.8, Registry Optimizations, Firewall Rules"
