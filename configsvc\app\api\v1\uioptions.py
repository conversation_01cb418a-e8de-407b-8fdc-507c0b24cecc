from datetime import datetime, date, time
import json
from app import logging

from app.core.helpers import key_exists, dictval2list, dct2df, recast_json
#import MariaDB connection
from app.core.database import dbQuery


# Query DB for UI Options
def UiInit():
    try:
        # Query db for initial UI options
        qrytxt = f"select * from ui_init;"
        logging.info(f"QUERY: {qrytxt}")
        dfQry = dbQuery(qrytxt)
        dfQry['updated'] = dfQry['updated'].astype(str)
        dfInit = recast_json(dfQry)
        dctRes = dfInit.to_dict(orient='records')
    except:
        # Return error
        message = {"message" : "Issue with UI Init data in table", "data":""}
        logging.warning(message['message'])
        return message
    return dictval2list(dctRes[0],",")

# Process Filters
def ProcessFilters(dct):
    logging.info(f"PARAMETERS => {dct}")
    if not bool(dct):
        logging.warning(f"PARAMETERS issue...")
        success = False
        data = ""
        message = f"Verify UI selections..."
        status = "WARN"
        logging.warning(message)
    else:
        respdata = UiOptions(dct)
        # dctClient = getClient(params)
        # dctEnviron = getEnvirons(params)
        # respdata = {**dctClient, **dctEnviron}
        # print(json.dumps(respdata, indent=2),"\n")   
        if respdata['data'] == "":
            success = False
            data = ""
            message = respdata['message'] #f"Invalid selection, please retry."
            status = "FAILED"
            logging.error(message)
        else:
            success =True
            message = f"Result(s) returned."
            data = respdata['data']
            status = "NORMAL"
            logging.info(message)
    result = {
        'data' : data,
        'status': status, 
        'success' : success,
        'message' : message
    }

    return result

# Query DB for UI Options

def getClient(dct):
    qrytxt = f"select * from clients where lower(client) = lower('{dct['client']}') ;"
    logging.info(f"QUERY - getClient: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    return dctRes

def getDeployment(dct):
    print(f"GET DEPLOYMENT:\n{dct}")
    qrytxt = f"select * from ui_deployments where lower(deployment) = lower('{dct['deployment']}')\
        and upper(aso_tag) = upper('{dct['aso_tag']}')\
        and lower(app_types) like lower('%{dct['app_type']}%');"
    logging.info(f"QUERY - getDeployment: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    return dctRes[0]

def getOStypes(dct):
    qrytxt = \
        f"select * from ui_ostypes where os_type = '{dct['os_type']}' and app_type = '{dct['app_type']}' ;"
    logging.info(f"QUERY - getOStypes: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    # print(f"Results: {len(dctRes)}\n")
    # print(f"Values: {dctRes}\n")
    return dctRes[0]

# Query DB for UI Options
def getIpConfigs(dct):
    qrytxt = f"select * from ipconfigs\
        where aso_tag = '{dct['aso_tag']}'\
            and app_type = '{dct['app_type']}'\
            and os_type = '{dct['os_type']}'\
            and sla_type = '{dct['sla_type']}'\
        ;"
    logging.info(f"QUERY - getIpConfigs: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    # print(f"Results: {len(dctRes)}\n")
    # print(f"Values: {dctRes}\n")
    return dctRes[0]

# Query DB for UI Options
def getResources(dct):
    # print(dct)
    qrytxt = f"select * from ui_resources\
        where aso_tag = '{dct['aso_tag']}'\
            and app_type = '{dct['app_type']}'\
            and os_type = '{dct['os_type']}'\
            and sla_type = '{dct['sla_type']}'\
        ;"
    logging.info(f"QUERY - getResources: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    # print(f"Results: {len(dctRes)}\n")
    # print(f"Values: {dctRes}\n")
    return dctRes[0]

# Query DB for UI Options
def getHosting(dct):
    # print(dct)
    qrytxt = f"select * from vcconfigs\
        where aso_tag = '{dct['aso_tag']}'\
            and lower(app_types) like lower('%{dct['app_type']}%')\
            and os_type = '{dct['os_type']}'\
            and sla_type = '{dct['sla_type']}'\
        ;"
    logging.info(f"QUERY - getHosting: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dfQry.drop(columns=['updated'], inplace=True)
    dctRes = dfQry.to_dict(orient='records')
    # print(f"Results: {len(dctRes)}\n")
    # print(f"Values: {dctRes}\n")
    return dctRes[0]

# Query DB for UI Options
def UiOptions(options):
    print(f"UI Filters:\n{options}")
    dctRes = {}

    if key_exists('client',options):
        try:
            dctClient = getClient(options)[0]
            # dctRes.update({'data':dctClient})
        except:
            return {'message' : "Invalid client selected", "data":""}
    else:
        return {'message' : "No client specified", "data":""}

    deployment = {**options, **dctClient}    # merge Client query result with options
    print("UI DEPLOYMENT\n",deployment)

    if key_exists('deployment',options):
        try:
            dctDeployTypes = getDeployment(deployment) #[0] #getEnvirons(options) #
            # dctRes.append({'data':dctEnvs})
        except:
            return {'message' : "Invalid deployment or app/os type selection", "data":""}
    else:
        return {'message' : "No deployment specified", "data":""}
    
    if key_exists('app_type',options):
        try:
            dctOStypes = getOStypes(options)
        except:
            return {'message' : "Invalid OS or App Type", "data":""}
    else:
        return {'message' : "No OS or App Type specified", "data":""}
    
    dctRes['data'] = {**dctClient, **dctDeployTypes, **dctOStypes} #, **dctEnvs} #}
    # Get IP Options
    try:
        dctIpConfigs = getIpConfigs(dctRes['data']) #[0] #getEnvirons(options) #
        # dctRes.append({'data':dctEnvs})
    except:
        return {'message' : "Invalid selections or No IP configs defined!", "data":""}
    # Get Resource Options
    try:
        dctResources = getResources(dctRes['data']) #[0] #getEnvirons(options) #
        # dctRes.append({'data':dctEnvs})
    except:
        return {'message' : "Invalid selections or No server resources defined!", "data":""}
    # Get Hosting Options
    try:
        dctHosting = getHosting(dctRes['data']) #[0] #getEnvirons(options) #
        # dctRes.append({'data':dctEnvs})
    except:
        return {'message' : "Invalid selections or No hosting defined!", "data":""}
    
    dctOptions = {**dctClient, **dctDeployTypes, **dctOStypes, **dctResources, **dctIpConfigs, **dctHosting}
    # print(json.dumps(dctOptions, indent=2),"\n")
    dctRes['data'] = dictval2list(dctOptions,",") #{**dctClient, **dctDeployTypes, **dctOStypes, **dctResources}
    # print(f"Results: {len(dctRes)}\n")
    return dctRes
