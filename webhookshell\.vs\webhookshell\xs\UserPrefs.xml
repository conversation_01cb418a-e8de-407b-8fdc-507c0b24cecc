﻿<Properties StartupConfiguration="{A00D04C3-FB24-47E9-9805-3644A82A2053}|Default">
  <MonoDevelop.Ide.Workspace ActiveConfiguration="Debug" />
  <MonoDevelop.Ide.DebuggingService.Breakpoints>
    <BreakpointStore />
  </MonoDevelop.Ide.DebuggingService.Breakpoints>
  <MonoDevelop.Ide.Workbench>
    <Pads>
      <Pad Id="ProjectPad">
        <State name="__root__">
          <Node name="Webhookshell">
            <Node name="Webhookshell">
              <Node name="Services" expanded="True" />
            </Node>
          </Node>
        </State>
      </Pad>
    </Pads>
  </MonoDevelop.Ide.Workbench>
  <MonoDevelop.Ide.ItemProperties.Webhookshell PreferredExecutionTarget="/Applications/Google Chrome.app" />
  <MultiItemStartupConfigurations />
  <MonoDevelop.Ide.DebuggingService.PinnedWatches />
</Properties>