import json
from java.text import SimpleDateFormat
from java.util import Calendar, Date

# Add wait days to delete_date before deleting instance
timestamp_format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'")
chosen_date = "${scheduledDate}"
expected_action_date = timestamp_format.parse(chosen_date)
wanted_cal = Calendar.getInstance()
wanted_cal.setTime(expected_action_date)

# Format the date for act_date
formatted_act_date = timestamp_format.format(wanted_cal.getTime())

releaseVariables['dns_request']  = {
    "reference": "${reference}",
    "requested_by": "${requester}",
    "requested_on": "${requested_on}",
    "catagory": "${catagory}",
    "record_type": "${record_type}",
    "act_date": formatted_act_date,
    "ptr": "${ptr}",
    "hostname": "${hostname}",
    "ip_address": "${ip_address}",
    "host_alias": "${host_alias}",
    "new_hostname": "${new_hostname}",
    "new_ip_address": "${new_ip_address}",
    "new_host_alias": "${new_host_alias}",
    "status": "${status}",
    "completed_datetime": "${completed_datetime}",
    "jobTask": "${jobTask}",
    "success": "${success}"
}

print(releaseVariables['dns_request'])