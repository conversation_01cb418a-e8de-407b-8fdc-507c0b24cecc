### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition
$config = Get-Content -Path "$currentPath\scriptConfigs.json" | ConvertFrom-Json
$AutoDeployModule = $config.AutoDeployModule
$ADDMModule = $config.ADDMModule

### Import module for connecting to APIs ###
Import-Module $AutoDeployModule
Import-Module $ADDMModule

### Paths ##
$appSource = $config.AppSourcePath
$credPath = $config.CredPath
$cliCredsPath = "$credPath\cliCreds.xml"
$adminCredLocation = "$credPath\windowsCreds.xml"
$mudCreds = "$credPath\prdDomainCreds.xml"
$ppeCreds = "$credPath\ppeDomainCreds.xml"
$devCreds = "$credPath\devDomainCreds.xml"

### Hard coded identifiers ###
$jobStage = "OS_CONFIG"
$completeJobStatus = "COMPLETED"
$receivedJobStatus = "IN_PROGRESS"
$requestJobStatus = "PENDING"
$global:configSuccess = $false
$global:finalResponse = ""
$global:configRequired = $false
$global:backupRequired = $false
$global:monitoringRequired = $false

### CodeRefs for configs ###
$localAdminDefault = "Administrator"
$correctAdminName = "adminacc"
$mudADGroups = "mudadGroups"
$devADGroups = "devadGroups"
$ppeADGroups = "ppeadGroups"
$mudsuffixList = "muddnsList"
$devsuffixList = "devdnsList"
$ppesuffixList = "ppednsList"
$mudsuffixList = "muddnsList"
$mudMSName = "mudSCOMserver"
$devMSName = "devSCOMserver"
$ppeMSName = "ppeSCOMserver"
$mudMSGroup = "mudSCOMGroup"
$devMSGroup = "devSCOMGroup"
$ppeMSGroup = "ppeSCOMGroup"
$mudMSPort = "mudSCOMPort"
$devMSPort = "devSCOMPort"
$ppeMSPort = "ppeSCOMPort"
$sqlBlock = "sqlBlock"
$sharedBlock = "ndbBlock"
$ppCode = "ppCode"
$timeCode = "timezone"

### Competencies ###
$bcxLinuxComp = $config.BCXLinuxCompetency

### Standards ###
$mudAdminGroups = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $mudADGroups}).Details
$devAdminGroups = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $devADGroups}).Details
$ppeAdminGroups = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $ppeADGroups}).Details

$mudSuffixSearchList = ((Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $mudsuffixList}).Details).split(";")
$devSuffixSearchList = ((Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $devsuffixList}).Details).split(";")
$ppeSuffixSearchList = ((Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $ppesuffixList}).Details).split(";")

$mudSCOMServer = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $mudMSName}).Details
$devSCOMServer = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $devMSName}).Details
$ppeSCOMServer = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $ppeMSName}).Details
$mudSCOMServerGroup = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $mudMSGroup}).Details
$devSCOMServerGroup = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $devMSGroup}).Details
$ppeSCOMServerGroup = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $ppeMSGroup}).Details
$mudSCOMServerPort = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $mudMSPort}).Details
$devSCOMServerPort = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $devMSPort}).Details
$ppeSCOMServerPort = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $ppeMSPort}).Details

$localAdminCorrect = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $correctAdminName}).Details
$sqlBlockSize = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $sqlBlock}).Details
$sharedBlockSize = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $sharedBlock}).Details
$powerplan = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $ppCode}).Details
$time = (Import-Excel -Path $config.BuildConfigsPath -WorksheetName "OS" | Where-Object {$_.CodeRef -eq $timeCode}).Details

Function Config_Management{
    $apiInputData = Get-Job -JobID "" -JobStatus $requestJobStatus -JobStage $jobStage -HostingPlatform $hostingPlatform

    if($apiInputData){
        Update-Job -JobID $apiInputData.request_jobid -JobStage $jobStage -JobStatus $receivedJobStatus -SuccessStatus $true -LogMessage "OS Config Started" -LogType "INFO" -IssueRef " " -IssueType " "
    }else{
        exit
    }
    $finalResponse = ""
    if($apiInputData.os_version -match "Windows Server"){
        $osConfig = WindowsOsConfigs -buildSpecs $apiInputData
        $configSuccess = $osConfig.Succeeded
        $finalResponse = $osConfig.Message
        $addmStarted = StartADDMScan -specInfo $apiInputData
    }elseif($apiInputData.competency -eq $bcxLinuxComp){
        $osConfig = BCXLinuxOsConfigs -buildSpecs $apiInputData
        $configSuccess = $osConfig.Succeeded
        $finalResponse = $osConfig.Message
        $addmStarted = StartADDMScan -specInfo $apiInputData
    }else{
        $configSuccess = $true
        $addmStarted = $true
        $finalResponse = "OS Config Not Required"
    }

    if(!($addmStarted)){
        $finalResponse += ", ADDM scan failed to start"
    }else{
        $finalResponse += ", ADDM scan started successfully"
    }
    $finalResponse = $finalResponse -join ""
    $configSuccess = [boolean]::Parse($configSuccess)
    Update-Job -JobID $apiInputData.request_jobid -JobStage $jobStage -JobStatus $completeJobStatus -SuccessStatus $configSuccess -LogMessage $finalResponse -LogType "INFO" -IssueRef " " -IssueType "OS_CONFIG"

}

Function WindowsOsConfigs{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$buildSpecs
    )
    
    ### initialize variables ###
    $serverName = $buildSpecs.vm_name
    $fqdn = $serverName + "." + $buildSpecs.dns_suffix
    $serverDescription = $buildSpecs.vm_description
    $app = $buildSpecs.app_type
    $sla = $buildSpecs.environment
    $osVersion = $buildSpecs.os_version

    if($buildSpecs.dns_suffix -like "dev*"){
        $creds = Import-Clixml -Path $devCreds
        $adminGroupAccounts = $devAdminGroups
        $dnsSearchList = $devSuffixSearchList
        $SCOMServer = $devSCOMServer
        $SCOMGroup = $devSCOMServerGroup
        $SCOMPort = $devSCOMServerPort
    }elseif($buildSpecs.dns_suffix -like "ppe*"){
        $creds = Import-Clixml -Path $ppeCreds
        $adminGroupAccounts = $ppeAdminGroups
        $dnsSearchList = $ppeSuffixSearchList
        $SCOMServer = $ppeSCOMServer
        $SCOMGroup = $ppeSCOMServerGroup
        $SCOMPort = $ppeSCOMServerPort
    }else{
        $creds = Import-Clixml -Path $mudCreds
        $adminGroupAccounts = $mudAdminGroups
        $dnsSearchList = $mudSuffixSearchList
        $SCOMServer = $mudSCOMServer
        $SCOMGroup = $mudSCOMServerGroup
        $SCOMPort = $mudSCOMServerPort
    }

    if($sla -like "prd"){
        $backupRequired = $true
    }
    if($app -notmatch "POC"){
        $monitoringRequired = $true
    }

    ### get disk details and formatting ###
    $tier2disks = ($buildSpecs.t2storage).Split(",") | Where-Object {$_.length -gt 0}
    $tier2drives = ($buildSpecs.t2drives).Split(",") | Where-Object {$_.length -gt 0}
    $tier3disks = ($buildSpecs.t3storage).Split(",") | Where-Object {$_.length -gt 0}
    $tier3drives = ($buildSpecs.t3drives).Split(",") | Where-Object {$_.length -gt 0}

    $diskDetails = @()
    if($app -match "SQL"){
        $blockSize = $sqlBlockSize    
    }else{
        $blockSize = $sharedBlockSize
    }

    if($tier2drives.Length -gt 1){   
        for($i = 0; $i -lt $tier2drives.length; $i++){
            $indivDisk = [PSCustomObject]@{
                DiskLetter = $tier2drives[$i]
                DiskSize = $tier2disks[$i]
                BlockSize = $blockSize
            }
            $diskDetails += $indivDisk
        }
    }else{
        $indivDisk = [PSCustomObject]@{
            DiskLetter = $tier2drives
            DiskSize = $tier2disks
            BlockSize = $blockSize
        }
        $diskDetails += $indivDisk
    }
    
    if($tier3drives.Length -gt 1){   
        for($i = 0; $i -lt $tier3drives.length; $i++){
            $indivDisk = [PSCustomObject]@{
                DiskLetter = $tier3drives[$i]
                DiskSize = $tier3disks[$i]
                BlockSize = $blockSize
            }
            $diskDetails += $indivDisk
        }
    }else{
        $indivDisk = [PSCustomObject]@{
            DiskLetter = $tier3drives
            DiskSize = $tier3disks
            BlockSize = $blockSize
        }
        $diskDetails += $indivDisk
    }

    ### call functions of os configs ###
    ### Critical functions have a callback / Non Critical functions no callback ###
    $accounts = ConfigAccounts -serverName $fqdn -user $creds -accountsToAdd $adminGroupAccounts
    ConfigNics -serverName $fqdn -user $creds -dnsList $dnsSearchList
    ConfigIdentity -serverName $fqdn -user $creds -description $serverDescription -time $time -powerplan $powerplan
    ConfigDisks -serverName $fqdn -user $creds -diskinfo $diskDetails -osSize $buildSpecs.os_disk
    ConfigDesktop -serverName $fqdn -user $creds -sla $sla
    ConfigSCCM -serverName $fqdn -user $creds
    ConfigCrowdStrike -serverName $fqdn -user $creds
    if($backupRequired){
        ConfigBackupAgent -serverName $serverName
    }
    if($monitoringRequired){
        ConfigMonitoringAgent -serverName $fqdn -user $creds -mgmtServerFQDN $SCOMServer -mgmtServerGroup $SCOMGroup -mgmtServerPort $SCOMPort -osVersion $osVersion
    }
    $restartServer = ServerReboot -serverName $fqdn -user $creds

if($accounts){
    if($restartServer){
        $critCompleted = $true
        $message = "OS Config Complete"
    }else{
        $critCompleted = $true
        $message = "OS Config Complete - Server Reboot still required"
    }
}else{
    $critCompleted = $false
    $message = "OS Config Failed To Access Server - possible redeploy required"
}

$task = "" | Select-Object Succeeded, Message
$task.Succeeded = $critCompleted
$task.Message = $message
$task
}

Function ConfigAccounts{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$serverName,
        [Parameter(Mandatory=$true)]$user,
        [Parameter(Mandatory=$true)]$accountsToAdd
    )

        ### Config Praetor ###
    Try{
        $adminPass = Import-Clixml $adminCredLocation

        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock{
            param(
                $adminPass,
                $localAdminDefault,
                $localAdminCorrect
            )
            $administrator = Get-LocalUser -Name $localAdminDefault -ErrorAction SilentlyContinue
            if($administrator){
                $administrator | Set-LocalUser -Name $localAdminCorrect
            }else{
                $administrator = Get-LocalUser -Name $localAdminCorrect
            }
            $administrator | Set-LocalUser -Password $adminPass.Password -PasswordNeverExpires $true
        } -ArgumentList $adminPass, $localAdminDefault, $localAdminCorrect
    }catch{
        ### Do Nothing ###
    }

    ### Admin groups ### 
    Try{

        $svcAccounts = $accountsToAdd

        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock{
            param (
                $svcAccounts
            )
                $existingGroups = Get-LocalGroup Administrators | Get-LocalGroupMember | Select-Object -ExpandProperty Name
                $groupsToAdd = (Compare-Object -ReferenceObject $svcAccounts -DifferenceObject $existingGroups) | Where-Object {$_.SideIndicator -match "<="}
                if($groupsToAdd){
                    Add-LocalGroupMember -Group "Administrators" -Member $groupsToAdd.InputObject
                }
        } -ArgumentList $svcAccounts
        $critAcc = $true
    }catch{
        $critAcc = $false
    }
$critAcc    
}

Function ConfigNics{
    param(
        [Parameter(Mandatory=$true)]$servername,
        [Parameter(Mandatory=$true)]$user,
        [Parameter(Mandatory=$true)]$dnsList
    )

    Try{
        Invoke-Command -ComputerName $servername -Credential $user -ErrorAction Stop -Scriptblock {
            param(
                $suffixSearchList
            )
            Get-NetAdapter | ForEach-Object { Disable-NetAdapterBinding -InterfaceAlias $_.Name -ComponentID ms_tcpip6 }
            Set-DnsClientGlobalSetting -SuffixSearchList $suffixSearchList
        } -ArgumentList $dnsList
    }catch{
        ### Do Nothing ###
    }
}

Function ConfigIdentity{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$serverName,
        [Parameter(Mandatory=$true)]$user,
        [Parameter(Mandatory=$true)]$description,
        [Parameter(Mandatory=$true)]$time,
        [Parameter(Mandatory=$true)]$powerplan
    )

    try {

    ### set server description ###
        $value = @{Description = "$description"}
        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock{
            param(
                $value
            )
            Set-CimInstance -Query 'Select * From Win32_OperatingSystem' -Property $value -ErrorAction Stop
        } -ArgumentList $value        

    ### set correct timezone ###
        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock{
            param(
                $timezone
            )
            Set-TimeZone -Name $timezone
        } -ArgumentList $time
    
    ### set power plan ###
        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock{
            param(
                $powerplan
            )
            powercfg.exe -SETACTIVE $powerplan
        } -ArgumentList $powerplan

    }catch {
        ### Do Nothing ###
    }

}

Function ConfigDisks{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$serverName,
        [Parameter(Mandatory=$true)]$user,
        [Parameter(Mandatory=$true)]$diskinfo,
        [Parameter(Mandatory=$true)]$osSize
    )

    $serverName
    $diskinfo = $diskinfo | Where-Object {$_.DiskLetter.length -gt 0}
    
    Try{
        ### set dvd rom to letter Z:\ ###
        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock {
            $DVD_Drive = Get-WmiObject win32_volume -EnableAllPrivileges -filter 'DriveType = "5"'    
            $DVD_Drive.DriveLetter = "Z:"    
            $DVD_Drive.Put() | Out-Null            
        }

        ### Expand C: to max size ###
        $cSize = Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock {(Get-Disk | Where-Object {$_.Number -eq 0} | Select-Object -ExpandProperty AllocatedSize)/1gb}
        if($cSize -ne $osSize){
            Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock {
                $size = Get-PartitionSupportedSize -DriveLetter C
                Resize-Partition -DriveLetter C -Size $size.Sizemax
            }
        }
        

        ### initialize, online, format disk partitions ###
        Invoke-Command -ComputerName $serverName -Credential $user -ErrorAction Stop -ScriptBlock {
            param(
                $diskinfo
            )
            Get-Disk | Where-Object {$_.PartitionStyle -match "RAW"} | Initialize-Disk -PartitionStyle GPT -ErrorAction Stop   
            for($i = 0; $i -lt ($diskinfo | Measure-Object).count; $i++){        
                New-Partition -DiskNumber ($i+1) -DriveLetter ($diskinfo[$i].DiskLetter).replace(" ","") -UseMaximumSize -ErrorAction Stop
                Format-Volume -DriveLetter ($diskinfo[$i].DiskLetter).replace(" ","") -NewFileSystemLabel "New Volume" -FileSystem NTFS -AllocationUnitSize $diskinfo[$i].blockSize -ErrorAction Stop   
            }
        } -ArgumentList $diskinfo

    }catch{
        ### Do Nothing ###
    }

}

Function ConfigDesktop{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$serverName,
        [Parameter(Mandatory=$true)]$user,
        [Parameter(Mandatory=$true)]$sla
    )

    if($sla -match "dev|poc"){
        $envSLA = "DEV"
    }elseif($sla -match "ppe"){
        $envSLA = "PPE"
    }elseif($sla -like "prd"){
        $envSLA = "PROD"
    }

        # Define source and destination paths
        $sourcePath = "$appSource\DeskTopInfo"
        $destinationPath = "\\$serverName\C$\Temp"
    
    try {

        # Create a PSDrive for the source server
        New-PSDrive -Name "SourceServer" -PSProvider FileSystem -Root $sourcePath
        # Create a PSDrive for the destination server
        New-PSDrive -Name "DestinationServer" -PSProvider FileSystem -Root $destinationPath -Credential $user

        $drivePath = "DestinationServer:\ServerInstalls\DesktopInfo"
        if(Test-Path $drivePath){
            Remove-Item Test-Path $drivePath -Recurse -Force -ErrorAction Stop
        }

        Copy-Item -path "SourceServer:\" -Destination $drivePath -Recurse -ErrorAction Stop

        # Remove the PSDrives (optional)
        Remove-PSDrive -Name "SourceServer" -Scope Global
        Remove-PSDrive -Name "DestinationServer" -Scope Global
    
        Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock{
            param(
                $envSLA
            )

            if(Test-Path "C:\DesktopInfo"){                
                Remove-Item "C:\DesktopInfo" -Recurse -Force
            }

            Set-Location C:\temp\ServerInstalls\DeskTopInfo -ErrorAction Stop
            & C:\temp\ServerInstalls\DeskTopInfo\INSTALL_BGinfo_$envSLA.bat -ErrorAction Stop | Out-Null 
            Copy-Item C:\temp\ServerInstalls\DeskTopInfo\BGinfo_$envSLA.lnk -Destination "C:\ProgramData\Microsoft\Windows\Start Menu\Programs\StartUp" -Force -Confirm:$false -ErrorAction Stop
        } -ArgumentList $envSLA

    }catch {
        ### Do Nothing ###
    }
}

Function ConfigSCCM{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$serverName,
        [Parameter(Mandatory=$true)]$user

    )
    # Define source and destination paths
    $sourcePath = "$appSource\SCCM"
    $destinationPath = "\\$serverName\C$\Temp"
    
    try {

        # Create a PSDrive for the source server
        New-PSDrive -Name "SourceServer" -PSProvider FileSystem -Root $sourcePath
        # Create a PSDrive for the destination server
        New-PSDrive -Name "DestinationServer" -PSProvider FileSystem -Root $destinationPath -Credential $user

        $drivePath = "DestinationServer:\ServerInstalls\SCCM"
        if(Test-Path $drivePath){
            Remove-Item Test-Path $drivePath -Recurse -Force -ErrorAction Stop
        }

        Copy-Item -path "SourceServer:\" -Destination $drivePath -Recurse -ErrorAction Stop

        # Remove the PSDrives (optional)
        Remove-PSDrive -Name "SourceServer" -Scope Global
        Remove-PSDrive -Name "DestinationServer" -Scope Global
    
        $installerPath = "C:\temp\ServerInstalls\SCCM\ccmsetup.exe"
        Invoke-Command -ComputerName $serverName -Credential $user  -ScriptBlock{    
            param (
                $installerPath
            )        
            # Start the SCCM client installation
            Start-Process -FilePath "cmd.exe" -ArgumentList "/c $installerPath" -Wait
        } -ArgumentList $installerPath
        Start-Sleep 60
    }catch {
        ### Do Nothing ###
    }

}

Function ConfigCrowdStrike{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$serverName,
        [Parameter(Mandatory=$true)]$user
    )
    
    # Define source and destination paths
    $sourcePath = "$appSource\CrowdStrike"
    $destinationPath = "\\$serverName\C$\Temp"
    
    try {

        # Create a PSDrive for the source server
        New-PSDrive -Name "SourceServer" -PSProvider FileSystem -Root $sourcePath
        # Create a PSDrive for the destination server
        New-PSDrive -Name "DestinationServer" -PSProvider FileSystem -Root $destinationPath -Credential $user

        $drivePath = "DestinationServer:\ServerInstalls\CrowdStrike"
        if(Test-Path $drivePath){
            Remove-Item $drivePath -Recurse -Force -ErrorAction Stop
        }

        Copy-Item -path "SourceServer:\" -Destination $drivePath -Recurse -ErrorAction Stop

        # Remove the PSDrives (optional)
        Remove-PSDrive -Name "SourceServer" -Scope Global
        Remove-PSDrive -Name "DestinationServer" -Scope Global
    
        $installerPath = "C:\Temp\ServerInstalls\CrowdStrike\Install_ADS.bat"
        Invoke-Command -ComputerName $serverName -Credential $user  -ScriptBlock{    
            param (
                $installerPath
            )        
            # Start the SCCM client installation
            Start-Process -FilePath "cmd.exe" -ArgumentList "/c $installerPath" -Wait
        } -ArgumentList $installerPath
        Start-Sleep -Seconds 60
    }catch {
        ### Do Nothing ###
    }
}

Function ConfigBackupAgent{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$serverName
    )
    $serverName
    "Install Backup Agent"
}

Function ConfigMonitoringAgent{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$serverName,
        [Parameter(Mandatory=$true)]$user,
        [Parameter(Mandatory=$true)]$mgmtServerFQDN,
        [Parameter(Mandatory=$true)]$mgmtServerGroup,
        [Parameter(Mandatory=$true)]$mgmtServerPort,
        [Parameter(Mandatory=$true)]$osVersion
    )

    # Define source and destination paths
    $sourcePath = "$appSource\SCOM\Agent"
    $destinationPath = "\\$serverName\C$\Temp"
    
    try {

        # Create a PSDrive for the source server
        New-PSDrive -Name "SourceServer" -PSProvider FileSystem -Root $sourcePath
        # Create a PSDrive for the destination server
        New-PSDrive -Name "DestinationServer" -PSProvider FileSystem -Root $destinationPath -Credential $user

        $drivePath = "DestinationServer:\ServerInstalls\SCOM"
        if(Test-Path $drivePath){
            Remove-Item Test-Path $drivePath -Recurse -Force -ErrorAction Stop
        }

        Copy-Item -path "SourceServer:\" -Destination $drivePath -Recurse -ErrorAction Stop

        # Remove the PSDrives (optional)
        Remove-PSDrive -Name "SourceServer" -Scope Global
        Remove-PSDrive -Name "DestinationServer" -Scope Global

        # Specify the path to the SCOM client installer MSI file
        $installerPath = "C:\temp\ServerInstalls\SCOM\AMD64\MOMAgent.msi"
        # Specify the SCOM management server and other properties
        $msFQDN = $mgmtServerFQDN
        $msGName = $mgmtServerGroup
        $msPort = $mgmtServerPort
        $additionalProperties = "/qn USE_SETTINGS_FROM_AD=0 USE_MANUALLY_SPECIFIED_SETTINGS=1 MANAGEMENT_GROUP=$msGName MANAGEMENT_SERVER_DNS=$msFQDN SECURE_PORT=$msPort ACTIONS_USE_COMPUTER_ACCOUNT=1 AcceptEndUserLicenseAgreement=1"
        if($osVersion -match "2019"){
            $removeKey = $true
        }else{
            $removeKey = $false
        }

        Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock{
            param (
                $installerPath,
                $additionalProperties,
                $removeKey
            )
            if($removeKey){
                $registryKeyPath = "HKLM:\SYSTEM\CurrentControlSet\Services\EventLog\Security"
                # Check if the registry key exists before attempting to delete it
                if (Test-Path -Path $registryKeyPath) {
                    # Remove the registry key and its subkeys recursively
                    Remove-ItemProperty -Path $registryKeyPath -Name CustomSD -Force -Verbose -ErrorAction SilentlyContinue
                }
            }
            # Install SCOM client with specified properties
            Start-Process -FilePath "msiexec.exe" -ArgumentList "/i $installerPath $additionalProperties" -Wait
        } -ArgumentList $installerPath, $additionalProperties, $removeKey

        Start-Sleep -Seconds 60
    }catch {
        ### Do Nothing ###
    }
}

Function ServerReboot{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$serverName,
        [Parameter(Mandatory=$true)]$user
    )

    try {
        "Sleep 5 minutes while waiting for applications to finish installing"
        Start-Sleep -Seconds 300
        Restart-Computer -ComputerName $serverName -Credential $user -Force -ErrorAction Stop
        Start-Sleep -Seconds 60
        $restartSuccess = $true
    }catch {
        $restartSuccess = $false
    }
$restartSuccess
}

Function BCXLinuxOsConfigs{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$buildSpecs
    )

    $cliCreds = Import-Clixml -Path $cliCredsPath
    $vCenterSuccess = vCenter_Connection -vCenterName $buildSpecs.vc_node -credentials $cliCreds
    $vmName = $buildSpecs.vm_name
    $client = $buildSpecs.business_unit
    if($buildSpecs.environment -match "prd"){
        $env = "prod"
    }else{
        $env = "nonprod"
    }

    if($buildSpecs.business_unit -match "Santam"){
        $client = "santam"
    }else{
        $client = "nonsantam"
    }

    if($vCenterSuccess){   
        try{
            if($buildSpecs.os_version -match "RHEL8"){                
                $command = "wget -O- --no-check-certificate https://linuxmanager.sanlam.co.za/cloud/deployment/PostDeployConfig8.sh | /bin/bash -s -- -c $client -s $env -d data -m mail -p Y"
                $command.Replace("&client&", $client)
                $command.Replace("&env&", $env)
                Invoke-VMScript -VM $vmName -ScriptText $command -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null    
            }elseif($buildSpecs.os_version -match "RHEL9"){
                $command = "wget -O- --no-check-certificate https://linuxmanager.sanlam.co.za/cloud/deployment/PostDeployConfig9.sh | /bin/bash -s -- -c $client -s $env -d data -m mail -p Y"
                $command.Replace("&client&", $client)
                $command.Replace("&env&", $env)
                Invoke-VMScript -VM $vmName -ScriptText $command -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null              
            }
            "Sleep while waiting for OS Config script to run"
            Start-Sleep -Seconds 300
            $completed = $true
            $message = "OS Config successful"
        }catch{
            $completed = $false
            $message = "Failed to execute OS Config script, reason: " + ($Error[0].exception).Message
        }
    }else{
        $completed = $false
        $message = "Failed to connect to vCenter $($apiInputData.vc_node) using account $($cliCreds.Username)"
    }

$task = "" | Select-Object Succeeded, Message
$task.Succeeded = $completed
$task.Message = $message
$task
}

Function SGTLinuxOsConfigs{
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]$buildSpecs
    )

    $cliCreds = Import-Clixml -Path $cliCredsPath
    $vCenterSuccess = vCenter_Connection -vCenterName $buildSpecs.vc_node -credentials $cliCreds
    $vmName = $buildSpecs.vm_name

    if($vCenterSuccess){    
        try{
            Invoke-VMScript -VM $vmName -ScriptText "wget --no-check-certificate -qO - https://***********/cloud/deployment/PostDeployConfig8.sh|bash" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null    
            Start-Sleep -Seconds 300
            "Sleep while waiting for OS Config script to run"
            $completed = $true
            $message = "OS Config successful"
        }catch{
            $completed = $false
            $message = "Failed to execute OS Config script, reason: " + ($Error[0].exception).Message
        }
    }else{
        $completed = $false
        $message = "Failed to connect to vCenter $($apiInputData.vc_node) using account $($cliCreds.Username)"
    }

$task = "" | Select-Object Succeeded, Message
$task.Succeeded = $completed
$task.Message = $message
$task
}

Function vCenter_Connection{
    param(
        [Parameter(Mandatory=$true)]$vCenterName,
        [Parameter(Mandatory=$true)][System.Management.Automation.PSCredential]$credentials
    )

    Try{
        Connect-VIServer -Server $vCenterName -Credential $credentials
        $vCenterConnected = $true
    }catch{
        $vCenterConnected = $false
    }

    $vCenterConnected
}

Function StartADDMScan{
    param(
        $specInfo
    )

    try{
        $inADDM = get_addm -vm $specInfo.vm_name -ErrorAction Stop
        if($inADDM.State -notmatch "Finished"){
            try{
                $result = set_addm -vmIP $specInfo.ip_address -JobID $specInfo.request_jobid -waitForScan $false -ErrorAction Stop
            }catch{
                $result = $false
            }
        }else{
            $result = $true
        }
    }catch{
        $result = $false
    }

    $result
}

Config_Management