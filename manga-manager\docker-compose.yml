version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: manga-manager-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ./uploads:/app/uploads
    environment:
      - NODE_ENV=production
      - PORT=8080
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=manga_manager
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - SESSION_SECRET=manga-manager-secret
      - GOOGLE_CLIENT_ID=your-google-client-id
      - GOOGLE_CLIENT_SECRET=your-google-client-secret
      - GOOGLE_CALLBACK_URL=http://localhost:8080/api/auth/google/callback
    depends_on:
      - db

  db:
    image: postgres:14-alpine
    container_name: manga-manager-db
    restart: unless-stopped
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=manga_manager
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres

volumes:
  postgres_data: