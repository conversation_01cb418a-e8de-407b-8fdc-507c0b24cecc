// Reader Module
const Reader = (() => {
  // Private variables
  let currentManga = null;
  let currentChapter = 1;
  let currentPage = 1;
  let totalPages = 0;
  let pages = [];
  let chapters = [];
  
  // Initialize
  const init = () => {
    setupEventListeners();
  };
  
  // Set up event listeners
  const setupEventListeners = () => {
    // Navigation buttons
    const prevPageBtn = document.getElementById('prev-page-btn');
    const nextPageBtn = document.getElementById('next-page-btn');
    const firstPageBtn = document.getElementById('first-page-btn');
    const lastPageBtn = document.getElementById('last-page-btn');
    
    if (prevPageBtn) prevPageBtn.addEventListener('click', prevPage);
    if (nextPageBtn) nextPageBtn.addEventListener('click', nextPage);
    if (firstPageBtn) firstPageBtn.addEventListener('click', goToFirstPage);
    if (lastPageBtn) lastPageBtn.addEventListener('click', goToLastPage);
    
    // Bookmark button
    const bookmarkPageBtn = document.getElementById('bookmark-page-btn');
    if (bookmarkPageBtn) {
      bookmarkPageBtn.addEventListener('click', bookmarkCurrentPage);
    }
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      // Only handle keyboard events when reader is open
      const readerModal = document.getElementById('readerModal');
      if (!readerModal || !readerModal.classList.contains('show')) {
        return;
      }
      
      switch (e.key) {
        case 'ArrowLeft':
          prevPage();
          break;
        case 'ArrowRight':
          nextPage();
          break;
        case 'Home':
          goToFirstPage();
          break;
        case 'End':
          goToLastPage();
          break;
      }
    });
  };
  
  // Load manga
  const loadManga = async (mangaId, chapter = 1, page = 1) => {
    try {
      if (!Auth.isAuthenticated) {
        UI.showAlert('Please login to read manga', 'warning');
        return false;
      }
      
      // Show loading
      document.getElementById('reader-image').src = 'images/loading.gif';
      document.getElementById('page-indicator').textContent = 'Loading...';
      
      // Fetch manga data
      const response = await fetch(`/api/manga/${mangaId}`, {
        headers: {
          'Authorization': `Bearer ${Auth.token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to load manga');
      }
      
      const data = await response.json();
      currentManga = data.manga;
      
      // Set title
      document.getElementById('reading-title').textContent = currentManga.title;
      
      // Load chapters
      chapters = currentManga.chapters || [];
      
      // Update chapter selector
      updateChapterSelector();
      
      // Load pages for the selected chapter
      await loadChapter(chapter, page);
      
      return true;
    } catch (error) {
      console.error('Load manga error:', error);
      UI.showAlert('Failed to load manga', 'danger');
      return false;
    }
  };
  
  // Load chapter
  const loadChapter = async (chapter = 1, page = 1) => {
    try {
      if (!currentManga) {
        return false;
      }
      
      // Show loading
      document.getElementById('reader-image').src = 'images/loading.gif';
      document.getElementById('page-indicator').textContent = 'Loading...';
      
      // Fetch chapter data
      const response = await fetch(`/api/manga/${currentManga.id}/chapters/${chapter}`, {
        headers: {
          'Authorization': `Bearer ${Auth.token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to load chapter');
      }
      
      const data = await response.json();
      pages = data.pages || [];
      totalPages = pages.length;
      currentChapter = chapter;
      currentPage = Math.min(page, totalPages);
      
      // Load current page
      loadPage(currentPage);
      
      return true;
    } catch (error) {
      console.error('Load chapter error:', error);
      UI.showAlert('Failed to load chapter', 'danger');
      return false;
    }
  };
  
  // Load page
  const loadPage = (page) => {
    if (!pages || !pages.length || page < 1 || page > totalPages) {
      return false;
    }
    
    currentPage = page;
    
    // Update image
    const readerImage = document.getElementById('reader-image');
    readerImage.src = pages[currentPage - 1].url;
    
    // Update page indicator
    document.getElementById('page-indicator').textContent = `Page ${currentPage} of ${totalPages}`;
    
    // Update reading progress
    updateReadingProgress();
    
    return true;
  };
  
  // Update chapter selector
  const updateChapterSelector = () => {
    const chapterSelector = document.getElementById('chapter-selector');
    
    if (!chapterSelector) return;
    
    // Clear chapter selector
    chapterSelector.innerHTML = '';
    
    // Add chapters
    chapters.forEach((chapter, index) => {
      const li = document.createElement('li');
      const a = document.createElement('a');
      a.className = 'dropdown-item';
      a.href = '#';
      a.textContent = `Chapter ${index + 1}`;
      a.addEventListener('click', (e) => {
        e.preventDefault();
        loadChapter(index + 1);
      });
      
      if (index + 1 === currentChapter) {
        a.classList.add('active');
      }
      
      li.appendChild(a);
      chapterSelector.appendChild(li);
    });
  };
  
  // Update reading progress
  const updateReadingProgress = async () => {
    try {
      if (!Auth.isAuthenticated || !currentManga) {
        return;
      }
      
      // Update reading progress on the server
      await fetch('/api/manga/progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${Auth.token}`
        },
        body: JSON.stringify({
          mangaId: currentManga.id,
          chapter: currentChapter,
          page: currentPage
        })
      });
    } catch (error) {
      console.error('Update reading progress error:', error);
    }
  };
  
  // Navigation functions
  const prevPage = () => {
    if (currentPage > 1) {
      loadPage(currentPage - 1);
    } else if (currentChapter > 1) {
      // Go to the last page of the previous chapter
      loadChapter(currentChapter - 1).then(() => {
        loadPage(totalPages);
      });
    }
  };
  
  const nextPage = () => {
    if (currentPage < totalPages) {
      loadPage(currentPage + 1);
    } else if (currentChapter < chapters.length) {
      // Go to the first page of the next chapter
      loadChapter(currentChapter + 1);
    }
  };
  
  const goToFirstPage = () => {
    loadPage(1);
  };
  
  const goToLastPage = () => {
    loadPage(totalPages);
  };
  
  // Bookmark current page
  const bookmarkCurrentPage = async () => {
    try {
      if (!Auth.isAuthenticated || !currentManga) {
        UI.showAlert('Please login to bookmark pages', 'warning');
        return;
      }
      
      // Create or update bookmark
      const response = await fetch('/api/bookmarks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${Auth.token}`
        },
        body: JSON.stringify({
          mangaId: currentManga.id,
          chapter: currentChapter,
          page: currentPage
        })
      });
      
      if (response.ok) {
        UI.showAlert('Page bookmarked successfully', 'success');
      } else {
        throw new Error('Failed to bookmark page');
      }
    } catch (error) {
      console.error('Bookmark error:', error);
      UI.showAlert('Failed to bookmark page', 'danger');
    }
  };
  
  // Open reader
  const openReader = async (mangaId, chapter = 1, page = 1) => {
    const readerModal = new bootstrap.Modal(document.getElementById('readerModal'));
    
    // Load manga
    const success = await loadManga(mangaId, chapter, page);
    
    if (success) {
      readerModal.show();
    }
  };
  
  // Public methods and properties
  return {
    init,
    openReader,
    loadManga,
    loadChapter,
    loadPage,
    prevPage,
    nextPage,
    goToFirstPage,
    goToLastPage,
    bookmarkCurrentPage
  };
})();