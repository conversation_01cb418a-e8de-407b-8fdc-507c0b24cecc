# Details of the deployed VMs from actual vsphere resources
output "vm_details" {
  description = "Details of the deployed virtual machines."
  value = {
    for vm_key, vm in vsphere_virtual_machine.vm : vm_key => {
      "vm_name"     = vm.name
      "uuid"        = vm.uuid
      "power_state" = vm.power_state
      # memory is in MB
      "memory"   = vm.memory
      "num_cpus" = vm.num_cpus
      "guest_id" = vm.guest_id
    }
  }
}

# Output datacenter information for each jobspec
output "datacenter_details" {
  description = "Datacenter information for each jobspec."
  value = {
    for js_id, dc in data.vsphere_datacenter.dc : js_id => {
      "name" = dc.name
      "id"   = dc.id
    }
  }
}

# Output cluster information for each jobspec
output "cluster_details" {
  description = "Cluster information for each jobspec."
  value = {
    for js_id, cluster in data.vsphere_compute_cluster.cluster : js_id => {
      "name"             = cluster.name
      "id"               = cluster.id
      "resource_pool_id" = cluster.resource_pool_id
    }
  }
}

# Output datastore information
output "datastore_details" {
  description = "Datastore information for each jobspec."
  value = {
    for ds_key, ds in data.vsphere_datastore.datastores : ds_key => {
      "name" = ds.name
      "id"   = ds.id
    }
  }
}

# Deployment summary from the JSON file
output "deployment_summary_file" {
  value = try(local_file.deployment_summary.filename, null)
}

# Summary of all jobspecs processed
output "jobspecs_summary" {
  description = "Summary of all processed jobspecs."
  value = {
    total_jobspecs = length(local.jobspecs)
    total_vms      = length(local.vm_deployments)
    jobspecs = {
      for js in local.jobspecs : js.jobspec_id => {
        vcenter_endpoint = js.vcenter_endpoint
        datacenter       = js.datacenter
        cluster          = js.cluster
        vm_count         = js.vm_configuration.vm_count
        total_disks      = try(length(js.vm_configuration.disk_configuration), 0)
      }
    }
  }
}

# Precompute total vCPUs and memory for efficiency
locals {
  total_vcpus  = sum([for vm in values(vsphere_virtual_machine.vm) : vm.num_cpus])
  total_memory = sum([for vm in values(vsphere_virtual_machine.vm) : vm.memory])
}

output "resource_totals" {
  description = "Total resources consumed by the deployment."
  value = {
    total_vcpus = local.total_vcpus
    total_memory = local.total_memory
  }
}