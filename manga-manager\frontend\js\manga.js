// Manga Module
const Manga = (() => {
  // Private variables
  let searchResults = [];
  let library = [];
  let categories = [];
  let bookmarks = [];
  
  // Initialize
  const init = () => {
    setupEventListeners();
    
    // If authenticated, load user data
    if (Auth.isAuthenticated) {
      loadUserData();
    }
  };
  
  // Set up event listeners
  const setupEventListeners = () => {
    // Search
    const searchButton = document.getElementById('search-button');
    const searchInput = document.getElementById('search-input');
    
    if (searchButton && searchInput) {
      searchButton.addEventListener('click', () => {
        const query = searchInput.value.trim();
        if (query) {
          searchManga(query);
        }
      });
      
      searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          const query = searchInput.value.trim();
          if (query) {
            searchManga(query);
          }
        }
      });
    }
    
    // Upload
    const uploadButton = document.getElementById('upload-button');
    if (uploadButton) {
      uploadButton.addEventListener('click', uploadManga);
    }
    
    // Manage categories
    const manageCategoriesBtn = document.getElementById('manage-categories-btn');
    if (manageCategoriesBtn) {
      manageCategoriesBtn.addEventListener('click', () => {
        // In a real app, you would show a modal to manage categories
        UI.showAlert('Category management is not implemented in this demo', 'info');
      });
    }
  };
  
  // Load user data (library, categories, bookmarks)
  const loadUserData = async () => {
    try {
      // Load categories
      const categoriesResponse = await fetch('/api/categories', {
        headers: {
          'Authorization': `Bearer ${Auth.token}`
        }
      });
      
      if (categoriesResponse.ok) {
        const data = await categoriesResponse.json();
        categories = data.categories;
        updateCategorySelects();
      }
      
      // Load library
      const libraryResponse = await fetch('/api/manga/library', {
        headers: {
          'Authorization': `Bearer ${Auth.token}`
        }
      });
      
      if (libraryResponse.ok) {
        const data = await libraryResponse.json();
        library = data.manga;
        updateLibraryView();
      }
      
      // Load bookmarks
      const bookmarksResponse = await fetch('/api/bookmarks', {
        headers: {
          'Authorization': `Bearer ${Auth.token}`
        }
      });
      
      if (bookmarksResponse.ok) {
        const data = await bookmarksResponse.json();
        bookmarks = data.bookmarks;
        updateBookmarksView();
      }
      
      // Update dashboard
      updateDashboard();
    } catch (error) {
      console.error('Error loading user data:', error);
      UI.showAlert('Failed to load your data', 'danger');
    }
  };
  
  // Update category selects
  const updateCategorySelects = () => {
    const categoryFilter = document.getElementById('category-filter');
    const mangaCategory = document.getElementById('manga-category');
    const dashboardCategories = document.getElementById('dashboard-categories');
    
    if (categories.length === 0) {
      // No categories yet
      return;
    }
    
    // Update category filter in search
    if (categoryFilter) {
      categoryFilter.innerHTML = '<option value="">All Categories</option>';
      categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        categoryFilter.appendChild(option);
      });
    }
    
    // Update category select in upload form
    if (mangaCategory) {
      mangaCategory.innerHTML = '<option value="">Select Category</option>';
      categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        mangaCategory.appendChild(option);
      });
    }
    
    // Update dashboard categories select
    if (dashboardCategories) {
      dashboardCategories.innerHTML = '';
      categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        // In a real app, you would check if the category is selected for the dashboard
        dashboardCategories.appendChild(option);
      });
    }
  };
  
  // Update library view
  const updateLibraryView = () => {
    const libraryContent = document.getElementById('library-content');
    
    if (!libraryContent) return;
    
    if (library.length === 0) {
      libraryContent.innerHTML = `
        <div class="col-12">
          <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> Your library is empty. Search and download manga to add to your library.
          </div>
        </div>
      `;
      return;
    }
    
    // Clear library content
    libraryContent.innerHTML = '';
    
    // Add manga cards
    library.forEach(manga => {
      const col = document.createElement('div');
      col.className = 'col-md-6 col-lg-4 col-xl-3 mb-4';
      
      col.innerHTML = `
        <div class="card manga-card ${manga.isExplicit ? 'explicit' : ''}">
          <img src="${manga.coverImage || 'images/placeholder.png'}" class="card-img-top" alt="${manga.title}">
          <div class="card-body">
            <h5 class="card-title">${manga.title}</h5>
            <p class="card-text">
              <span class="badge bg-primary">${getCategoryName(manga.categoryId)}</span>
              ${manga.isExplicit ? '<span class="badge bg-danger ms-1">Explicit</span>' : ''}
            </p>
            <div class="d-flex justify-content-between">
              <button class="btn btn-sm btn-primary" data-manga-id="${manga.id}">Read</button>
              <button class="btn btn-sm btn-outline-secondary" data-manga-id="${manga.id}">Details</button>
            </div>
          </div>
        </div>
      `;
      
      libraryContent.appendChild(col);
      
      // Add event listeners
      const readBtn = col.querySelector('.btn-primary');
      const detailsBtn = col.querySelector('.btn-outline-secondary');
      
      readBtn.addEventListener('click', () => openReader(manga.id));
      detailsBtn.addEventListener('click', () => openMangaDetails(manga.id));
    });
  };
  
  // Update bookmarks view
  const updateBookmarksView = () => {
    const bookmarksContent = document.getElementById('bookmarks-content');
    
    if (!bookmarksContent) return;
    
    if (bookmarks.length === 0) {
      bookmarksContent.innerHTML = `
        <div class="col-12">
          <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> You don't have any bookmarks yet.
          </div>
        </div>
      `;
      return;
    }
    
    // Clear bookmarks content
    bookmarksContent.innerHTML = '';
    
    // Add bookmark cards
    bookmarks.forEach(bookmark => {
      const manga = library.find(m => m.id === bookmark.mangaId);
      
      if (!manga) return;
      
      const col = document.createElement('div');
      col.className = 'col-md-6 col-lg-4 col-xl-3 mb-4';
      
      col.innerHTML = `
        <div class="card manga-card ${manga.isExplicit ? 'explicit' : ''}">
          <img src="${manga.coverImage || 'images/placeholder.png'}" class="card-img-top" alt="${manga.title}">
          <div class="card-body">
            <h5 class="card-title">${manga.title}</h5>
            <p class="card-text">
              <span class="badge bg-primary">${getCategoryName(manga.categoryId)}</span>
              ${manga.isExplicit ? '<span class="badge bg-danger ms-1">Explicit</span>' : ''}
            </p>
            <p class="card-text">
              <small class="text-muted">
                Chapter ${bookmark.chapter}, Page ${bookmark.page}
              </small>
            </p>
            <div class="d-flex justify-content-between">
              <button class="btn btn-sm btn-primary" data-bookmark-id="${bookmark.id}">Continue</button>
              <button class="btn btn-sm btn-outline-danger" data-bookmark-id="${bookmark.id}">Remove</button>
            </div>
          </div>
        </div>
      `;
      
      bookmarksContent.appendChild(col);
      
      // Add event listeners
      const continueBtn = col.querySelector('.btn-primary');
      const removeBtn = col.querySelector('.btn-outline-danger');
      
      continueBtn.addEventListener('click', () => continueReading(bookmark.id));
      removeBtn.addEventListener('click', () => removeBookmark(bookmark.id));
    });
  };
  
  // Update dashboard
  const updateDashboard = () => {
    const dashboardContent = document.getElementById('dashboard-content');
    
    if (!dashboardContent) return;
    
    // Update stats
    dashboardContent.querySelector('.col-xl-3:nth-child(1) .h5').textContent = `${library.length} Manga`;
    dashboardContent.querySelector('.col-xl-3:nth-child(2) .h5').textContent = `${bookmarks.length}`;
    dashboardContent.querySelector('.col-xl-3:nth-child(3) .h5').textContent = `${categories.length}`;
    
    // Calculate reading progress
    let totalChapters = 0;
    let readChapters = 0;
    
    library.forEach(manga => {
      totalChapters += manga.chaptersCount || 0;
      readChapters += manga.readChaptersCount || 0;
    });
    
    const progressPercentage = totalChapters > 0 ? Math.round((readChapters / totalChapters) * 100) : 0;
    dashboardContent.querySelector('.col-xl-3:nth-child(4) .h5').textContent = `${progressPercentage}%`;
    
    // Update recently read
    const recentlyReadContainer = dashboardContent.querySelector('.card-body');
    
    if (recentlyReadContainer) {
      if (bookmarks.length === 0) {
        recentlyReadContainer.innerHTML = `
          <div class="alert alert-info mb-0">
            <i class="bi bi-info-circle"></i> You haven't read any manga yet.
          </div>
        `;
      } else {
        // Sort bookmarks by last updated
        const sortedBookmarks = [...bookmarks].sort((a, b) => 
          new Date(b.updatedAt) - new Date(a.updatedAt)
        ).slice(0, 5); // Get top 5
        
        const recentlyReadHtml = sortedBookmarks.map(bookmark => {
          const manga = library.find(m => m.id === bookmark.mangaId);
          
          if (!manga) return '';
          
          return `
            <div class="d-flex align-items-center mb-3">
              <img src="${manga.coverImage || 'images/placeholder.png'}" alt="${manga.title}" class="me-3" style="width: 50px; height: 70px; object-fit: cover;">
              <div>
                <h6 class="mb-0">${manga.title}</h6>
                <small class="text-muted">
                  Chapter ${bookmark.chapter}, Page ${bookmark.page}
                </small>
                <div>
                  <button class="btn btn-sm btn-primary mt-1" data-bookmark-id="${bookmark.id}">Continue</button>
                </div>
              </div>
            </div>
          `;
        }).join('');
        
        recentlyReadContainer.innerHTML = recentlyReadHtml || `
          <div class="alert alert-info mb-0">
            <i class="bi bi-info-circle"></i> You haven't read any manga yet.
          </div>
        `;
        
        // Add event listeners
        recentlyReadContainer.querySelectorAll('.btn-primary').forEach(btn => {
          const bookmarkId = btn.getAttribute('data-bookmark-id');
          btn.addEventListener('click', () => continueReading(bookmarkId));
        });
      }
    }
  };
  
  // Search manga
  const searchManga = async (query) => {
    try {
      // Show loading
      const searchResults = document.getElementById('search-results');
      searchResults.innerHTML = `
        <div class="col-12 text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2">Searching for "${query}"...</p>
        </div>
      `;
      
      // Get search parameters
      const categoryFilter = document.getElementById('category-filter');
      const sortOption = document.getElementById('sort-option');
      const includeExplicit = document.getElementById('include-explicit');
      
      const params = new URLSearchParams({
        q: query,
        sort: sortOption ? sortOption.value : 'relevance',
        includeExplicit: includeExplicit && includeExplicit.checked ? 'true' : 'false'
      });
      
      if (categoryFilter && categoryFilter.value) {
        params.append('category', categoryFilter.value);
      }
      
      // Fetch search results
      const response = await fetch(`/api/manga/search?${params.toString()}`, {
        headers: Auth.isAuthenticated ? {
          'Authorization': `Bearer ${Auth.token}`
        } : {}
      });
      
      if (response.ok) {
        const data = await response.json();
        searchResults = data.results;
        
        // Update search results view
        updateSearchResults();
      } else {
        throw new Error('Search failed');
      }
    } catch (error) {
      console.error('Search error:', error);
      UI.showAlert('Failed to search manga', 'danger');
      
      // Clear search results
      const searchResults = document.getElementById('search-results');
      searchResults.innerHTML = `
        <div class="col-12">
          <div class="alert alert-danger">
            <i class="bi bi-exclamation-triangle"></i> Failed to search manga. Please try again later.
          </div>
        </div>
      `;
    }
  };
  
  // Update search results view
  const updateSearchResults = () => {
    const searchResultsContainer = document.getElementById('search-results');
    const searchPagination = document.getElementById('search-pagination');
    
    if (!searchResultsContainer) return;
    
    if (searchResults.length === 0) {
      searchResultsContainer.innerHTML = `
        <div class="col-12">
          <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> No results found. Try a different search term.
          </div>
        </div>
      `;
      
      if (searchPagination) {
        searchPagination.innerHTML = '';
      }
      
      return;
    }
    
    // Clear search results
    searchResultsContainer.innerHTML = '';
    
    // Add manga cards
    searchResults.forEach(manga => {
      const col = document.createElement('div');
      col.className = 'col';
      
      col.innerHTML = `
        <div class="card h-100 manga-card ${manga.isExplicit ? 'explicit' : ''}">
          <img src="${manga.coverImage || 'images/placeholder.png'}" class="card-img-top" alt="${manga.title}">
          <div class="card-body">
            <h5 class="card-title">${manga.title}</h5>
            <p class="card-text">
              <span class="badge bg-primary">${manga.category}</span>
              ${manga.isExplicit ? '<span class="badge bg-danger ms-1">Explicit</span>' : ''}
            </p>
            <div class="d-flex align-items-center mb-2">
              <div class="me-2">Accuracy:</div>
              <div class="progress flex-grow-1" style="height: 10px;">
                <div class="progress-bar" role="progressbar" style="width: ${manga.accuracy}%;" aria-valuenow="${manga.accuracy}" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
              <div class="ms-2">${manga.accuracy}%</div>
            </div>
            <div class="d-flex justify-content-between">
              <button class="btn btn-sm btn-primary" data-manga-id="${manga.id}">Download</button>
              <button class="btn btn-sm btn-outline-secondary" data-manga-id="${manga.id}">Details</button>
            </div>
          </div>
        </div>
      `;
      
      searchResultsContainer.appendChild(col);
      
      // Add event listeners
      const downloadBtn = col.querySelector('.btn-primary');
      const detailsBtn = col.querySelector('.btn-outline-secondary');
      
      downloadBtn.addEventListener('click', () => downloadManga(manga.id));
      detailsBtn.addEventListener('click', () => openMangaDetails(manga.id));
    });
    
    // Update pagination
    if (searchPagination) {
      // In a real app, you would implement pagination
      searchPagination.innerHTML = '';
    }
  };
  
  // Download manga
  const downloadManga = async (mangaId) => {
    try {
      if (!Auth.isAuthenticated) {
        UI.showAlert('Please login to download manga', 'warning');
        return;
      }
      
      UI.showAlert('Downloading manga...', 'info');
      
      const response = await fetch(`/api/manga/${mangaId}/download`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${Auth.token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        
        // Add to library
        library.push(data.manga);
        
        // Update library view
        updateLibraryView();
        
        // Update dashboard
        updateDashboard();
        
        UI.showAlert('Manga downloaded successfully', 'success');
      } else {
        throw new Error('Download failed');
      }
    } catch (error) {
      console.error('Download error:', error);
      UI.showAlert('Failed to download manga', 'danger');
    }
  };
  
  // Upload manga
  const uploadManga = async () => {
    try {
      if (!Auth.isAuthenticated) {
        UI.showAlert('Please login to upload manga', 'warning');
        return;
      }
      
      const mangaFiles = document.getElementById('manga-files');
      const mangaTitle = document.getElementById('manga-title');
      const mangaCategory = document.getElementById('manga-category');
      const isExplicit = document.getElementById('is-explicit');
      
      if (!mangaFiles || !mangaFiles.files.length) {
        UI.showAlert('Please select files to upload', 'warning');
        return;
      }
      
      if (!mangaTitle || !mangaTitle.value.trim()) {
        UI.showAlert('Please enter a title', 'warning');
        return;
      }
      
      if (!mangaCategory || !mangaCategory.value) {
        UI.showAlert('Please select a category', 'warning');
        return;
      }
      
      UI.showAlert('Uploading manga...', 'info');
      
      const formData = new FormData();
      
      // Add files
      for (let i = 0; i < mangaFiles.files.length; i++) {
        formData.append('files', mangaFiles.files[i]);
      }
      
      // Add metadata
      formData.append('title', mangaTitle.value.trim());
      formData.append('categoryId', mangaCategory.value);
      formData.append('isExplicit', isExplicit && isExplicit.checked ? 'true' : 'false');
      
      const response = await fetch('/api/manga/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${Auth.token}`
        },
        body: formData
      });
      
      if (response.ok) {
        const data = await response.json();
        
        // Add to library
        library.push(data.manga);
        
        // Update library view
        updateLibraryView();
        
        // Update dashboard
        updateDashboard();
        
        // Reset form
        mangaFiles.value = '';
        mangaTitle.value = '';
        mangaCategory.value = '';
        if (isExplicit) isExplicit.checked = false;
        
        UI.showAlert('Manga uploaded successfully', 'success');
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      UI.showAlert('Failed to upload manga', 'danger');
    }
  };
  
  // Open manga details
  const openMangaDetails = (mangaId) => {
    // Find manga
    const manga = searchResults.find(m => m.id === mangaId) || 
                  library.find(m => m.id === mangaId);
    
    if (!manga) {
      UI.showAlert('Manga not found', 'danger');
      return;
    }
    
    // Update modal
    document.getElementById('manga-title-detail').textContent = manga.title;
    document.getElementById('manga-cover').src = manga.coverImage || 'images/placeholder.png';
    document.getElementById('manga-category-badge').textContent = manga.category || getCategoryName(manga.categoryId);
    
    if (manga.isExplicit) {
      document.getElementById('manga-explicit-badge').classList.remove('d-none');
    } else {
      document.getElementById('manga-explicit-badge').classList.add('d-none');
    }
    
    document.getElementById('manga-chapters-count').textContent = manga.chaptersCount || 'Unknown';
    document.getElementById('manga-added-date').textContent = manga.createdAt ? new Date(manga.createdAt).toLocaleDateString() : 'Unknown';
    document.getElementById('manga-description').textContent = manga.description || 'No description available.';
    
    // Last read
    const bookmark = bookmarks.find(b => b.mangaId === manga.id);
    if (bookmark) {
      document.getElementById('manga-last-read').textContent = `Chapter ${bookmark.chapter}, Page ${bookmark.page}`;
    } else {
      document.getElementById('manga-last-read').textContent = 'Never';
    }
    
    // Update buttons
    const readBtn = document.getElementById('read-manga-btn');
    const downloadBtn = document.getElementById('download-manga-btn');
    const bookmarkBtn = document.getElementById('bookmark-manga-btn');
    
    readBtn.setAttribute('data-manga-id', manga.id);
    downloadBtn.setAttribute('data-manga-id', manga.id);
    bookmarkBtn.setAttribute('data-manga-id', manga.id);
    
    // Show/hide download button
    if (library.some(m => m.id === manga.id)) {
      downloadBtn.classList.add('d-none');
    } else {
      downloadBtn.classList.remove('d-none');
    }
    
    // Add event listeners
    readBtn.onclick = () => {
      const mangaDetailsModal = bootstrap.Modal.getInstance(document.getElementById('mangaDetailsModal'));
      mangaDetailsModal.hide();
      openReader(manga.id);
    };
    
    downloadBtn.onclick = () => {
      const mangaDetailsModal = bootstrap.Modal.getInstance(document.getElementById('mangaDetailsModal'));
      mangaDetailsModal.hide();
      downloadManga(manga.id);
    };
    
    bookmarkBtn.onclick = () => {
      if (!Auth.isAuthenticated) {
        UI.showAlert('Please login to bookmark manga', 'warning');
        return;
      }
      
      // In a real app, you would add a bookmark
      UI.showAlert('Bookmark functionality is not implemented in this demo', 'info');
    };
    
    // Show modal
    const mangaDetailsModal = new bootstrap.Modal(document.getElementById('mangaDetailsModal'));
    mangaDetailsModal.show();
  };
  
  // Open manga reader
  const openReader = (mangaId) => {
    // In a real app, you would load manga data and open the reader
    UI.showAlert('Reader functionality is not implemented in this demo', 'info');
  };
  
  // Continue reading from bookmark
  const continueReading = (bookmarkId) => {
    // Find bookmark
    const bookmark = bookmarks.find(b => b.id === bookmarkId);
    
    if (!bookmark) {
      UI.showAlert('Bookmark not found', 'danger');
      return;
    }
    
    // In a real app, you would open the reader at the bookmarked page
    UI.showAlert('Reader functionality is not implemented in this demo', 'info');
  };
  
  // Remove bookmark
  const removeBookmark = async (bookmarkId) => {
    try {
      if (!Auth.isAuthenticated) {
        UI.showAlert('Please login to manage bookmarks', 'warning');
        return;
      }
      
      const response = await fetch(`/api/bookmarks/${bookmarkId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${Auth.token}`
        }
      });
      
      if (response.ok) {
        // Remove from bookmarks array
        bookmarks = bookmarks.filter(b => b.id !== bookmarkId);
        
        // Update bookmarks view
        updateBookmarksView();
        
        // Update dashboard
        updateDashboard();
        
        UI.showAlert('Bookmark removed successfully', 'success');
      } else {
        throw new Error('Failed to remove bookmark');
      }
    } catch (error) {
      console.error('Remove bookmark error:', error);
      UI.showAlert('Failed to remove bookmark', 'danger');
    }
  };
  
  // Helper function to get category name by ID
  const getCategoryName = (categoryId) => {
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : 'Unknown';
  };
  
  // Public methods and properties
  return {
    init,
    searchManga,
    downloadManga,
    uploadManga,
    openMangaDetails,
    openReader,
    continueReading,
    removeBookmark
  };
})();