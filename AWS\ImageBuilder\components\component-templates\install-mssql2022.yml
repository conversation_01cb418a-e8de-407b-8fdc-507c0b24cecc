# AWS Image Builder Component: Install Microsoft SQL Server 2022
# This component installs SQL Server 2022 Developer Edition on Windows Server

name: win-server-mssql2022
description: Install Microsoft SQL Server 2022 Developer Edition on Windows Server
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: CheckExistingSQLServer
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Checking for existing SQL Server installations..."
        
        # Check for existing SQL Server instances
        $sqlInstances = Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\Instance Names\SQL" -ErrorAction SilentlyContinue
        if ($sqlInstances) {
            Write-Host "Found existing SQL Server instances:"
            $sqlInstances.PSObject.Properties | ForEach-Object {
                Write-Host "  Instance: $($_.Name) = $($_.Value)"
            }
            
            # Check if SQL Server 2022 is already installed
            $sql2022Check = Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\*\Setup" -Name "Version" -ErrorAction SilentlyContinue | 
                Where-Object { $_.Version -like "16.*" }
            
            if ($sql2022Check) {
                Write-Host "SQL Server 2022 (version 16.x) is already installed"
                Write-Host "Skipping installation..."
                exit 0
            }
        } else {
            Write-Host "No existing SQL Server instances found. Proceeding with installation..."
        }

  - name: PrepareInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Preparing SQL Server 2022 installation..."
        
        # Create temp directory
        $tempDir = "C:\temp\sql2022"
        if (!(Test-Path $tempDir)) {
            New-Item -ItemType Directory -Path $tempDir -Force
            Write-Host "Created temp directory: $tempDir"
        }
        
        # Download SQL Server 2022 Developer Edition
        $downloadUrl = "https://go.microsoft.com/fwlink/p/?linkid=2215158"
        $installerPath = "$tempDir\SQL2022-SSEI-Dev.exe"
        
        Write-Host "Downloading SQL Server 2022 Developer Edition installer..."
        try {
            Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing -TimeoutSec 600
            Write-Host "Download completed successfully"
            
            if ((Test-Path $installerPath) -and ((Get-Item $installerPath).Length -gt 1MB)) {
                Write-Host "Download verification successful"
            } else {
                throw "Downloaded file is missing or too small"
            }
        } catch {
            Write-Error "Failed to download SQL Server 2022: $($_.Exception.Message)"
            exit 1
        }

  - name: DownloadSQLServerMedia
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Downloading SQL Server 2022 installation media..."
        
        $tempDir = "C:\temp\sql2022"
        $installerPath = "$tempDir\SQL2022-SSEI-Dev.exe"
        $mediaPath = "$tempDir\media"
        
        # Create media directory
        if (!(Test-Path $mediaPath)) {
            New-Item -ItemType Directory -Path $mediaPath -Force
        }
        
        # Download the installation media using the bootstrap installer
        Write-Host "Extracting SQL Server installation media..."
        $process = Start-Process -FilePath $installerPath -ArgumentList "/Action=Download", "/MediaPath=$mediaPath", "/MediaType=CAB", "/Quiet" -Wait -PassThru
        
        if ($process.ExitCode -eq 0) {
            Write-Host "SQL Server media downloaded successfully"
            
            # Verify setup.exe exists
            $setupPath = "$mediaPath\setup.exe"
            if (Test-Path $setupPath) {
                Write-Host "Setup.exe found at: $setupPath"
            } else {
                Write-Error "Setup.exe not found in downloaded media"
                exit 1
            }
        } else {
            Write-Error "Failed to download SQL Server media. Exit code: $($process.ExitCode)"
            exit 1
        }

  - name: CreateConfigurationFile
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Creating SQL Server configuration file..."
        
        $tempDir = "C:\temp\sql2022"
        $configPath = "$tempDir\ConfigurationFile.ini"
        
        # Create configuration file for unattended installation
        $configContent = @"
[OPTIONS]
ACTION="Install"
FEATURES=SQLENGINE,REPLICATION,FULLTEXT,DQ,AS,RS,DQC,CONN,IS,BC,SDK,BOL
INSTANCENAME="MSSQLSERVER"
INSTANCEDIR="C:\Program Files\Microsoft SQL Server"
AGTSVCACCOUNT="NT AUTHORITY\SYSTEM"
AGTSVCSTARTUPTYPE="Automatic"
SQLSVCACCOUNT="NT AUTHORITY\SYSTEM"
SQLSVCSTARTUPTYPE="Automatic"
SQLCOLLATION="SQL_Latin1_General_CP1_CI_AS"
SQLSYSADMINACCOUNTS="BUILTIN\Administrators"
SECURITYMODE="SQL"
SAPWD="TempPassword123!"
TCPENABLED="1"
NPENABLED="1"
BROWSERSVCSTARTUPTYPE="Automatic"
RSSVCACCOUNT="NT AUTHORITY\SYSTEM"
RSSVCSTARTUPTYPE="Automatic"
ASSVCACCOUNT="NT AUTHORITY\SYSTEM"
ASSVCSTARTUPTYPE="Automatic"
ASCOLLATION="Latin1_General_CI_AS"
ASDATADIR="C:\Program Files\Microsoft SQL Server\MSAS16.MSSQLSERVER\OLAP\Data"
ASLOGDIR="C:\Program Files\Microsoft SQL Server\MSAS16.MSSQLSERVER\OLAP\Log"
ASBACKUPDIR="C:\Program Files\Microsoft SQL Server\MSAS16.MSSQLSERVER\OLAP\Backup"
ASTEMPDIR="C:\Program Files\Microsoft SQL Server\MSAS16.MSSQLSERVER\OLAP\Temp"
ASCONFIGDIR="C:\Program Files\Microsoft SQL Server\MSAS16.MSSQLSERVER\OLAP\Config"
ISSVCACCOUNT="NT AUTHORITY\SYSTEM"
ISSVCSTARTUPTYPE="Automatic"
IACCEPTSQLSERVERLICENSETERMS="True"
SUPPRESSPRIVACYSTATEMENTNOTICE="True"
"@
        
        Set-Content -Path $configPath -Value $configContent -Encoding UTF8
        Write-Host "Configuration file created at: $configPath"

  - name: InstallSQLServer2022
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing SQL Server 2022..."
        
        $tempDir = "C:\temp\sql2022"
        $mediaPath = "$tempDir\media"
        $setupPath = "$mediaPath\setup.exe"
        $configPath = "$tempDir\ConfigurationFile.ini"
        
        if (!(Test-Path $setupPath)) {
            Write-Error "Setup.exe not found at: $setupPath"
            exit 1
        }
        
        if (!(Test-Path $configPath)) {
            Write-Error "Configuration file not found at: $configPath"
            exit 1
        }
        
        Write-Host "Starting SQL Server 2022 installation..."
        Write-Host "This may take 30-60 minutes depending on system performance..."
        
        # Install SQL Server using configuration file
        $process = Start-Process -FilePath $setupPath -ArgumentList "/ConfigurationFile=`"$configPath`"", "/IAcceptSQLServerLicenseTerms", "/Quiet" -Wait -PassThru
        
        Write-Host "Installation process completed with exit code: $($process.ExitCode)"
        
        # SQL Server setup exit codes:
        # 0 = Success
        # 1641 = Success, reboot initiated  
        # 3010 = Success, reboot required
        # Other = Error
        
        switch ($process.ExitCode) {
            0 { 
                Write-Host "SQL Server 2022 installed successfully"
            }
            1641 { 
                Write-Host "SQL Server 2022 installed successfully (reboot initiated)"
            }
            3010 { 
                Write-Host "SQL Server 2022 installed successfully (reboot required)"
            }
            default { 
                Write-Error "Installation failed with exit code: $($process.ExitCode)"
                
                # Check for setup logs
                $logPath = "$env:ProgramFiles\Microsoft SQL Server\160\Setup Bootstrap\Log"
                if (Test-Path $logPath) {
                    Write-Host "Setup logs available at: $logPath"
                    $summaryLog = Get-ChildItem "$logPath\Summary.txt" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime | Select-Object -Last 1
                    if ($summaryLog) {
                        Write-Host "Latest summary log:"
                        Get-Content $summaryLog.FullName | Select-Object -Last 20
                    }
                }
                exit 1
            }
        }

  - name: ConfigureSQLServer
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring SQL Server 2022..."

        # Wait for SQL Server service to start
        Write-Host "Waiting for SQL Server service to start..."
        $timeout = 300 # 5 minutes
        $timer = 0
        do {
            Start-Sleep -Seconds 10
            $timer += 10
            $service = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
        } while (($service.Status -ne "Running") -and ($timer -lt $timeout))

        if ($service.Status -eq "Running") {
            Write-Host "SQL Server service is running"
        } else {
            Write-Error "SQL Server service failed to start within timeout period"
            exit 1
        }

        # Configure SQL Server settings
        try {
            # Import SQL Server module
            Import-Module SqlServer -ErrorAction SilentlyContinue

            # Enable TCP/IP protocol
            Write-Host "Enabling TCP/IP protocol..."
            $smo = 'Microsoft.SqlServer.Management.Smo.'
            $wmi = new-object ($smo + 'Wmi.ManagedComputer')
            $uri = "ManagedComputer[@Name='$env:COMPUTERNAME']/ServerInstance[@Name='MSSQLSERVER']/ServerProtocol[@Name='Tcp']"
            $Tcp = $wmi.GetSmoObject($uri)
            $Tcp.IsEnabled = $true
            $Tcp.Alter()

            # Enable Named Pipes
            Write-Host "Enabling Named Pipes protocol..."
            $uri = "ManagedComputer[@Name='$env:COMPUTERNAME']/ServerInstance[@Name='MSSQLSERVER']/ServerProtocol[@Name='Np']"
            $Np = $wmi.GetSmoObject($uri)
            $Np.IsEnabled = $true
            $Np.Alter()

            Write-Host "SQL Server protocols configured successfully"

        } catch {
            Write-Warning "Failed to configure SQL Server protocols: $($_.Exception.Message)"
            Write-Host "Manual configuration may be required"
        }

  - name: VerifyInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Verifying SQL Server 2022 installation..."

        # Check SQL Server service
        $sqlService = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
        if ($sqlService -and $sqlService.Status -eq "Running") {
            Write-Host "SUCCESS: SQL Server service is running"
        } else {
            Write-Error "FAILED: SQL Server service is not running"
            exit 1
        }

        # Check SQL Server Agent service
        $agentService = Get-Service -Name "SQLSERVERAGENT" -ErrorAction SilentlyContinue
        if ($agentService) {
            Write-Host "SQL Server Agent service status: $($agentService.Status)"
        }

        # Test SQL Server connection
        try {
            $connectionString = "Server=localhost;Integrated Security=true;Connection Timeout=30;"
            $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
            $connection.Open()

            $command = $connection.CreateCommand()
            $command.CommandText = "SELECT @@VERSION"
            $version = $command.ExecuteScalar()

            Write-Host "SUCCESS: SQL Server connection test passed"
            Write-Host "SQL Server Version: $version"

            $connection.Close()
        } catch {
            Write-Error "FAILED: SQL Server connection test failed: $($_.Exception.Message)"
            exit 1
        }

        # Display installation summary
        Write-Host "`nSQL Server 2022 Installation Summary:"
        Write-Host "- Instance Name: MSSQLSERVER"
        Write-Host "- Authentication: Mixed Mode (Windows + SQL)"
        Write-Host "- TCP/IP: Enabled"
        Write-Host "- Named Pipes: Enabled"
        Write-Host "- Default SA Password: TempPassword123! (CHANGE THIS!)"

  - name: Cleanup
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Cleaning up installation files..."

        $tempDir = "C:\temp\sql2022"
        if (Test-Path $tempDir) {
            try {
                Remove-Item $tempDir -Recurse -Force
                Write-Host "Cleanup completed successfully"
            } catch {
                Write-Warning "Failed to clean up temp directory: $($_.Exception.Message)"
            }
        }

- name: validate
  steps:
  - name: ValidateSQLServer2022
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Final validation of SQL Server 2022 installation..."

        # Check service status
        $sqlService = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue
        if (-not $sqlService -or $sqlService.Status -ne "Running") {
            Write-Error "VALIDATION FAILED: SQL Server service is not running"
            exit 1
        }

        # Test database connectivity
        try {
            $connectionString = "Server=localhost;Integrated Security=true;Connection Timeout=30;"
            $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
            $connection.Open()

            $command = $connection.CreateCommand()
            $command.CommandText = "SELECT SERVERPROPERTY('ProductVersion') as Version, SERVERPROPERTY('Edition') as Edition"
            $reader = $command.ExecuteReader()

            if ($reader.Read()) {
                $version = $reader["Version"]
                $edition = $reader["Edition"]
                Write-Host "VALIDATION SUCCESS: SQL Server 2022 is properly installed"
                Write-Host "Version: $version"
                Write-Host "Edition: $edition"
            }

            $reader.Close()
            $connection.Close()

        } catch {
            Write-Error "VALIDATION FAILED: Cannot connect to SQL Server: $($_.Exception.Message)"
            exit 1
        }
