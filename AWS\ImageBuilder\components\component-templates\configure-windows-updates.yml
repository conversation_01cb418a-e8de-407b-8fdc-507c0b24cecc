# AWS Image Builder Component: Configure Windows Updates/WSUS
# This component configures Windows Update settings and WSUS integration

name: win-server-windows-updates
description: Configure Windows Update settings and WSUS integration for enterprise environments
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: CheckCurrentUpdateSettings
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Checking current Windows Update settings..."
        
        # Check Windows Update service status
        $wuService = Get-Service -Name "wuauserv" -ErrorAction SilentlyContinue
        if ($wuService) {
            Write-Host "Windows Update service status: $($wuService.Status)"
        }
        
        # Check current update settings
        $updateSettings = Get-ItemProperty "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -ErrorAction SilentlyContinue
        if ($updateSettings) {
            Write-Host "Current Windows Update policy settings:"
            $updateSettings | Format-List
        } else {
            Write-Host "No Windows Update policies currently configured"
        }
        
        # Check WSUS settings
        $wsusSettings = Get-ItemProperty "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" -ErrorAction SilentlyContinue
        if ($wsusSettings) {
            Write-Host "Current WSUS settings:"
            $wsusSettings | Format-List
        }

  - name: ConfigureWindowsUpdateService
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Windows Update service..."
        
        # Ensure Windows Update service is set to automatic
        Write-Host "Setting Windows Update service to Automatic startup..."
        Set-Service -Name "wuauserv" -StartupType Automatic
        
        # Start the service if it's not running
        $wuService = Get-Service -Name "wuauserv"
        if ($wuService.Status -ne "Running") {
            Write-Host "Starting Windows Update service..."
            Start-Service -Name "wuauserv"
        }
        
        # Configure Background Intelligent Transfer Service (BITS)
        Write-Host "Configuring BITS service..."
        Set-Service -Name "BITS" -StartupType Automatic
        $bitsService = Get-Service -Name "BITS"
        if ($bitsService.Status -ne "Running") {
            Start-Service -Name "BITS"
        }

  - name: ConfigureAutomaticUpdates
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Automatic Updates settings..."
        
        # Create Windows Update policy registry keys
        $auPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU"
        if (!(Test-Path $auPath)) {
            New-Item -Path $auPath -Force | Out-Null
            Write-Host "Created Windows Update AU registry path"
        }
        
        # Configure automatic updates
        # AUOptions: 2=Notify before download, 3=Auto download and notify, 4=Auto download and install
        Set-ItemProperty -Path $auPath -Name "AUOptions" -Value 3 -Type DWord
        Write-Host "Set automatic updates to download and notify"
        
        # Configure scheduled install day (0=Every day, 1=Sunday, 2=Monday, etc.)
        Set-ItemProperty -Path $auPath -Name "ScheduledInstallDay" -Value 0 -Type DWord
        Write-Host "Set scheduled install day to every day"
        
        # Configure scheduled install time (0-23 hours)
        Set-ItemProperty -Path $auPath -Name "ScheduledInstallTime" -Value 3 -Type DWord
        Write-Host "Set scheduled install time to 3 AM"
        
        # Enable automatic updates
        Set-ItemProperty -Path $auPath -Name "NoAutoUpdate" -Value 0 -Type DWord
        Write-Host "Enabled automatic updates"
        
        # Configure reboot behavior
        Set-ItemProperty -Path $auPath -Name "NoAutoRebootWithLoggedOnUsers" -Value 1 -Type DWord
        Write-Host "Disabled automatic reboot when users are logged on"
        
        # Configure update detection frequency (hours)
        Set-ItemProperty -Path $auPath -Name "DetectionFrequencyEnabled" -Value 1 -Type DWord
        Set-ItemProperty -Path $auPath -Name "DetectionFrequency" -Value 22 -Type DWord
        Write-Host "Set update detection frequency to 22 hours"

  - name: ConfigureWSUSSettings
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring WSUS settings..."
        
        # Create WSUS registry path
        $wsusPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"
        if (!(Test-Path $wsusPath)) {
            New-Item -Path $wsusPath -Force | Out-Null
            Write-Host "Created WSUS registry path"
        }
        
        # Note: These are template settings - update with your actual WSUS server
        # Set WSUS server URL (update with your WSUS server)
        $wsusServer = "http://your-wsus-server:8530"
        Set-ItemProperty -Path $wsusPath -Name "WUServer" -Value $wsusServer -Type String
        Set-ItemProperty -Path $wsusPath -Name "WUStatusServer" -Value $wsusServer -Type String
        Write-Host "Set WSUS server to: $wsusServer (UPDATE THIS WITH YOUR ACTUAL WSUS SERVER)"
        
        # Enable use of WSUS server
        Set-ItemProperty -Path "$wsusPath\AU" -Name "UseWUServer" -Value 1 -Type DWord
        Write-Host "Enabled use of WSUS server"
        
        # Configure target group (optional)
        $targetGroup = "Windows Servers"
        Set-ItemProperty -Path $wsusPath -Name "TargetGroupEnabled" -Value 1 -Type DWord
        Set-ItemProperty -Path $wsusPath -Name "TargetGroup" -Value $targetGroup -Type String
        Write-Host "Set target group to: $targetGroup"
        
        # Disable access to Microsoft Update
        Set-ItemProperty -Path $wsusPath -Name "DisableWindowsUpdateAccess" -Value 1 -Type DWord
        Write-Host "Disabled direct access to Microsoft Update"

  - name: ConfigureUpdateCategories
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring update categories..."
        
        $wsusPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"
        
        # Enable automatic updates for recommended updates
        Set-ItemProperty -Path "$wsusPath\AU" -Name "IncludeRecommendedUpdates" -Value 1 -Type DWord
        Write-Host "Enabled recommended updates"
        
        # Configure Microsoft Update (for Office updates, etc.)
        Set-ItemProperty -Path "$wsusPath\AU" -Name "AllowMUUpdateService" -Value 1 -Type DWord
        Write-Host "Allowed Microsoft Update service"
        
        # Configure driver updates
        Set-ItemProperty -Path $wsusPath -Name "ExcludeWUDriversInQualityUpdate" -Value 1 -Type DWord
        Write-Host "Excluded Windows Update drivers from quality updates"

  - name: ConfigureMaintenanceWindow
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring maintenance window settings..."
        
        $auPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU"
        
        # Configure active hours to prevent restarts during business hours
        # Active hours: 8 AM to 6 PM (8-18)
        Set-ItemProperty -Path $auPath -Name "ActiveHoursStart" -Value 8 -Type DWord
        Set-ItemProperty -Path $auPath -Name "ActiveHoursEnd" -Value 18 -Type DWord
        Write-Host "Set active hours: 8 AM to 6 PM"
        
        # Configure restart grace period (minutes)
        Set-ItemProperty -Path $auPath -Name "RebootWarningTimeoutEnabled" -Value 1 -Type DWord
        Set-ItemProperty -Path $auPath -Name "RebootWarningTimeout" -Value 60 -Type DWord
        Write-Host "Set reboot warning timeout to 60 minutes"
        
        # Configure reboot grace period (minutes)
        Set-ItemProperty -Path $auPath -Name "RebootRelaunchTimeoutEnabled" -Value 1 -Type DWord
        Set-ItemProperty -Path $auPath -Name "RebootRelaunchTimeout" -Value 1440 -Type DWord
        Write-Host "Set reboot grace period to 24 hours (1440 minutes)"

  - name: ConfigureDeliveryOptimization
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Delivery Optimization..."
        
        # Create Delivery Optimization registry path
        $doPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\DeliveryOptimization"
        if (!(Test-Path $doPath)) {
            New-Item -Path $doPath -Force | Out-Null
            Write-Host "Created Delivery Optimization registry path"
        }
        
        # Configure download mode (0=HTTP only, 1=LAN, 2=Group, 3=Internet, 99=Simple, 100=Bypass)
        # Setting to 1 (LAN) for enterprise environments
        Set-ItemProperty -Path $doPath -Name "DODownloadMode" -Value 1 -Type DWord
        Write-Host "Set Delivery Optimization to LAN mode"
        
        # Configure bandwidth limits
        Set-ItemProperty -Path $doPath -Name "DOPercentageMaxDownloadBandwidth" -Value 80 -Type DWord
        Write-Host "Set maximum download bandwidth to 80%"
        
        Set-ItemProperty -Path $doPath -Name "DOPercentageMaxUploadBandwidth" -Value 20 -Type DWord
        Write-Host "Set maximum upload bandwidth to 20%"

  - name: RestartWindowsUpdateService
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Restarting Windows Update service to apply changes..."
        
        try {
            # Stop Windows Update service
            Stop-Service -Name "wuauserv" -Force
            Write-Host "Stopped Windows Update service"
            
            # Wait a moment
            Start-Sleep -Seconds 5
            
            # Start Windows Update service
            Start-Service -Name "wuauserv"
            Write-Host "Started Windows Update service"
            
            # Verify service is running
            $service = Get-Service -Name "wuauserv"
            Write-Host "Windows Update service status: $($service.Status)"
            
        } catch {
            Write-Warning "Failed to restart Windows Update service: $($_.Exception.Message)"
        }

- name: validate
  steps:
  - name: ValidateWindowsUpdateConfiguration
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Validating Windows Update configuration..."
        
        # Check Windows Update service
        $wuService = Get-Service -Name "wuauserv" -ErrorAction SilentlyContinue
        if ($wuService -and $wuService.Status -eq "Running") {
            Write-Host "✓ Windows Update service is running"
        } else {
            Write-Error "✗ Windows Update service is not running"
            exit 1
        }
        
        # Check automatic updates configuration
        $auSettings = Get-ItemProperty "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -ErrorAction SilentlyContinue
        if ($auSettings) {
            Write-Host "✓ Automatic Updates settings configured"
            Write-Host "  - AUOptions: $($auSettings.AUOptions)"
            Write-Host "  - NoAutoUpdate: $($auSettings.NoAutoUpdate)"
            Write-Host "  - ScheduledInstallTime: $($auSettings.ScheduledInstallTime)"
        } else {
            Write-Warning "✗ Automatic Updates settings not found"
        }
        
        # Check WSUS configuration
        $wsusSettings = Get-ItemProperty "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" -ErrorAction SilentlyContinue
        if ($wsusSettings) {
            Write-Host "✓ WSUS settings configured"
            Write-Host "  - WUServer: $($wsusSettings.WUServer)"
            Write-Host "  - TargetGroup: $($wsusSettings.TargetGroup)"
        } else {
            Write-Warning "✗ WSUS settings not configured"
        }
        
        # Test Windows Update detection
        Write-Host "`nTesting Windows Update detection..."
        try {
            $updateSession = New-Object -ComObject Microsoft.Update.Session
            $updateSearcher = $updateSession.CreateUpdateSearcher()
            Write-Host "✓ Windows Update COM objects accessible"
        } catch {
            Write-Warning "✗ Windows Update COM objects not accessible: $($_.Exception.Message)"
        }
        
        Write-Host "`nVALIDATION SUCCESS: Windows Update configuration completed"
        Write-Host "IMPORTANT: Update the WSUS server URL with your actual WSUS server address"
