# AWS Image Builder Component: Configure Registry Tweaks
# This component applies common Windows registry optimizations and configurations

name: ConfigureRegistryTweaks
description: Apply Windows registry optimizations and security configurations
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ApplyPerformanceOptimizations
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Applying Windows performance optimizations..."
              
              # Disable Windows Error Reporting
              Write-Host "Disabling Windows Error Reporting..."
              New-Item -Path "HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting" -Force | Out-Null
              Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting" -Name "Disabled" -Value 1 -Type DWord
              
              # Disable Customer Experience Improvement Program
              Write-Host "Disabling Customer Experience Improvement Program..."
              New-Item -Path "HKLM:\SOFTWARE\Microsoft\SQMClient\Windows" -Force | Out-Null
              Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\SQMClient\Windows" -Name "CEIPEnable" -Value 0 -Type DWord
              
              # Optimize visual effects for performance
              Write-Host "Optimizing visual effects for performance..."
              Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" -Name "VisualFXSetting" -Value 2 -Type DWord
              
              # Disable unnecessary services startup
              Write-Host "Configuring service startup optimizations..."
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Themes" -Name "Start" -Value 4 -Type DWord
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\TabletInputService" -Name "Start" -Value 4 -Type DWord

      - name: ApplySecurityConfigurations
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Applying Windows security configurations..."
              
              # Enable Windows Defender real-time protection
              Write-Host "Configuring Windows Defender..."
              Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows Defender\Real-Time Protection" -Name "DisableRealtimeMonitoring" -Value 0 -Type DWord -ErrorAction SilentlyContinue
              
              # Disable AutoRun for all drives
              Write-Host "Disabling AutoRun for security..."
              New-Item -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" -Force | Out-Null
              Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" -Name "NoDriveTypeAutoRun" -Value 255 -Type DWord
              
              # Enable UAC (User Account Control)
              Write-Host "Ensuring UAC is enabled..."
              Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "EnableLUA" -Value 1 -Type DWord
              Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "ConsentPromptBehaviorAdmin" -Value 2 -Type DWord
              
              # Disable guest account
              Write-Host "Disabling guest account..."
              Set-ItemProperty -Path "HKLM:\SAM\SAM\Domains\Account\Users\000001F5" -Name "F" -Value ([byte[]](0x01,0x01,0x00,0x00)) -ErrorAction SilentlyContinue

      - name: ApplyNetworkOptimizations
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Applying network optimizations..."
              
              # Optimize TCP settings for server workloads
              Write-Host "Optimizing TCP settings..."
              New-Item -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" -Force | Out-Null
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" -Name "TcpAckFrequency" -Value 1 -Type DWord
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" -Name "TCPNoDelay" -Value 1 -Type DWord
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" -Name "TcpTimedWaitDelay" -Value 30 -Type DWord
              
              # Disable IPv6 if not needed (common in enterprise environments)
              Write-Host "Configuring IPv6 settings..."
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip6\Parameters" -Name "DisabledComponents" -Value 32 -Type DWord
              
              # Configure DNS settings for better performance
              Write-Host "Optimizing DNS settings..."
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Dnscache\Parameters" -Name "MaxCacheTtl" -Value 86400 -Type DWord
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Dnscache\Parameters" -Name "MaxNegativeCacheTtl" -Value 300 -Type DWord

      - name: ApplySystemOptimizations
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Applying system optimizations..."
              
              # Disable hibernation to save disk space
              Write-Host "Disabling hibernation..."
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Power" -Name "HibernateEnabled" -Value 0 -Type DWord
              
              # Configure page file settings for optimal performance
              Write-Host "Configuring virtual memory settings..."
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "ClearPageFileAtShutdown" -Value 0 -Type DWord
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "DisablePagingExecutive" -Value 1 -Type DWord
              
              # Optimize boot settings
              Write-Host "Optimizing boot settings..."
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control" -Name "WaitToKillServiceTimeout" -Value "2000" -Type String
              Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control" -Name "ServicesPipeTimeout" -Value 60000 -Type DWord
              
              # Configure Windows Update settings for enterprise use
              Write-Host "Configuring Windows Update settings..."
              New-Item -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" -Force | Out-Null
              New-Item -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Force | Out-Null
              Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "NoAutoUpdate" -Value 0 -Type DWord
              Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "AUOptions" -Value 3 -Type DWord

      - name: ApplyEventLogOptimizations
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Configuring Event Log settings..."
              
              # Increase event log sizes for better monitoring
              Write-Host "Increasing event log sizes..."
              wevtutil sl Application /ms:104857600  # 100MB
              wevtutil sl System /ms:104857600       # 100MB
              wevtutil sl Security /ms:209715200     # 200MB
              
              # Configure log retention
              wevtutil sl Application /rt:false
              wevtutil sl System /rt:false
              wevtutil sl Security /rt:false
              
              Write-Host "Event log configuration completed"

  - name: validate
    steps:
      - name: ValidateRegistryTweaks
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Validating registry tweaks..."
              
              # Validate key registry settings
              $validationErrors = @()
              
              # Check Windows Error Reporting is disabled
              $werDisabled = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting" -Name "Disabled" -ErrorAction SilentlyContinue
              if ($werDisabled.Disabled -ne 1) {
                  $validationErrors += "Windows Error Reporting not properly disabled"
              }
              
              # Check UAC is enabled
              $uacEnabled = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "EnableLUA" -ErrorAction SilentlyContinue
              if ($uacEnabled.EnableLUA -ne 1) {
                  $validationErrors += "UAC not properly enabled"
              }
              
              # Check AutoRun is disabled
              $autoRunDisabled = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" -Name "NoDriveTypeAutoRun" -ErrorAction SilentlyContinue
              if ($autoRunDisabled.NoDriveTypeAutoRun -ne 255) {
                  $validationErrors += "AutoRun not properly disabled"
              }
              
              if ($validationErrors.Count -eq 0) {
                  Write-Host "VALIDATION SUCCESS: All registry tweaks applied correctly"
                  exit 0
              } else {
                  Write-Error "VALIDATION FAILED: $($validationErrors -join ', ')"
                  exit 1
              }
