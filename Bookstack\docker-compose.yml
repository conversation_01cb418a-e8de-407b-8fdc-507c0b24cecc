services:
  bookstack:
    image: solidnerd/bookstack:latest
    environment:
    - DB_HOST=srv009485.mud.internal.co.za:3306
    - DB_DATABASE=bookstackdb
    - DB_USERNAME=bookstacksvc
    - DB_PASSWORD=HjYbTv*Jl3&bjpD1
    # Set the APP_ to the URL of bookstack without without a trailing slash,
    # but including any port numbers. For example, one of:
    # APP_URL=https://example.com
    # APP_URL=http://localhost:8080
    # APP_URL=https://wiki.example.com:8443
    - APP_URL=http://srv041374.mud.internal.co.za:8088
    # APP_KEY is used for encryption where needed, so needs to be persisted to
    # preserve decryption abilities.
    # Can run `php artisan key:generate` to generate a key
    - APP_KEY=K9#mP2$vX7@nQ4&jR8%wE3!tY6^uI1*z
    volumes:
    - uploads:/var/www/bookstack/public/uploads
    - storage-uploads:/var/www/bookstack/storage/uploads
    ports:
    - "8080:8080"

volumes:
 uploads:
 storage-uploads: