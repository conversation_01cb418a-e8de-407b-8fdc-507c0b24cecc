#docker run --rm -v "/data/netbox-docker:/mnt/pwd" busybox /bin/sh -c "chmod -R 644 /mnt/pwd/configuration"


chmod -R 644 /mnt/pwd/configuration
chmod -R 755 /mnt/pwd/configuration

docker run --rm -v "/data/netbox-docker:/mnt/pwd" busybox /bin/sh -c "chmod -R 644 /mnt/pwd/configuration"

docker run --rm -v "/data/netbox-docker:/mnt/pwd" busybox /bin/sh -c "
  find /mnt/pwd/configuration -type f -exec chmod 644 {} \; &&
  find /mnt/pwd/configuration -type d -exec chmod 755 {} \;
"