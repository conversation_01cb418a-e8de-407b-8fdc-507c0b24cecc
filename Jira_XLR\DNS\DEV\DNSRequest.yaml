---
apiVersion: xl-release/v1
kind: Templates
metadata:
  home: DCS/Executable_Master_Tasks
spec:
- template: ADS DNS Request
  description: ADS Automated DNS Request
  scheduledStartDate: 2024-03-15T07:00:00Z
  phases:
  - phase: Prep Phase
    tasks:
    - name: Get Config
      type: xlrelease.SequentialGroup
      tasks:
      - name: Gen JobSpec
        type: xlrelease.ScriptTask
        script: |-
          import json

          datamap = '''
              {
                "request_source": "${request_source}",
                "jira_ref": "${jira_ref}",
                "hostname": "${hostname}",
                "ip_address": "${ip_address}",
                "app_owner": "${app_owner}",
                "pri_tech_owner": "${pri_tech_owner}",
                "sec_tech_owner": "${sec_tech_owner}",
                "host_alias": "${host_alias}",
                "new_hostname": "${new_hostname}",
                "new_ip_address": "${new_ip_address}",
                "new_host_alias": "${new_host_alias}",
                "record_type": "${record_type}",
                "category": "${category}",
                "server_name":"${hostname}",
                "act_date":"${act_date}"
              }
          '''
          releaseVariables['dnsRequestDataRaw'] = datamap

          dataobj = json.loads(datamap)
          releaseVariables['dnsRequestData'] = dataobj
      - name: Assign Variables
        type: xlrelease.ScriptTask
        script: |-
          import json
          from datetime import datetime
          from java.text import SimpleDateFormat
          from java.util import TimeZone

          if releaseVariables["category"] == "Update Record":
              deleteRecord = True
              addRecord = True
              updateRecord = True
          elif releaseVariables["category"] == "Add Record":
              deleteRecord = False
              addRecord = True
              updateRecord = False
          elif releaseVariables["category"] == "Remove Record":
              deleteRecord = True
              addRecord = False
              updateRecord = False

          releaseVariables["updateRecord"] = updateRecord
          releaseVariables["addRecord"] = addRecord
          releaseVariables["deleteRecord"] = deleteRecord

          server_name, domain = releaseVariables["hostname"].split('.', 1)
          releaseVariables["server_name"] = server_name
          releaseVariables["domain"] = domain

          if not releaseVariables['dnsRequestDataRaw']:
              releaseVariables['action_proceed'] = False
    - name: Validation
      type: xlrelease.SequentialGroup
      tasks:
      - name: Jira Update Message
        type: xlrelease.ScriptTask
        script: |-
          if releaseVariables["record_type"] == "C-Record" and releaseVariables["category"] == "Update Record":
              updateMessageReceived = (
                  'Action: ${category}\n'
                  'Record Type: ${record_type}\n'
                  'From: ${host_alias} - ${hostname}\n'
                  'To: ${new_host_alias} - ${new_hostname}'
              )
              releaseVariables['action_proceed'] = True
          elif releaseVariables["record_type"] == "C-Record" and releaseVariables["category"] == "Remove Record":
              updateMessageReceived = (
                  'Action: ${category}\n'
                  'Record Type: ${record_type}\n'
                  'Record: ${host_alias} pointing to ${hostname}'
              )
              releaseVariables['action_proceed'] = True
          elif releaseVariables["record_type"] == "C-Record" and releaseVariables["category"] == "Add Record":
              updateMessageReceived = (
                  'Action: ${category}\n'
                  'Record Type: ${record_type}\n'
                  'Record: ${host_alias} pointing to ${hostname}'
              )
              releaseVariables['action_proceed'] = True
          elif releaseVariables["record_type"] == "A-Record" and releaseVariables["category"] == "Update Record":
              updateMessageReceived = (
                  'Action: ${category}\n'
                  'Record Type: ${record_type}\n'
                  'From: ${hostname} - ${ip_address}\n'
                  'To: ${new_hostname} - ${new_ip_address}'
              )
              releaseVariables['action_proceed'] = True
          elif releaseVariables["record_type"] == "A-Record" and releaseVariables["category"] == "Remove Record":
              updateMessageReceived = (
                  'Action: ${category}\n'
                  'Record Type: ${record_type}\n'
                  'Record: ${hostname} with an IP of ${ip_address}'
              )
              releaseVariables['action_proceed'] = True
          elif releaseVariables["record_type"] == "A-Record" and releaseVariables["category"] == "Add Record":
              updateMessageReceived = (
                  'Action: ${category}\n'
                  'Record Type: ${record_type}\n'
                  'Record: ${hostname} with an IP of ${ip_address}'
              )
              releaseVariables['action_proceed'] = True
          else:
              updateMessageReceived = 'This request has missing information! Unable to proceed.'
              releaseVariables["updateMessageReceived"] = updateMessageReceived
              releaseVariables['action_proceed'] = False

          releaseVariables["updateMessageReceived"] = updateMessageReceived
      - name: Update JIRA Comment
        type: jira.UpdateIssue
        jiraServer: Jira Dev
        issueId: "${jira_ref}"
        comment: |-
          *DNS request received with the following details:*
          ${updateMessageReceived}
    - name: Schedule Jobs
      type: xlrelease.SequentialGroup
      tasks:
      - name: Get JIRA Info
        type: xlrelease.SequentialGroup
        tasks:
        - name: Confirm JIRA set to proceed
          type: jira.GetIssueDetails
          jiraServer: Jira Dev
          issueId: "${jira_ref}"
          variableMapping:
            pythonScript.issueDetails: "${jira_response}"
        - name: Work with JIRA issue
          type: xlrelease.ScriptTask
          script: |-
            from datetime import datetime, timedelta
            from java.util import Calendar, Date
            from java.text import SimpleDateFormat

            rawdata = releaseVariables['jira_response']
            # Access nested fields properly
            scheduledDate = rawdata["fields.customfield_19992"]
            print scheduledDate

            dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ")
            scheduledDate = dateFormat.parse(scheduledDate)

            releaseVariables["scheduledDate"] = scheduledDate
            print scheduledDate
        - name: Schedule Action Phase
          type: xlrelease.ScriptTask
          script: |-
            from datetime import datetime, timedelta
            from java.util import Calendar, Date

            actdate = releaseVariables['act_date']
            print("PHASE START: ", type(actdate), actdate)

            release.title = releaseVariables['jira_ref']
            releaseApi.updateRelease(release)

            phases = []
            for p in release.phases:
                print("ID: ", p.id)
                phases.append(p.id)
            ActionPhase = phaseApi.getPhase(phases[1])
            print ActionPhase
            # Set Action date and time to the scheduled date and time.
            ActionPhase.setStartDate(actdate) # setScheduledStartDate
            ActionPhase.setScheduledStartDate(actdate)
            ActionPhase.setTitle("COOLDOWN until " + str(actdate))
            ActionPhase.setFlagComment("COOLDOWN")
            # Apply updated properties
            phaseApi.updatePhase(ActionPhase)

            cal = Calendar.getInstance()
            #print cal1  # This will print something like: java.util.GregorianCalendar[time=1620123456789,areFieldsSet=true,areAllFieldsSet=true,lenient=true,zone=America/New_York,firstDayOfWeek=1,minimalDaysInFirstWeek=1,ERA=1,YEAR=2023,MONTH=4,WEEK_OF_YEAR=18,WEEK_OF_MONTH=1,DAY_OF_MONTH=10,DAY_OF_YEAR=130,DAY_OF_WEEK=3,DAY_OF_WEEK_IN_MONTH=2,AM_PM=0,HOUR=10,HOUR_OF_DAY=10,MINUTE=30,SECOND=45,MILLISECOND=789,ZONE_OFFSET=-18000000,DST_OFFSET=3600000]
            cal.setTime(actdate)

            taskslist = []
            for item in ActionPhase.tasks:
                if item.title == "Check to Proceed":
                    t = taskApi.getTask(item.id)
                    t.setScheduledStartDate(cal.getTime())
                    t.setStartDate(cal.getTime())
                    t.setFlagComment("COOLDOWN")
                    t.setDescription("Scheduled for " + str(actdate))
                    taskApi.updateTask(t)
    color: '#3d6c9e'
  - phase: Validation Phase
    tasks:
    - name: Check to Proceed
      type: xlrelease.SequentialGroup
      tasks:
      - name: Confirm JIRA set to proceed
        type: jira.GetIssueDetails
        jiraServer: Jira Dev
        issueId: "${jira_ref}"
        variableMapping:
          pythonScript.issueDetails: "${jira_response}"
      - name: Work with JIRA issue
        type: xlrelease.ScriptTask
        script: |-
          releaseVariables['action_proceed'] = False
          releaseVariables['action_cancelled'] = False
          releaseVariables['dnsRemove_proceed'] = False
          releaseVariables['dnsAdd_proceed'] = False

          rawdata = releaseVariables['jira_response']
          print rawdata["fields.status.name"]
          var_progress = rawdata["fields.status.name"]

          if var_progress == 'In Progress':
            releaseVariables['action_proceed'] = True
          else:
            releaseVariables['action_proceed'] = False

          releaseVariables['dnsRemove_proceed'] = releaseVariables["updateRecord"] or releaseVariables["deleteRecord"]
          releaseVariables['dnsAdd_proceed'] = releaseVariables["updateRecord"] or releaseVariables["addRecord"]
    color: '#3d6c9e'
  - phase: Delete Phase
    tasks:
    - name: Check to Proceed
      type: xlrelease.SequentialGroup
      precondition: |-
        if releaseVariables['action_proceed'] == False:
          result = False
        else:
          result = True
      tasks:
      - name: Action Record
        type: xlrelease.SequentialGroup
        precondition: |-
          if releaseVariables['dnsRemove_proceed'] == False:
            result = False
          else:
            result = True
        tasks:
        - name: DNS API Variable Assignment
          type: xlrelease.ScriptTask
          script: |-
            #import json
            #print "dns_api_response: \n \n"
            #rvar = releaseApi.getVariables("")
            #print rvar
        - name: Remove DNS Record
          type: xlrelease.CreateReleaseTask
          precondition: "result = releaseVariables[\"updateRecord\"] or releaseVariables[\"deleteRecord\"]"
          newReleaseTitle: Remove DNS Record
          templateVariables:
          - type: xlrelease.StringVariable
            key: success
            requiresValue: false
            label: Success Result
            description: Result of the second expression
            value: "${removeSuccess}"
          - type: xlrelease.StringVariable
            key: jira_ref
            requiresValue: false
            label: Jira Reference
            description: Jira reference Number
            value: "${jira_ref}"
          - type: xlrelease.StringVariable
            key: dnsAPIResponse
            requiresValue: false
            label: DNS API Response
            description: Result of the first expression
            value: "${apiRemoveResponse}"
          - type: xlrelease.StringVariable
            key: dnsAPIResponseRaw
            requiresValue: false
            label: Raw DNS API Response
            value: "${raw_apiRemoveResponse}"
          - type: xlrelease.StringVariable
            key: record_type
            value: "${record_type}"
          - type: xlrelease.StringVariable
            key: category
            value: "${category}"
          - type: xlrelease.StringVariable
            key: hostname
            requiresValue: false
            value: "${hostname}"
          - type: xlrelease.StringVariable
            key: new_hostname
            requiresValue: false
            value: "${new_hostname}"
          - type: xlrelease.StringVariable
            key: ip_address
            requiresValue: false
            value: "${ip_address}"
          - type: xlrelease.StringVariable
            key: new_ip_address
            requiresValue: false
            value: "${new_ip_address}"
          - type: xlrelease.StringVariable
            key: host_alias
            requiresValue: false
            value: "${host_alias}"
          - type: xlrelease.StringVariable
            key: new_host_alias
            requiresValue: false
            value: "${new_host_alias}"
          createdReleaseId: "${dnsRemoveAPIResponse_ID}"
          template: DCS/Individual_Technical_Tasks/DNS-RemoveRecord
          folder: DCS/Individual_Technical_Tasks
        - name: Remove DNS Record Proceed
          type: xlrelease.GateTask
          dependencies:
          - type: xlrelease.Dependency
            targetId: "${dnsRemoveAPIResponse}"
        - name: API Response Cleanup
          type: xlrelease.ScriptTask
          script: |-
            import json

            releaseVariables['dnsRemoveDate'] = releaseVariables['dnsRemoveAPIResponse']['date']
            releaseVariables['dnsRemoveStatus'] = releaseVariables['dnsRemoveAPIResponse']['status']
            releaseVariables['dnsRemoveMessage'] = releaseVariables['dnsRemoveAPIResponse']['message']
            releaseVariables['dnsRemoveSuccess'] = releaseVariables['dnsRemoveAPIResponse']['success']

            if releaseVariables['dnsRemoveSuccess']  == 'success' and releaseVariables['dnsRemoveStatus'] == 'NORMAL':
                releaseVariables['action_proceed'] = True
                dnsRemoveUpdateMessage = "Removing of DNS record succeeded!\n" + \
                                     releaseVariables['dnsRemoveRequestDataRaw']
            else:
                releaseVariables['action_proceed'] = False
                dnsRemoveUpdateMessage = "Removing of DNS record failed\n" + \
                                     releaseVariables['dnsRemoveRequestDataRaw']

            releaseVariables['dnsRemoveUpdateMessage'] = dnsRemoveUpdateMessage

            print dnsRemoveUpdateMessage
        - name: Update JIRA Comment
          type: jira.UpdateIssue
          jiraServer: Jira Dev
          issueId: "${jira_ref}"
          comment: "${dnsRemoveUpdateMessage}"
    color: '#d61f21'
  - phase: Add Phase
    tasks:
    - name: Check to Proceed
      type: xlrelease.SequentialGroup
      precondition: |-
        if releaseVariables['action_proceed'] == False:
          result = False
        else:
          result = True
      tasks:
      - name: Action Record
        type: xlrelease.SequentialGroup
        precondition: |-
          if releaseVariables['dnsAdd_proceed'] == True:
            result = True
          else:
            result = False
        tasks:
        - name: DNS API Variable Assignment
          type: xlrelease.ScriptTask
          script: |-
            #import json
            #print "dns_api_response: \n \n"
            #rvar = releaseApi.getVariables("")
            #print rvar
        - name: Add DNS Record
          type: xlrelease.CreateReleaseTask
          precondition: "result = releaseVariables[\"updateRecord\"] or releaseVariables[\"addRecord\"]"
          newReleaseTitle: Add DNS Record
          templateVariables:
          - type: xlrelease.StringVariable
            key: success
            requiresValue: false
            label: Success Result
            description: Result of the second expression
            value: "${addSuccess}"
          - type: xlrelease.StringVariable
            key: jira_ref
            requiresValue: false
            label: Jira Reference
            description: Jira reference Number
            value: "${jira_ref}"
          - type: xlrelease.StringVariable
            key: dnsAPIResponse
            requiresValue: false
            label: DNS API Response
            description: Result of the first expression
            value: "${apiAddResponse}"
          - type: xlrelease.StringVariable
            key: dnsAPIResponseRaw
            requiresValue: false
            label: Raw DNS API Response
            value: "${raw_apiAddResponse}"
          - type: xlrelease.StringVariable
            key: record_type
            value: "${record_type}"
          - type: xlrelease.StringVariable
            key: category
            value: "${category}"
          - type: xlrelease.StringVariable
            key: hostname
            requiresValue: false
            value: "${hostname}"
          - type: xlrelease.StringVariable
            key: new_hostname
            requiresValue: false
            value: "${new_hostname}"
          - type: xlrelease.StringVariable
            key: ip_address
            requiresValue: false
            value: "${ip_address}"
          - type: xlrelease.StringVariable
            key: new_ip_address
            requiresValue: false
            value: "${new_ip_address}"
          - type: xlrelease.StringVariable
            key: host_alias
            requiresValue: false
            value: "${host_alias}"
          - type: xlrelease.StringVariable
            key: new_host_alias
            requiresValue: false
            value: "${new_host_alias}"
          createdReleaseId: "${dnsAddAPIResponse_ID}"
          template: DCS/Individual_Technical_Tasks/DNS-AddRecord
          folder: DCS/Individual_Technical_Tasks
        - name: Add DNS Record Proceed
          type: xlrelease.GateTask
          dependencies:
          - type: xlrelease.Dependency
            targetId: "${dnsAddAPIResponse}"
        - name: API Response Cleanup
          type: xlrelease.ScriptTask
          script: |-
            import json

            dnsAddAPIResponse = releaseVariables.get('dnsAddAPIResponse', '{}')

            if isinstance(dnsAddAPIResponse, basestring):
                try:
                    dnsAddAPIResponse = json.loads(dnsAddAPIResponse)
                except (ValueError, TypeError) as e:
                    print "Error parsing JSON: " + str(e)
                    # Set defaults to avoid further errors
                    dnsAddAPIResponse = {
                        "date": "N/A",
                        "status": "ERROR",
                        "message": "Failed to parse API response",
                        "success": "false"
                    }

            print "Type of dnsAddAPIResponse: " + str(type(dnsAddAPIResponse))
            print "Value of dnsAddAPIResponse: " + str(dnsAddAPIResponse)

            releaseVariables['dnsAddDate'] = dnsAddAPIResponse.get('date', 'N/A')
            releaseVariables['dnsAddStatus'] = dnsAddAPIResponse.get('status', 'ERROR')
            releaseVariables['dnsAddMessage'] = dnsAddAPIResponse.get('message', 'No message available')
            releaseVariables['dnsAddSuccess'] = dnsAddAPIResponse.get('success', 'false')

            is_success = str(releaseVariables['dnsAddSuccess']).lower() == "true"

            if is_success and releaseVariables['dnsAddStatus'] == 'NORMAL':
                releaseVariables['action_proceed'] = True
                dnsAddUpdateMessage = "Adding of DNS record succeeded\n" + \
                                     releaseVariables.get('dnsAddRequestDataRaw', 'No request data available')
            else:
                releaseVariables['action_proceed'] = False
                dnsAddUpdateMessage = "Adding of DNS record failed\n" + \
                                     releaseVariables.get('dnsAddRequestDataRaw', 'No request data available')

            releaseVariables['dnsAddUpdateMessage'] = dnsAddUpdateMessage

            print dnsAddUpdateMessage
        - name: Update JIRA Comment
          type: jira.UpdateIssue
          jiraServer: Jira Dev
          issueId: "${jira_ref}"
          comment: "${dnsAddUpdateMessage}"
    color: '#498500'
  - phase: Post Action Phase
    tasks:
    - name: Check to Proceed
      type: xlrelease.SequentialGroup
      precondition: |-
        if releaseVariables['action_proceed'] == False:
          result = False
        else:
          result = True
      tasks:
      - name: Confirm JIRA set to proceed
        type: jira.GetIssueDetails
        jiraServer: Jira Dev
        issueId: "${jira_ref}"
        variableMapping:
          pythonScript.issueDetails: "${jira_response}"
      - name: Work with JIRA issue
        type: xlrelease.ScriptTask
        script: |-
          releaseVariables['action_proceed'] = False
          releaseVariables['action_cancelled'] = False

          rawdata = releaseVariables['jira_response']
          print rawdata["fields.status.name"]
          var_progress = rawdata["fields.status.name"]

          if var_progress == 'In Progress':
            releaseVariables['action_proceed'] = True
          else:
            releaseVariables['action_proceed'] = False


          if releaseVariables['updateRecord'] == 'true':
              if releaseVariables['dnsRemoveSuccess'] == 'success' and releaseVariables['dnsAddSuccess'] == 'success':
                  dnsUpdateSuccess = True
                  dnsUpdateMessage = "DNS record updated successfully"
                  dnsRequestSuccess = True
                  dnsRequestMessage = "DNS record updated successfully"
              else:
                  dnsUpdateSuccess = False
                  dnsUpdateMessage = "DNS record update failed"
                  dnsRequestSuccess = False
                  dnsRequestMessage = "An error occurred in the DNS request, please log a BMC request for further assistance"
          elif releaseVariables['addRecord'] == 'true':
              if releaseVariables['dnsAddSuccess'] == 'success':
                  dnsAddSuccess = True
                  dnsAddMessage = "DNS record added successfully"
                  dnsRequestSuccess = True
                  dnsRequestMessage = "DNS record added successfully"
              else:
                  dnsAddSuccess = False
                  dnsAddMessage = "DNS record add failed"
                  dnsRequestSuccess = False
                  dnsRequestMessage = "An error occurred in the DNS request, please log a BMC request for further assistance"
          elif releaseVariables['deleteRecord'] == 'true':
              if releaseVariables['dnsRemoveSuccess'] == 'success':
                  dnsRemoveSuccess = True
                  dnsRemoveMessage = "DNS record removed successfully"
                  dnsRequestSuccess = True
                  dnsRequestMessage = "DNS record removed successfully"
              else:
                  dnsRemoveSuccess = False
                  dnsRemoveMessage = "DNS record remove failed"
                  dnsRequestSuccess = False
                  dnsRequestMessage = "An error occurred in the DNS request, please log a BMC request for further assistance"
          else:
              dnsRequestSuccess = False
              dnsRequestMessage = "An error occurred in the DNS request, please log a BMC request for further assistance"

          releaseVariables['dnsRequestSuccess'] = dnsRequestSuccess
          releaseVariables['dnsRequestMessage'] = dnsRequestMessage
      - name: Jira Update Message
        type: xlrelease.ScriptTask
        script: |-
          releaseVariables['dnsRequestSuccess'] = dnsRequestSuccess
          releaseVariables['dnsRequestMessage'] = dnsRequestMessage
      - name: Succeeded
        type: xlrelease.SequentialGroup
        precondition: |-
          if releaseVariables['dnsRequestSuccess'] == False:
            result = False
          else:
            result = True
        tasks:
        - name: Update JIRA Comment
          type: jira.UpdateIssue
          jiraServer: Jira Dev
          issueId: "${jira_ref}"
          newStatus: Closed
          comment: |-
            ${dnsRequestSuccess}
            ${dnsRequestMessage}
      - name: Failed
        type: xlrelease.SequentialGroup
        precondition: |-
          if releaseVariables['dnsRequestSuccess'] == True:
            result = False
          else:
            result = True
        tasks:
        - name: Update JIRA Comment
          type: jira.UpdateIssue
          jiraServer: Jira Dev
          issueId: "${jira_ref}"
          comment: |-
            ${dnsRequestSuccess}
            ${dnsRequestMessage}
    color: '#3d6c9e'
  tags:
  - dns
  - dns_request
  variables:
  - type: xlrelease.StringVariable
    key: request_source
    requiresValue: false
    showOnReleaseStart: false
    label: Source System of Request
    value: Jira
  - type: xlrelease.StringVariable
    key: jira_ref
    requiresValue: false
    label: Jira reference
    description: Jira reference Number
  - type: xlrelease.StringVariable
    key: category
    requiresValue: false
    label: Request Catagory (Add/Remove/Update)
    description: Request Catagory (Add/Remove/Update)
  - type: xlrelease.StringVariable
    key: hostname
    requiresValue: false
    showOnReleaseStart: false
    label: Current Host Name
  - type: xlrelease.StringVariable
    key: host_alias
    requiresValue: false
    showOnReleaseStart: false
    label: Hostname Alias (C)
  - type: xlrelease.StringVariable
    key: new_hostname
    requiresValue: false
    showOnReleaseStart: false
    label: Hostname to be Updated
  - type: xlrelease.StringVariable
    key: new_host_alias
    requiresValue: false
    showOnReleaseStart: false
    label: Host Alias to be Updated
  - type: xlrelease.StringVariable
    key: ip_address
    requiresValue: false
    showOnReleaseStart: false
    label: Current IP Address
  - type: xlrelease.StringVariable
    key: new_ip_address
    requiresValue: false
    showOnReleaseStart: false
    label: IP Address to be Updated
  - type: xlrelease.StringVariable
    key: ptr
    requiresValue: false
    showOnReleaseStart: false
    label: ptr
    value: "Yes"
  - type: xlrelease.StringVariable
    key: app_owner
    requiresValue: false
    showOnReleaseStart: false
    label: Application Owner
  - type: xlrelease.StringVariable
    key: pri_tech_owner
    requiresValue: false
    showOnReleaseStart: false
    label: Primary Tech Owner
  - type: xlrelease.StringVariable
    key: sec_tech_owner
    requiresValue: false
    showOnReleaseStart: false
    label: Secondary Tech Owner
  - type: xlrelease.StringVariable
    key: record_type
    requiresValue: false
    label: Record Type (A-Record/C-Name)
  - type: xlrelease.DateVariable
    key: act_date
    requiresValue: false
    showOnReleaseStart: false
    label: Action Date
    description: Action Date
  - type: xlrelease.StringVariable
    key: pto_id
    requiresValue: false
    showOnReleaseStart: false
    label: Primary Tech Owner ID
    description: Primary Tech Owner ID
  - type: xlrelease.StringVariable
    key: sto_id
    requiresValue: false
    showOnReleaseStart: false
    label: Secondary Tech Owner ID
    description: Secondary Tech Owner ID
  - type: xlrelease.StringVariable
    key: apo_id
    requiresValue: false
    showOnReleaseStart: false
    label: App Owner ID
    description: App Owner ID
  - type: xlrelease.StringVariable
    key: issue_key
    requiresValue: false
    showOnReleaseStart: false
    label: Comment
    description: Comment text to add to the issue
  - type: xlrelease.StringVariable
    key: srv
    requiresValue: false
    showOnReleaseStart: false
    label: Comment
    description: Comment text to add to the issue
  - type: xlrelease.StringVariable
    key: A-Record
    requiresValue: false
    showOnReleaseStart: false
    label: Script
    description: Script
  - type: xlrelease.StringVariable
    key: C-Record
    requiresValue: false
    showOnReleaseStart: false
    label: Script
    description: Script
  - type: xlrelease.StringVariable
    key: updateMessageReceived
    requiresValue: false
    showOnReleaseStart: false
    label: First Call-Back Jira Update Message
    description: Crafted Jira update message
  - type: xlrelease.StringVariable
    key: api_response
    requiresValue: false
    showOnReleaseStart: false
    label: Value
    description: Value of the release variable or default value of the template variable
  - type: xlrelease.StringVariable
    key: raw_api_response
    requiresValue: false
    showOnReleaseStart: false
    label: Value
    description: Value of the release variable or default value of the template variable
  - type: xlrelease.StringVariable
    key: success
    requiresValue: false
    showOnReleaseStart: false
    label: Value
    description: Value of the release variable or default value of the template variable
  - type: xlrelease.StringVariable
    key: removeSuccess
    requiresValue: false
    showOnReleaseStart: false
    label: Value
    description: Value of the release variable or default value of the template variable
  - type: xlrelease.StringVariable
    key: addSuccess
    requiresValue: false
    showOnReleaseStart: false
    label: Value
    description: Value of the release variable or default value of the template variable
  - type: xlrelease.StringVariable
    key: apiAddResponse
    requiresValue: false
    showOnReleaseStart: false
    label: Value
    description: Value of the release variable or default value of the template variable
  - type: xlrelease.StringVariable
    key: raw_apiAddResponse
    requiresValue: false
    showOnReleaseStart: false
    label: Value
    description: Value of the release variable or default value of the template variable
  - type: xlrelease.StringVariable
    key: apiRemoveResponse
    requiresValue: false
    showOnReleaseStart: false
    label: Value
    description: Value of the release variable or default value of the template variable
  - type: xlrelease.StringVariable
    key: raw_apiRemoveResponse
    requiresValue: false
    showOnReleaseStart: false
    label: Value
    description: Value of the release variable or default value of the template variable
  - type: xlrelease.StringVariable
    key: recordFrom
    requiresValue: false
    showOnReleaseStart: false
    label: Comment
    description: Comment text to add to the issue
  - type: xlrelease.StringVariable
    key: recordTo
    requiresValue: false
    showOnReleaseStart: false
    label: Comment
    description: Comment text to add to the issue
  - type: xlrelease.StringVariable
    key: actionDate
    requiresValue: false
    showOnReleaseStart: false
    label: Comment
    description: Comment text to add to the issue
  - type: xlrelease.BooleanVariable
    key: action_proceed
    requiresValue: false
    showOnReleaseStart: false
  - type: xlrelease.MapStringStringVariable
    key: jira_response
    requiresValue: false
    showOnReleaseStart: false
    label: Issue Details
    description: Issue Details
  - type: xlrelease.StringVariable
    key: dnsRemoveAPIResponse
    requiresValue: false
    showOnReleaseStart: false
  - type: xlrelease.StringVariable
    key: dnsAddAPIResponse
    requiresValue: false
    showOnReleaseStart: false
  - type: xlrelease.StringVariable
    key: finalUpdateMessage
    requiresValue: false
    showOnReleaseStart: false
    label: Comment
    description: Comment text to add to the issue
  - type: xlrelease.StringVariable
    key: dnsAddUpdateMessage
    requiresValue: false
    showOnReleaseStart: false
    label: Comment
    description: Comment text to add to the issue
  - type: xlrelease.StringVariable
    key: dnsRemoveUpdateMessage
    requiresValue: false
    showOnReleaseStart: false
    label: Comment
    description: Comment text to add to the issue
  - type: xlrelease.StringVariable
    key: dnsRequestMessage
    requiresValue: false
    showOnReleaseStart: false
    label: Comment
    description: Comment text to add to the issue
  - type: xlrelease.StringVariable
    key: dnsRequestSuccess
    requiresValue: false
    showOnReleaseStart: false
    label: Comment
    description: Comment text to add to the issue
  - type: xlrelease.StringVariable
    key: dnsAddAPIResponse_ID
    requiresValue: false
    showOnReleaseStart: false
  - type: xlrelease.StringVariable
    key: dnsRemoveAPIResponse_ID
    requiresValue: false
    showOnReleaseStart: false
  allowPasswordsInAllFields: true
  disableNotifications: true
  scriptUsername: svcdevslmdcsrelease
  scriptUserPassword: !value "xlrelease_Release_ADS_DNS_Request_scriptUserPassword"
  riskProfile: Default risk profile
  author: G985965
  defaultTargetFolder: Sandbox
