FROM soulteary/cronicle

RUN apk update && apk upgrade --no-cache
RUN apk add --virtual build-deps gcc python3 py3-pip  python3-dev musl-dev linux-headers
RUN apk add --no-cache mariadb-dev mariadb-connector-c-dev
RUN apk add --no-cache libreoffice-base libreoffice-writer

# Set the pip cache directory
ENV PIP_CACHE_DIR=/pip_cache
RUN mkdir -p $PIP_CACHE_DIR
# Upgrade pip
ENV PIP_BREAK_SYSTEM_PACKAGES=1
RUN pip install --upgrade pip
# Copy the requirements file to the container
COPY requirements.txt /requirements.txt
# Install the dependencies from the requirements file
RUN pip install --cache-dir $PIP_CACHE_DIR -r /requirements.txt
