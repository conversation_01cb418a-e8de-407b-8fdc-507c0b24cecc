from app.core.config import config
from app import logging
from app.core.helpers import key_exists, dictval2list, dct2df, recast_json

# Import database modules
import sqlalchemy
from sqlalchemy import create_engine, text
# Import pandas modules
import pandas as pd

read_engine = create_engine(config.SQLALCHEMY_DATABASE_READ_URI, pool_pre_ping=True)
write_engine = create_engine(config.SQLALCHEMY_DATABASE_WRITE_URI, pool_pre_ping=True)

# Check Database Availability
def dbCheck():
    # try:
    qry_db_version = "SELECT VERSION();"
    results = dbQuery(qry_db_version)
    print(results)
    # Check if anything at all is returned
    if results:
        return "Database ready"
    else:
        return "Database unavailable"               


# Execute Query
def dbQuery(query):
    #query = "SELECT * FROM {} WHERE {}".format(table,target)
    # print("QUERY:","\n",query,"\n")
    logging.info("Executing database query...")
    logging.warning(f"QUERY:\n{query}")
    conn = read_engine.connect()
    df_query = pd.read_sql(text(query), con = conn)
    # print(df_query.info())
    df_results = recast_json(df_query)
    # print(df_results.info())
    # print(df_results.head(10))

    conn.close()
    return df_results

# Append records to table
def df2sql(data,table,action):
    # Receive data and upload to database
    logging.info(f"{action} request for table {table} received")
    #print(data)
    data.to_sql(name=table, con=write_engine, if_exists = action, index=False)
    logging.info(f"{action} to table {table} complete")
    return

# Execute Query
def dbUpdateQuery(query):
    # engine = create_engine(config.SQLALCHEMY_DATABASE_URI)
    print("QUERY:","\n",query,"\n")
    conn = write_engine.raw_connection()
    with conn.cursor() as cursor:
        cursor.execute(query)
        conn.commit()
    conn.close()
    return
