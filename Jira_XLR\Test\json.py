import json

datamap = '''
    {
      "asset_owner": "Santam",
      "app_tier": "ATIV",
      "app_type": "Shared",
      "client": "Santam",
      "competency": "BCX CPS",
      "dr_solution": "Backup and Restore",
      "environment": "Production",
      "nics": 1,
      "os_version": "Windows Server 2022",
      "reference": "DCS-1526",
      "requested_by": "",
      "vcpus": 4,
      "vm_count": 1,
      "vm_description": "Server Description: 	CCTV Central Server",
      "vram": 32,
	    "disk1" : {
	    	"tier": "Gold",
	    	"size": "1000",
	    	"drive": "D",
	    	"label": "null"
	    },
	    "disk2" : {
	    	"tier": "null",
	    	"size": "0",
	    	"drive": "null",
	    	"label": "null"
	    },
	    "disk3" : {
	    	"tier": "null",
	    	"size": "0",
	    	"drive": "null",
	    	"label": "null"
	    },
	    "disk4" : {
	    	"tier": "null",
	    	"size": "0",
	    	"drive": "null",
	    	"label": "null"
	    },
	    "disk5" : {
	    	"tier": "null",
	    	"size": "",
	    	"drive": "null",
	    	"label": "null"
	    },
	    "disk6" : {
	    	"tier": "null",
	    	"size": "",
	    	"drive": "null",
	    	"label": "null"
	    }
	}
'''
dataobj = json.loads(datamap)
#print(dataobj)
releaseVariables['autodeploy_api_data'] = dataobj
#print()

# Assign sap_cc variable
sap_cc = "T010010912" 

# Mapping app_tier to dr_critical
app_tier = "ATIV"
if app_tier in ["ATI", "ATII", "ATIII"]:
    dr_critical = "Yes"
elif app_tier in ["ATIV", "ATV"]:
    dr_critical = "No"

releaseVariables['dr_critical'] = dr_critical
releaseVariables['sap_cc'] = sap_cc