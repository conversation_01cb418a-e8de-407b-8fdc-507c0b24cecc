# Packer Environment Configuration
# Copy this file to .env and configure your specific values

# =============================================================================
# PACKER CONFIGURATION
# =============================================================================
PACKER_VERSION=1.14.1
TERRAFORM_VERSION=1.12.2
POWERSHELL_VERSION=7.5.2

# =============================================================================
# AWS CONFIGURATION
# =============================================================================
# AWS Credentials (required for AWS builds)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_DEFAULT_REGION=us-east-1
AWS_SESSION_TOKEN=
AWS_PROFILE=default

# =============================================================================
# AZURE CONFIGURATION (Optional)
# =============================================================================
AZURE_CLIENT_ID=your_azure_client_id
AZURE_CLIENT_SECRET=your_azure_client_secret
AZURE_TENANT_ID=your_azure_tenant_id
AZURE_SUBSCRIPTION_ID=your_azure_subscription_id

# =============================================================================
# VMWARE VSPHERE CONFIGURATION
# =============================================================================
# vCenter Server Details (required for VMware builds)
VCENTER_SERVER=your_vcenter_server.domain.com
VCENTER_USERNAME=your_vcenter_username
VCENTER_PASSWORD=your_vcenter_password
VCENTER_DATACENTER=your_datacenter_name
VCENTER_CLUSTER=your_cluster_name
VCENTER_DATASTORE=your_datastore_name
VCENTER_NETWORK=your_network_name

# =============================================================================
# BUILD CONFIGURATION
# =============================================================================
# Build Platform: vmware, aws, or both
BUILD_PLATFORM=both

# Build Version: 2019, 2022, or both
BUILD_VERSION=both

# Enable debug logging (true/false)
BUILD_DEBUG=false

# Enable parallel builds (true/false)
BUILD_PARALLEL=false

# =============================================================================
# PACKER LOGGING
# =============================================================================
# Packer log level (0=disabled, 1=enabled)
PACKER_LOG=0

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================
# Timezone
TZ=UTC

# Network mode for Docker (host recommended for VMware access)
NETWORK_MODE=host

# =============================================================================
# RESOURCE LIMITS
# =============================================================================
# Memory limits for containers
MEMORY_LIMIT=4G
MEMORY_RESERVATION=2G

# CPU limits for containers
CPU_LIMIT=2.0
CPU_RESERVATION=1.0

# =============================================================================
# PATH CONFIGURATION
# =============================================================================
# Path to ISO files (local directory to mount)
ISO_PATH=./packer/ISO

# Path for packer cache
CACHE_PATH=./packer/packer_cache

# Path for logs
LOGS_PATH=./packer/logs

# =============================================================================
# WINDOWS SERVER CONFIGURATION
# =============================================================================
# Default Windows Server settings (can be overridden in variable files)
WINDOWS_USERNAME=Administrator
WINDOWS_PASSWORD=P@ssw0rd123!

# Windows Update settings
WINDOWS_UPDATE_TIMEOUT=3600
WINDOWS_RESTART_TIMEOUT=600

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================
# VMware network settings
VM_NETWORK_ADAPTER=vmxnet3
VM_DISK_CONTROLLER=pvscsi

# AWS instance settings
AWS_INSTANCE_TYPE=t3.medium
AWS_REGION=us-east-1
AWS_VPC_ID=
AWS_SUBNET_ID=
AWS_SECURITY_GROUP_ID=

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================
# Enable nested virtualization (for Hyper-V builds)
ENABLE_NESTED_VIRTUALIZATION=false

# Custom scripts directory
CUSTOM_SCRIPTS_PATH=./packer/scripts/custom

# Temporary directory for builds
TEMP_DIR=/tmp/packer-builds
