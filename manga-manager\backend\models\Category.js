module.exports = (sequelize, DataTypes) => {
  const Category = sequelize.define('Category', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    icon: {
      type: DataTypes.STRING,
      allowNull: true
    },
    color: {
      type: DataTypes.STRING,
      allowNull: true
    },
    isAdult: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    }
  }, {
    timestamps: true
  });

  Category.associate = (models) => {
    Category.belongsToMany(models.Manga, {
      through: 'MangaCategories',
      foreignKey: 'categoryId',
      as: 'mangas'
    });
    Category.belongsToMany(models.User, {
      through: 'UserCategories',
      foreignKey: 'categoryId',
      as: 'preferredByUsers'
    });
  };

  return Category;
};