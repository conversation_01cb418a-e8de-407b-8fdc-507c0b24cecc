import json

datamap = '''
    {
      "request_source": "${request_source}",
      "jira_ref": "${jira_ref}",
      "hostname": "${hostname}",
      "ip_address": "${ip_address}",
      "app_owner": "${app_owner}",
      "pri_tech_owner": "${pri_tech_owner}",
      "sec_tech_owner": "${sec_tech_owner}",
      "host_alias": "${host_alias}",
      "new_hostname": "${new_hostname}",
      "new_ip_address": "${new_ip_address}",
      "new_host_alias": "${new_host_alias}",
      "record_type": "${record_type}",
      "category": "${category}",
      "server_name":"${hostname}",
      "act_date":"${act_date}"
	}
'''
releaseVariables['dnsRequestDataRaw'] = datamap

dataobj = json.loads(datamap)
releaseVariables['dnsRequestData'] = dataobj