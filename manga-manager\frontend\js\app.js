// Main Application
document.addEventListener('DOMContentLoaded', () => {
  // Initialize modules
  Auth.init();
  UI.init();
  Manga.init();
  Reader.init();
  
  // Check for sidebar collapsed state
  const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
  if (sidebarCollapsed) {
    document.getElementById('sidebar').classList.add('collapsed');
    document.getElementById('content').classList.add('expanded');
  }
  
  // Handle mobile sidebar
  const sidebarClose = document.querySelector('.sidebar-close');
  if (sidebarClose) {
    sidebarClose.addEventListener('click', () => {
      document.getElementById('sidebar').classList.remove('active');
    });
  }
  
  // Handle mobile sidebar toggle
  const sidebarCollapse = document.getElementById('sidebarCollapse');
  if (sidebarCollapse && window.innerWidth < 768) {
    sidebarCollapse.addEventListener('click', () => {
      document.getElementById('sidebar').classList.add('active');
    });
  }
});