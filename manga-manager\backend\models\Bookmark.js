module.exports = (sequelize, DataTypes) => {
  const Bookmark = sequelize.define('Bookmark', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    mangaId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Mangas',
        key: 'id'
      }
    },
    chapterId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Chapters',
        key: 'id'
      }
    },
    pageNumber: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    note: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    type: {
      type: DataTypes.ENUM('reading', 'favorite', 'to-read', 'custom'),
      defaultValue: 'reading'
    }
  }, {
    timestamps: true
  });

  Bookmark.associate = (models) => {
    Bookmark.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    Bookmark.belongsTo(models.Manga, {
      foreignKey: 'mangaId',
      as: 'manga'
    });
    Bookmark.belongsTo(models.Chapter, {
      foreignKey: 'chapterId',
      as: 'chapter'
    });
  };

  return Bookmark;
};