const passport = require('passport');
const LocalStrategy = require('passport-local').Strategy;
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const bcrypt = require('bcrypt');
const { User } = require('../../backend/models');
const speakeasy = require('speakeasy');

// Local Strategy (username/password)
passport.use(new LocalStrategy(
  { usernameField: 'email' },
  async (email, password, done) => {
    try {
      const user = await User.findOne({ where: { email } });
      
      if (!user) {
        return done(null, false, { message: 'Incorrect email.' });
      }
      
      if (!user.password) {
        return done(null, false, { message: 'This account uses Google authentication.' });
      }
      
      const isMatch = await bcrypt.compare(password, user.password);
      
      if (!isMatch) {
        return done(null, false, { message: 'Incorrect password.' });
      }
      
      return done(null, user);
    } catch (error) {
      return done(error);
    }
  }
));

// Google OAuth Strategy
passport.use(new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: process.env.GOOGLE_CALLBACK_URL
}, async (accessToken, refreshToken, profile, done) => {
  try {
    // Find or create user
    let user = await User.findOne({ where: { googleId: profile.id } });
    
    if (!user) {
      // Check if email already exists
      const existingUser = await User.findOne({ where: { email: profile.emails[0].value } });
      
      if (existingUser) {
        // Link Google account to existing user
        existingUser.googleId = profile.id;
        existingUser.profilePicture = profile.photos[0].value;
        await existingUser.save();
        return done(null, existingUser);
      }
      
      // Create new user
      user = await User.create({
        username: profile.displayName,
        email: profile.emails[0].value,
        googleId: profile.id,
        profilePicture: profile.photos[0].value
      });
    }
    
    return done(null, user);
  } catch (error) {
    return done(error);
  }
}));

// Serialize user to session
passport.serializeUser((user, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findByPk(id);
    done(null, user);
  } catch (error) {
    done(error);
  }
});

// MFA verification middleware
const verifyMfa = (req, res, next) => {
  const { user } = req;
  const { token } = req.body;
  
  if (!user.mfaEnabled) {
    return next();
  }
  
  const verified = speakeasy.totp.verify({
    secret: user.mfaSecret,
    encoding: 'base32',
    token
  });
  
  if (!verified) {
    return res.status(401).json({ message: 'Invalid MFA token' });
  }
  
  next();
};

module.exports = { verifyMfa };