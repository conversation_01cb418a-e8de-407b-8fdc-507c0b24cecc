# AWS Image Builder - Optimized Base Images

This folder contains AWS Image Builder configurations for creating Windows Server 2022 base AMIs with .NET Framework 4.8, registry optimizations, and firewall rules for business deployment.

## Overview

This configuration creates an optimized Windows Server 2022 base image with .NET Framework 4.8, performance registry tweaks, and security-focused firewall rules that can be used across multiple businesses. Business-specific configurations are dynamically loaded from S3 during deployment.

## Folder Structure

```
ImageBuilder/
├── components/          # Reusable Image Builder components
│   ├── install-dotnet48.yml
│   ├── configure-registry-tweaks.yml
│   └── configure-firewall-rules.yml
├── configs/             # Business configuration files
│   ├── SPF_PRD.json    # SanlamLife (SPF) production config
│   └── STM_PRD.json    # Santam (STM) production config
├── recipes/             # Image recipes
│   └── windows-server-2022-custom.yml
├── infrastructure/      # Infrastructure configurations
│   └── build-infrastructure.yml
├── distribution/        # Distribution settings
│   └── distribution-config.yml
├── pipelines/          # Complete pipeline definitions
│   └── windows-server-pipeline.yml
├── templates/          # User data templates
│   └── user-data-template.ps1
├── deployment/         # ImageBuilder deployment scripts
│   ├── deploy.ps1
│   ├── cleanup.ps1
│   ├── update-base-ami.ps1
│   └── upload-business-configs.ps1
├── CONSOLE_DEPLOYMENT_GUIDE.md     # Step-by-step console deployment
├── AWS_CLI_DEPLOYMENT_GUIDE.md     # AWS CLI deployment guide
├── BUSINESS_DEPLOYMENT_GUIDE.md    # Business configuration guide
├── WINDOWS_SERVER_2022_IMAGES_EXPLAINED.md # Windows Server image types explained
├── REGISTRY_AND_FIREWALL_CONFIGURATION.md # Registry tweaks and firewall rules explained
└── README.md                       # This file
```

## Business Configurations

### SPF_PRD.json (SanlamLife)
- **Business Unit**: SPF
- **Domain**: mud.internal.co.za
- **Server Roles**: MSSQL-2022, Shared-2022
- **Client Tag**: SPF

### STM_PRD.json (Santam)
- **Business Unit**: STM
- **Domain**: mud.internal.co.za
- **Server Roles**: MSSQL-2022, Shared-2022
- **Client Tag**: STM
## Base Image Approach

This configuration creates an **optimized base image** with essential components:
- Windows Server 2022 with latest updates
- .NET Framework 4.8 (commonly required across businesses)
- Performance registry optimizations
- Security-focused firewall rules
- No business-specific or domain-specific configurations

### Benefits of Optimized Base Image
- **Common Foundation**: .NET 4.8 is required by most business applications
- **Performance Optimized**: Registry tweaks for better server performance
- **Security Hardened**: Firewall rules and security configurations applied
- **Faster Deployment**: Pre-configured optimizations reduce setup time
- **Flexibility**: Can be used across multiple businesses and use cases
- **Maintainability**: Easier to update and maintain than business-specific images

### 📋 **For detailed configuration information, see [REGISTRY_AND_FIREWALL_CONFIGURATION.md](REGISTRY_AND_FIREWALL_CONFIGURATION.md)**

### Dynamic Business Configuration
Business-specific configurations are dynamically loaded from S3 during deployment:
- **S3 Storage**: Configurations stored centrally in S3 bucket
- **Client Tag Based**: Automatic selection based on CLIENT_TAG (SPF, STM, etc.)
- **Runtime Loading**: No hardcoded configurations in user data
- **Easy Updates**: Modify S3 configurations without changing deployment scripts

## Prerequisites

### AWS Requirements
- AWS CLI configured with appropriate permissions
- VPC with private subnet and NAT Gateway for internet access
- Security group allowing outbound HTTPS, HTTP, DNS, and NTP
- S3 bucket for build logs (optional but recommended)
- SNS topic for build notifications (optional)

### IAM Permissions Required
- EC2ImageBuilderInstanceProfile with permissions for:
  - EC2 instance management
  - S3 access for logs and artifacts
  - SSM Parameter Store access (for domain credentials)
  - CloudWatch Logs access

## Quick Start

### Option 1: AWS Console Deployment (Recommended)

📋 **For detailed step-by-step instructions, see [CONSOLE_DEPLOYMENT_GUIDE.md](CONSOLE_DEPLOYMENT_GUIDE.md)**

**Quick Overview:**

1. **Create Components** (if needed):
   - Navigate to **EC2 Image Builder** > **Components** in AWS Console
   - Click **Create component**
   - Copy content from `components/install-dotnet48.yml`
   - Set name: `InstallDotNet48`
   - Set version: `1.0.0`
   - Paste the YAML content and create

2. **Create Image Recipe**:
   - Go to **EC2 Image Builder** > **Image recipes**
   - Click **Create image recipe**
   - Name: `WindowsServer2022Base`
   - Version: `1.0.0`
   - Base image: Select **Windows Server 2022 English Full Base** (latest)
   - Add components:
     - **update-windows** (AWS managed)
     - **InstallDotNet48** (your custom component)
     - **reboot-windows** (AWS managed)

3. **Create Infrastructure Configuration**:
   - Go to **Infrastructure configurations**
   - Click **Create infrastructure configuration**
   - Name: `WindowsServer2022BuildInfrastructure`
   - Instance type: `m5.large` (or larger)
   - Select your VPC, subnet, and security group
   - Create or select IAM instance profile with required permissions

4. **Create Distribution Configuration**:
   - Go to **Distribution configurations**
   - Click **Create distribution configuration**
   - Name: `WindowsServer2022Distribution`
   - Region: `af-south-1`
   - AMI name: `BaseWindowsServer2022-{imagebuilder:buildDate}`
   - Add target accounts if needed

5. **Create Image Pipeline**:
   - Go to **Image pipelines**
   - Click **Create image pipeline**
   - Name: `WindowsServer2022BasePipeline`
   - Select your recipe, infrastructure, and distribution configurations
   - Set schedule if desired (e.g., weekly)

6. **Run Pipeline**:
   - Select your pipeline and click **Run pipeline**
   - Monitor progress in the **Executions** tab

### Option 2: AWS CLI Deployment

📋 **For detailed CLI instructions, see [AWS_CLI_DEPLOYMENT_GUIDE.md](AWS_CLI_DEPLOYMENT_GUIDE.md)**

**Quick Overview:**

1. **Create Components**:
   ```bash
   aws imagebuilder create-component --data file://components/install-dotnet48.yml
   aws imagebuilder create-component --data file://components/configure-registry-tweaks.yml
   aws imagebuilder create-component --data file://components/configure-firewall-rules.yml
   ```

2. **Create Infrastructure and Distribution**:
   ```bash
   aws imagebuilder create-infrastructure-configuration --cli-input-yaml file://infrastructure/build-infrastructure.yml
   aws imagebuilder create-distribution-configuration --cli-input-yaml file://distribution/distribution-config.yml
   ```

3. **Create Recipe and Pipeline**:
   ```bash
   aws imagebuilder create-image-recipe --cli-input-yaml file://recipes/windows-server-2022-custom.yml
   aws imagebuilder create-image-pipeline --cli-input-yaml file://pipelines/windows-server-pipeline.yml
   ```

4. **Start Build**:
   ```bash
   aws imagebuilder start-image-pipeline-execution --image-pipeline-arn "your-pipeline-arn"
   ```

**Automated Deployment:**
```powershell
.\deployment\deploy.ps1 -Region "af-south-1" -SecurityGroupId "sg-xxxxxxxxx" -SubnetId "subnet-xxxxxxxxx" -StartBuild
```

## S3 Configuration Setup

Before deploying instances, upload business configurations to S3:

1. **Upload Configurations**:
   ```powershell
   .\deployment\upload-business-configs.ps1 -S3Bucket "your-config-bucket"
   ```

2. **Configure IAM Permissions**:
   - EC2 instances need `s3:GetObject` permission on the config bucket
   - EC2 instances need `ec2:DescribeTags` permission for tag-based selection

## Deployment with Business Configuration

📋 **For detailed deployment instructions, see [BUSINESS_DEPLOYMENT_GUIDE.md](BUSINESS_DEPLOYMENT_GUIDE.md)**

After the base AMI is created and S3 configurations are uploaded:

1. **Prepare User Data**:
   - Copy `templates/user-data-template.ps1`
   - Update the S3 bucket name in the script

2. **Launch Instance with Client Tag**:
   ```powershell
   # Launch EC2 instance with ClientTag for automatic config selection
   aws ec2 run-instances \
     --image-id ami-xxxxxxxxx \
     --tag-specifications 'ResourceType=instance,Tags=[{Key=ClientTag,Value=SPF}]' \
     --user-data file://your-user-data.ps1
   ```

3. **Available Client Tags**:
   - `SPF`: SanlamLife configuration
   - `STM`: Santam configuration

## EC2 Instance Deployment

After creating the base AMI, use the EC2 deployment scripts for launching business instances:

📋 **See `../EC2-Deployment/` folder for instance deployment scripts:**
- `generate-user-data.ps1`: Generate business-specific user data
- `launch-business-instance.ps1`: Launch instances with business configurations
- `schedule-auto-updates.ps1`: Setup automatic AMI updates on instances

## Automatic Base AMI Updates

### Problem: Base AMI Updates
AWS regularly releases new Windows Server 2022 AMIs with latest patches and updates. Your Image Builder recipe needs to use the latest base AMI to ensure security and compliance.

### Solution Options

#### Option 1: Use AMI Name Pattern (Recommended)
Instead of hardcoding an AMI ID, use the AMI name pattern. Image Builder automatically uses the latest AMI matching the pattern:

```yaml
# In your recipe file
parentImage: Windows_Server-2022-English-Full-Base  # Always uses latest
```

#### Option 2: Automated AMI ID Updates
Use the provided scripts to automatically detect and update to the latest AMI:

```powershell
# Check for updates and create new recipe version if needed
.\deployment\update-base-ami.ps1 -Region "us-east-1" -StartBuild

# Force update regardless of current AMI
.\deployment\update-base-ami.ps1 -Force -StartBuild

# Use AMI name pattern for future automatic updates
.\deployment\update-base-ami.ps1 -UseAMIName -StartBuild
```

#### Option 3: Scheduled Automatic Updates
Set up Windows Task Scheduler to automatically check for and apply base AMI updates:

```powershell
# Set up weekly automatic updates (Sundays at 2 AM)
.\deployment\schedule-auto-updates.ps1 -ScheduleType Weekly -ScheduleDay Sunday -ScheduleTime "02:00" -AutoStartBuild

# Set up daily updates
.\deployment\schedule-auto-updates.ps1 -ScheduleType Daily -ScheduleTime "03:00"

# Remove scheduled updates
.\deployment\schedule-auto-updates.ps1 -RemoveSchedule
```

### How It Works

1. **AMI Name Pattern**: Image Builder queries AWS for the latest AMI matching the pattern
2. **Automatic Detection**: Scripts use AWS CLI to find the newest Windows Server 2022 AMI
3. **Version Management**: Creates new recipe versions automatically
4. **Pipeline Updates**: Updates the pipeline to use the new recipe version
5. **Build Triggering**: Optionally starts a new build with the updated base AMI

### Benefits

- **Security**: Always uses the latest patched base AMI
- **Compliance**: Ensures images meet current security standards
- **Automation**: Reduces manual intervention and human error
- **Consistency**: Standardized update process across environments

## Detailed Deployment Steps

### Step 1: Prepare Your Environment

1. **Update AMI ID**: Find the latest Windows Server 2022 AMI for your region:
   ```powershell
   aws ec2 describe-images --owners amazon --filters "Name=name,Values=Windows_Server-2022-English-Full-Base-*" --query 'Images[*].[ImageId,Name,CreationDate]' --output table
   ```

2. **Create Required IAM Role**:
   ```powershell
   # Create instance profile for Image Builder
   aws iam create-role --role-name EC2ImageBuilderInstanceRole --assume-role-policy-document '{
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Principal": {
           "Service": "ec2.amazonaws.com"
         },
         "Action": "sts:AssumeRole"
       }
     ]
   }'

   # Attach required policies
   aws iam attach-role-policy --role-name EC2ImageBuilderInstanceRole --policy-arn arn:aws:iam::aws:policy/EC2InstanceProfileForImageBuilder
   aws iam attach-role-policy --role-name EC2ImageBuilderInstanceRole --policy-arn arn:aws:iam::aws:policy/SSMInstanceCore

   # Create instance profile
   aws iam create-instance-profile --instance-profile-name EC2ImageBuilderInstanceProfile
   aws iam add-role-to-instance-profile --instance-profile-name EC2ImageBuilderInstanceProfile --role-name EC2ImageBuilderInstanceRole
   ```

3. **Create S3 Bucket for Logs** (Optional):
   ```powershell
   aws s3 mb s3://your-imagebuilder-logs-bucket-unique-name
   ```

### Step 2: Configure Domain Join (Optional)

If you plan to join instances to a domain, store credentials in Parameter Store:

```powershell
# Store domain username
aws ssm put-parameter --name "/aws/imagebuilder/domain/username" --value "DOMAIN\administrator" --type "String"

# Store domain password (encrypted)
aws ssm put-parameter --name "/aws/imagebuilder/domain/password" --value "YourDomainPassword" --type "SecureString"
```

### Step 3: Deploy Image Builder Resources

Use the provided YAML files to create all resources. Make sure to update the placeholder values first:

1. **Update `infrastructure/build-infrastructure.yml`**:
   - Replace `sg-0123456789abcdef0` with your security group ID
   - Replace `subnet-0123456789abcdef0` with your subnet ID
   - Replace `your-key-pair-name` with your EC2 key pair
   - Replace `your-imagebuilder-logs-bucket` with your S3 bucket name

2. **Update `distribution/distribution-config.yml`**:
   - Replace account IDs with your target AWS accounts
   - Add or remove regions as needed

3. **Deploy in Order**:
   ```powershell
   # 1. Create components
   aws imagebuilder create-component --cli-input-yaml file://components/install-dotnet48.yml
   aws imagebuilder create-component --cli-input-yaml file://components/install-bginfo.yml
   aws imagebuilder create-component --cli-input-yaml file://components/domain-join.yml

   # 2. Create infrastructure configuration
   aws imagebuilder create-infrastructure-configuration --cli-input-yaml file://infrastructure/build-infrastructure.yml

   # 3. Create distribution configuration
   aws imagebuilder create-distribution-configuration --cli-input-yaml file://distribution/distribution-config.yml

   # 4. Create image recipe
   aws imagebuilder create-image-recipe --cli-input-yaml file://recipes/windows-server-2022-custom.yml

   # 5. Create pipeline
   aws imagebuilder create-image-pipeline --cli-input-yaml file://pipelines/windows-server-pipeline.yml
   ```

### Step 4: Monitor Build Process

1. **Start Pipeline Execution**:
   ```powershell
   aws imagebuilder start-image-pipeline-execution --image-pipeline-arn "arn:aws:imagebuilder:region:account:image-pipeline/windowsserver2022custompipeline"
   ```

2. **Monitor Progress**:
   ```powershell
   # List pipeline executions
   aws imagebuilder list-image-pipeline-executions --image-pipeline-arn "your-pipeline-arn"

   # Get execution details
   aws imagebuilder get-image --image-build-version-arn "your-image-build-arn"
   ```

3. **Check Logs**:
   - Build logs are available in CloudWatch Logs
   - S3 bucket (if configured) contains detailed build artifacts
   - EC2 console shows the temporary build instance during execution

## Build Process Timeline

The complete build process typically takes 2-4 hours and includes:

1. **Infrastructure Setup** (5-10 minutes)
   - Launch build instance
   - Apply security groups and networking
   - Install Image Builder agent

2. **Component Execution** (60-90 minutes)
   - Install .NET Framework 4.8 (20-30 minutes)
   - Install and configure BGInfo (10-15 minutes)
   - Prepare domain join capabilities (15-20 minutes)
   - Apply Windows updates (30-45 minutes)

3. **Testing Phase** (30-60 minutes)
   - Validate component installations
   - Run built-in Windows tests
   - Verify system functionality

4. **Image Creation** (30-45 minutes)
   - Create AMI snapshot
   - Apply tags and metadata
   - Distribute to target regions/accounts

5. **Cleanup** (5-10 minutes)
   - Terminate build instance
   - Clean up temporary resources

## Troubleshooting

### Common Issues

1. **Build Fails During .NET Installation**:
   - Check internet connectivity from build subnet
   - Verify security group allows outbound HTTPS (443)
   - Review CloudWatch logs for specific error messages

2. **BGInfo Not Displaying**:
   - Verify BGInfo downloaded successfully
   - Check if antivirus is blocking execution
   - Ensure registry entries were created correctly

3. **Domain Join Preparation Fails**:
   - Verify RSAT features are available on Windows Server 2022
   - Check if Windows features installation succeeded
   - Review firewall configuration

4. **Build Instance Access Issues**:
   - Verify IAM role has required permissions
   - Check VPC/subnet configuration
   - Ensure NAT Gateway provides internet access

### Debugging Steps

1. **Enable Detailed Logging**:
   ```powershell
   # Update infrastructure config to keep instance on failure
   # Set terminateInstanceOnFailure: false
   ```

2. **Access Build Instance**:
   - Use Session Manager or RDP to connect to failed build instance
   - Review logs in `C:\ProgramData\Amazon\ImageBuilder\Logs\`
   - Check Windows Event Logs

3. **Component Testing**:
   - Test individual PowerShell scripts manually
   - Verify download URLs are accessible
   - Check registry entries and file installations

## Post-Build Usage

### Launching Instances from Custom AMI

```powershell
# Launch instance using custom AMI
aws ec2 run-instances --image-id ami-your-custom-ami-id --instance-type t3.medium --key-name your-key-pair --security-group-ids sg-your-sg-id --subnet-id subnet-your-subnet-id
```

### Domain Joining After Launch

1. **Using the Provided Script**:
   ```powershell
   # On the launched instance, run:
   C:\Windows\Scripts\Join-Domain.ps1 -DomainName "contoso.com" -Username "contoso\administrator" -Password "YourPassword"
   ```

2. **Using PowerShell Directly**:
   ```powershell
   # Create credential and join domain
   $credential = Get-Credential
   Add-Computer -DomainName "contoso.com" -Credential $credential -Restart
   ```

### Verifying Installation

After launching an instance from your custom AMI:

1. **Check .NET Framework**:
   ```powershell
   Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release
   ```

2. **Verify BGInfo**:
   - Check desktop background for system information
   - Verify startup entries: `Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" -Name BGInfo`

3. **Confirm Domain Join Readiness**:
   ```powershell
   # Check RSAT installation
   Get-WindowsFeature -Name RSAT-AD-PowerShell

   # Verify domain join script
   Test-Path "C:\Windows\Scripts\Join-Domain.ps1"
   ```

## Maintenance and Updates

### Updating Components

1. **Create New Component Version**:
   ```powershell
   # Update version in component YAML file
   aws imagebuilder create-component --cli-input-yaml file://components/install-dotnet48.yml
   ```

2. **Update Recipe**:
   ```powershell
   # Reference new component version in recipe
   aws imagebuilder create-image-recipe --cli-input-yaml file://recipes/windows-server-2022-custom.yml
   ```

3. **Rebuild Pipeline**:
   ```powershell
   aws imagebuilder start-image-pipeline-execution --image-pipeline-arn "your-pipeline-arn"
   ```

### Scheduled Builds

The pipeline is configured to run weekly on Sundays at 2 AM UTC. This ensures:
- Latest Windows updates are included
- Security patches are applied
- Base AMI updates are incorporated

### Cost Optimization

- Build instances are automatically terminated after completion
- Use appropriate instance types (m5.large is usually sufficient)
- Consider using Spot instances for cost savings (not recommended for production)
- Clean up old AMIs periodically to reduce storage costs

## Security Considerations

1. **Network Security**:
   - Use private subnets with NAT Gateway
   - Restrict security group rules to minimum required
   - Enable VPC Flow Logs for monitoring

2. **Access Control**:
   - Use IAM roles instead of access keys
   - Apply least privilege principle
   - Enable CloudTrail for API auditing

3. **Image Security**:
   - Regularly update base AMIs
   - Apply latest security patches
   - Consider using AWS Inspector for vulnerability assessment

4. **Domain Credentials**:
   - Store in SSM Parameter Store with encryption
   - Use dedicated service account with minimal privileges
   - Rotate credentials regularly

## Support and Resources

- **AWS Image Builder Documentation**: https://docs.aws.amazon.com/imagebuilder/
- **Windows Server 2022 Documentation**: https://docs.microsoft.com/en-us/windows-server/
- **Sysinternals BGInfo**: https://docs.microsoft.com/en-us/sysinternals/downloads/bginfo
- **.NET Framework Downloads**: https://dotnet.microsoft.com/download/dotnet-framework
