{"CLIENT": "Sanlam", "BU": "SPF", "APP_TYPE": "shared", "app_settings": {"disk_configuration": {"allocation_unit_size": "4096", "file_system": "NTFS"}, "windows_features": ["IIS-WebServerRole", "IIS-WebServer", "IIS-CommonHttpFeatures"], "firewall_rules": [{"name": "HTTP-In", "port": "80", "protocol": "TCP", "direction": "Inbound"}, {"name": "HTTPS-In", "port": "443", "protocol": "TCP", "direction": "Inbound"}], "registry_settings": [], "services": [{"name": "W3SVC", "startup_type": "Automatic"}]}, "environments": {"PRD": {"ENV": "PRD", "domain": "mud.internal.co.za", "basePath": "OU=Servers,OU=SanlamLife,OU=Businesses,DC=sanlam,DC=co,DC=za", "OS_TYPE": "Windows", "OS_VERSION": ["Server 2022", "Server 2025"], "LOCAL_ADM": ["MUD\\DL-Sanlamlife-TSMSLocalAdmin", "MUD\\DL-Sanlamlife-ADDMDisLocalAdmin", "MUD\\svcbmcadispaccount"], "target_ou": {"2022": "OU=Shared,OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=sanlam,DC=co,DC=za", "2025": "OU=Shared,OU=Server 2025,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=sanlam,DC=co,DC=za", "2019": "OU=Shared,OU=Server 2019,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=sanlam,DC=co,DC=za"}}, "PPE": {"ENV": "PPE", "domain": "ppe.internal.co.za", "basePath": "OU=Servers,OU=SanlamLife,OU=Businesses,DC=ppe,DC=internal,DC=co,DC=za", "OS_TYPE": "Windows", "OS_VERSION": "Server 2022", "DEFAULT_ADM": ["DG-SanlamLife-PPELocalAdmin"], "target_ou": {"2022": "OU=Shared,OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=ppe,DC=internal,DC=co,DC=za"}}, "DEV": {"ENV": "DEV", "domain": "mud.internal.co.za", "basePath": "OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za", "OS_TYPE": "Windows", "OS_VERSION": "Server 2022", "DEFAULT_ADM": ["DG-SanlamLife-DEVLocalAdmin"], "target_ou": {"2022": "OU=Shared,OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"}}}}