const express = require('express');
const { User, Category, ReadingHistory, Manga } = require('../models');
const { isAuthenticated } = require('../../middleware/auth/auth-middleware');
const bcrypt = require('bcrypt');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');

const router = express.Router();

// Get current user profile
router.get('/profile', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password', 'mfaSecret'] },
      include: [
        {
          model: Category,
          as: 'preferredCategories',
          through: { attributes: [] }
        }
      ]
    });
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    res.status(200).json({ user });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update user profile
router.put('/profile', isAuthenticated, async (req, res) => {
  try {
    const { username, email, prefersDarkMode, profilePicture } = req.body;
    
    const user = await User.findByPk(req.user.id);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Update user fields if provided
    if (username) user.username = username;
    if (email) user.email = email;
    if (prefersDarkMode !== undefined) user.prefersDarkMode = prefersDarkMode;
    if (profilePicture) user.profilePicture = profilePicture;
    
    await user.save();
    
    res.status(200).json({
      message: 'Profile updated successfully',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        prefersDarkMode: user.prefersDarkMode,
        profilePicture: user.profilePicture
      }
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Change password
router.put('/password', isAuthenticated, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ message: 'Current password and new password are required' });
    }
    
    const user = await User.findByPk(req.user.id);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Check if current password is correct
    const isMatch = await bcrypt.compare(currentPassword, user.password);
    
    if (!isMatch) {
      return res.status(400).json({ message: 'Current password is incorrect' });
    }
    
    // Hash new password
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(newPassword, salt);
    
    await user.save();
    
    res.status(200).json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Error changing password:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Setup MFA
router.post('/mfa/setup', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Generate new secret
    const secret = speakeasy.generateSecret({
      name: `MangaManager:${user.email}`
    });
    
    // Save secret to user
    user.mfaSecret = secret.base32;
    await user.save();
    
    // Generate QR code
    const qrCode = await QRCode.toDataURL(secret.otpauth_url);
    
    res.status(200).json({
      message: 'MFA setup initiated',
      secret: secret.base32,
      qrCode
    });
  } catch (error) {
    console.error('Error setting up MFA:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Verify and enable MFA
router.post('/mfa/verify', isAuthenticated, async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({ message: 'Token is required' });
    }
    
    const user = await User.findByPk(req.user.id);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    if (!user.mfaSecret) {
      return res.status(400).json({ message: 'MFA not set up' });
    }
    
    // Verify token
    const verified = speakeasy.totp.verify({
      secret: user.mfaSecret,
      encoding: 'base32',
      token
    });
    
    if (!verified) {
      return res.status(400).json({ message: 'Invalid token' });
    }
    
    // Enable MFA
    user.mfaEnabled = true;
    await user.save();
    
    res.status(200).json({ message: 'MFA enabled successfully' });
  } catch (error) {
    console.error('Error verifying MFA:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Disable MFA
router.post('/mfa/disable', isAuthenticated, async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(400).json({ message: 'Token is required' });
    }
    
    const user = await User.findByPk(req.user.id);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    if (!user.mfaEnabled) {
      return res.status(400).json({ message: 'MFA not enabled' });
    }
    
    // Verify token
    const verified = speakeasy.totp.verify({
      secret: user.mfaSecret,
      encoding: 'base32',
      token
    });
    
    if (!verified) {
      return res.status(400).json({ message: 'Invalid token' });
    }
    
    // Disable MFA
    user.mfaEnabled = false;
    user.mfaSecret = null;
    await user.save();
    
    res.status(200).json({ message: 'MFA disabled successfully' });
  } catch (error) {
    console.error('Error disabling MFA:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get reading history
router.get('/history', isAuthenticated, async (req, res) => {
  try {
    const history = await ReadingHistory.findAll({
      where: { userId: req.user.id },
      include: [
        {
          model: Manga,
          as: 'manga'
        }
      ],
      order: [['lastRead', 'DESC']],
      limit: 20
    });
    
    res.status(200).json({ history });
  } catch (error) {
    console.error('Error fetching reading history:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Clear reading history
router.delete('/history', isAuthenticated, async (req, res) => {
  try {
    await ReadingHistory.destroy({
      where: { userId: req.user.id }
    });
    
    res.status(200).json({ message: 'Reading history cleared' });
  } catch (error) {
    console.error('Error clearing reading history:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;