{"listeners": {"0.0.0.0:8080": {"pass": "routes/main"}, "0.0.0.0:8081": {"pass": "routes/status"}}, "routes": {"main": [{"match": {"uri": "/static/*"}, "action": {"share": "/opt/netbox/netbox${uri}"}}, {"action": {"pass": "applications/netbox"}}], "status": [{"match": {"uri": "/status/*"}, "action": {"proxy": "http://unix:/opt/unit/unit.sock"}}]}, "applications": {"netbox": {"type": "python 3", "path": "/opt/netbox/netbox/", "module": "netbox.wsgi", "home": "/opt/netbox/venv", "processes": {"max": 4, "spare": 1, "idle_timeout": 120}}}, "access_log": "/dev/stdout"}