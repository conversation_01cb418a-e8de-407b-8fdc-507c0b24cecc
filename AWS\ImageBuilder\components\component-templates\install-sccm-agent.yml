# AWS Image Builder Component: Install SCCM Client Agent
# This component installs the Microsoft System Center Configuration Manager (SCCM) client agent on Windows Server
# Prerequisites: SCCM infrastructure and client installation files
# Required Environment Variables (via AWS Systems Manager Parameter Store):
# SCCM_SITE_CODE - Your SCCM site code (e.g., "P01")
# SCCM_MANAGEMENT_POINT - FQDN of your SCCM management point server
# SCCM_INSTALLER_URL - URL to download ccmsetup.exe (optional, uses default if not provided)
# SCCM_INSTALL_PROPERTIES - Additional installation properties (optional)

name: win-server-sccm-agent
description: Install Microsoft SCCM client agent on Windows Server
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: CheckExistingSCCM
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
            commands:
              - |
                Write-Host "Checking for existing SCCM client installation..."

                # Check if SCCM client service exists
                $sccmService = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
                if ($sccmService) {
                    Write-Host "SCCM client service found: $($sccmService.Status)"
                    
                    # Check SCCM client version
                    try {
                        $sccmClient = Get-WmiObject -Namespace "root\ccm" -Class "SMS_Client" -ErrorAction SilentlyContinue
                        if ($sccmClient) {
                            Write-Host "SCCM client version: $($sccmClient.ClientVersion)"
                            Write-Host "SCCM client already installed and configured"
                            exit 0
                        }
                    } catch {
                        Write-Host "SCCM client service exists but WMI namespace not accessible"
                    }
                } else {
                    Write-Host "SCCM client not found. Proceeding with installation..."
                }

      - name: ValidateEnvironmentVariables
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating required environment variables..."

                $siteCode = $env:SCCM_SITE_CODE
                $managementPoint = $env:SCCM_MANAGEMENT_POINT
                $installerUrl = $env:SCCM_INSTALLER_URL
                $installProperties = $env:SCCM_INSTALL_PROPERTIES

                if (-not $siteCode) {
                    Write-Error "SCCM_SITE_CODE environment variable is required"
                    exit 1
                }

                if (-not $managementPoint) {
                    Write-Error "SCCM_MANAGEMENT_POINT environment variable is required"
                    exit 1
                }

                Write-Host "Site Code: $siteCode"
                Write-Host "Management Point: $managementPoint"

                if ($installerUrl) {
                    Write-Host "Custom installer URL provided: $installerUrl"
                } else {
                    Write-Host "ℹ Using default installer location"
                }

                if ($installProperties) {
                    Write-Host "Additional install properties: $installProperties"
                }

      - name: DownloadSCCMInstaller
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Preparing SCCM client installer..."

                $tempDir = "C:\temp\sccm"
                $installerPath = "$tempDir\ccmsetup.exe"
                $installerUrl = $env:SCCM_INSTALLER_URL

                # Create temp directory
                New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
                Write-Host "Created temp directory: $tempDir"

                if ($installerUrl) {
                    Write-Host "Downloading SCCM installer from: $installerUrl"
                    try {
                        Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath -UseBasicParsing
                        Write-Host "Downloaded installer successfully"
                    } catch {
                        Write-Error "Failed to download SCCM installer: $($_.Exception.Message)"
                        exit 1
                    }
                } else {
                    # Try to find ccmsetup.exe in common locations
                    $commonPaths = @(
                        "\\$($env:SCCM_MANAGEMENT_POINT)\SMS_$($env:SCCM_SITE_CODE)\Client\ccmsetup.exe",
                        "\\$($env:SCCM_MANAGEMENT_POINT)\SMSPKGC$\ccmsetup.exe",
                        "\\$($env:SCCM_MANAGEMENT_POINT)\Client\ccmsetup.exe"
                    )
                    
                    $found = $false
                    foreach ($path in $commonPaths) {
                        Write-Host "Checking: $path"
                        if (Test-Path $path) {
                            Write-Host "Found installer at: $path"
                            Copy-Item -Path $path -Destination $installerPath -Force
                            $found = $true
                            break
                        }
                    }
                    
                    if (-not $found) {
                        Write-Error "Could not locate ccmsetup.exe. Please provide SCCM_INSTALLER_URL or ensure installer is accessible via UNC path."
                        exit 1
                    }
                }

                # Verify installer file
                if (!(Test-Path $installerPath)) {
                    Write-Error "SCCM installer not found at: $installerPath"
                    exit 1
                }

                $fileSize = (Get-Item $installerPath).Length
                if ($fileSize -lt 1MB) {
                    Write-Error "Downloaded file appears to be invalid (size: $fileSize bytes)"
                    exit 1
                }

                Write-Host "SCCM installer ready: $installerPath ($([math]::Round($fileSize/1MB, 2)) MB)"

      - name: InstallSCCMClient
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Installing SCCM client..."

                $tempDir = "C:\temp\sccm"
                $installerPath = "$tempDir\ccmsetup.exe"
                $siteCode = $env:SCCM_SITE_CODE
                $managementPoint = $env:SCCM_MANAGEMENT_POINT
                $installProperties = $env:SCCM_INSTALL_PROPERTIES

                # Build installation arguments
                $arguments = @(
                    "/mp:$managementPoint",
                    "SMSSITECODE=$siteCode"
                )

                # Add additional properties if provided
                if ($installProperties) {
                    $additionalProps = $installProperties.Split(' ')
                    $arguments += $additionalProps
                }

                # Add common installation parameters
                $arguments += @(
                    "/UsePKICert",
                    "/NoCRLCheck",
                    "/ForceInstall"
                )

                $argumentString = $arguments -join ' '
                Write-Host "Installation command: $installerPath $argumentString"
                Write-Host "Starting SCCM client installation..."
                Write-Host "This may take several minutes..."

                try {
                    $process = Start-Process -FilePath $installerPath -ArgumentList $argumentString -Wait -PassThru -NoNewWindow
                    
                    Write-Host "Installation process completed with exit code: $($process.ExitCode)"
                    
                    # SCCM client installation exit codes
                    switch ($process.ExitCode) {
                        0 { Write-Host "Installation completed successfully" }
                        7 { Write-Host "Installation completed successfully (restart required)" }
                        1602 { Write-Error "Installation cancelled by user"; exit 1 }
                        1603 { Write-Error "Fatal error during installation"; exit 1 }
                        1618 { Write-Error "Another installation is in progress"; exit 1 }
                        1619 { Write-Error "Installation package could not be opened"; exit 1 }
                        default { 
                            Write-Warning "Installation completed with exit code: $($process.ExitCode)"
                            # Don't fail on unknown exit codes as SCCM might still install successfully
                        }
                    }
                } catch {
                    Write-Error "Failed to start SCCM installation: $($_.Exception.Message)"
                    exit 1
                }

      - name: WaitForServiceInitialization
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Waiting for SCCM client service initialization..."

                $maxWaitTime = 300  # 5 minutes
                $waitInterval = 10  # 10 seconds
                $elapsedTime = 0

                while ($elapsedTime -lt $maxWaitTime) {
                    $sccmService = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
                    
                    if ($sccmService) {
                        Write-Host "SCCM service found: $($sccmService.Status)"
                        
                        if ($sccmService.Status -eq "Running") {
                            Write-Host "SCCM service is running"
                            break
                        } elseif ($sccmService.Status -eq "Stopped") {
                            Write-Host "Starting SCCM service..."
                            try {
                                Start-Service -Name "CcmExec" -ErrorAction Stop
                                Write-Host "SCCM service started"
                                break
                            } catch {
                                Write-Warning "Failed to start SCCM service: $($_.Exception.Message)"
                            }
                        }
                    } else {
                        Write-Host "Waiting for SCCM service to be created..."
                    }
                    
                    Start-Sleep -Seconds $waitInterval
                    $elapsedTime += $waitInterval
                    Write-Host "Elapsed time: $elapsedTime seconds"
                }

                if ($elapsedTime -ge $maxWaitTime) {
                    Write-Warning "Timeout waiting for SCCM service initialization"
                }

      - name: VerifyInstallationAndCleanup
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Verifying SCCM client installation..."

                # Check service
                $sccmService = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
                if (!$sccmService) {
                    Write-Error "SCCM service not found after installation"
                    exit 1
                }

                Write-Host "SCCM service status: $($sccmService.Status)"

                # Check WMI namespace (may take time to initialize)
                $retryCount = 0
                $maxRetries = 6
                $wmiAvailable = $false

                while ($retryCount -lt $maxRetries -and !$wmiAvailable) {
                    try {
                        $sccmClient = Get-WmiObject -Namespace "root\ccm" -Class "SMS_Client" -ErrorAction Stop
                        if ($sccmClient) {
                            Write-Host "SCCM WMI namespace accessible"
                            Write-Host "Client version: $($sccmClient.ClientVersion)"
                            $wmiAvailable = $true
                        }
                    } catch {
                        $retryCount++
                        if ($retryCount -lt $maxRetries) {
                            Write-Host "WMI namespace not ready, retrying in 30 seconds... (attempt $retryCount/$maxRetries)"
                            Start-Sleep -Seconds 30
                        }
                    }
                }

                if (!$wmiAvailable) {
                    Write-Warning "SCCM WMI namespace not accessible yet (this may be normal during initial setup)"
                }

                # Clean up temporary files
                Write-Host "Cleaning up temporary files..."
                Remove-Item -Path "C:\temp\sccm" -Recurse -Force -ErrorAction SilentlyContinue
                Write-Host "Cleanup completed"

  - name: validate
    steps:
      - name: ValidateSCCMInstallation
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating SCCM client installation..."

                # Check service exists and is running
                $sccmService = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
                if (!$sccmService) {
                    Write-Error "SCCM client validation failed: Service not found"
                    exit 1
                }

                if ($sccmService.Status -ne "Running") {
                    Write-Error "SCCM client validation failed: Service not running (Status: $($sccmService.Status))"
                    exit 1
                }

                # Check if client is registered with site
                try {
                    $sccmClient = Get-WmiObject -Namespace "root\ccm" -Class "SMS_Client" -ErrorAction SilentlyContinue
                    if ($sccmClient) {
                        Write-Host "SCCM client version: $($sccmClient.ClientVersion)"

                        # Get site assignment
                        $siteInfo = Get-WmiObject -Namespace "root\ccm" -Class "SMS_Authority" -ErrorAction SilentlyContinue
                        if ($siteInfo) {
                            Write-Host "Assigned to site: $($siteInfo.Name)"
                        }
                    }
                } catch {
                    Write-Warning "WMI validation not available yet (normal during initial setup)"
                }

                Write-Host "SCCM client agent validation completed successfully"
                Write-Host "Note: Full client functionality may take additional time to initialize"
