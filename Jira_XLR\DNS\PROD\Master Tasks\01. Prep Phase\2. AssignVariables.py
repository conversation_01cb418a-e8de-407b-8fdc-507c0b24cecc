import json
from datetime import datetime
from java.text import SimpleDateFormat
from java.util import TimeZone

if releaseVariables["category"] == "Update Record":
    deleteRecord = True
    addRecord = True
    updateRecord = True
elif releaseVariables["category"] == "Add Record":
    deleteRecord = False
    addRecord = True
    updateRecord = False
elif releaseVariables["category"] == "Remove Record":
    deleteRecord = True
    addRecord = False
    updateRecord = False

releaseVariables["updateRecord"] = updateRecord
releaseVariables["addRecord"] = addRecord
releaseVariables["deleteRecord"] = deleteRecord

server_name, domain = releaseVariables["hostname"].split('.', 1)
releaseVariables["server_name"] = server_name
releaseVariables["domain"] = domain

if not releaseVariables['dnsRequestDataRaw']:
    releaseVariables['action_proceed'] = False