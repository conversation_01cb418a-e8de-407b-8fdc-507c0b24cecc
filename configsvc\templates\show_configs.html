{% extends "base.html" %}
{% block content %}
    <p class="text-danger"><b>{{ response['messages'] }}</b></p>

    <!-- Tabs navs  data-mdb-tab-init   -->

    <ul class="nav nav-tabs">
        <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#deployment_config" aria-current="page"><b>Deployment</b></a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#hosting_config" aria-current="page"><b>Hosting</b></a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#ipam_config" ><b>IPAM</b></a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#os_config" ><b>OS Config</b></a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#ad_config" ><b>Active Directory</b></a></li>
      </ul>
    <!-- Tabs navs -->

    <div class="tab-content" id="config_requests">
        <p></p>
        <div class="tab-pane fade show active" id="deployment_config" role="tabpanel" aria-labelledby="tab_review_deployment">

            <p class="text-info"><b>{{ response['data']['deployment']['messages'] }}</b></p>
            <table class="table">
                <thead>
                    <tr>
                        <th scope="col">Item</th> <th scope="col">Detail</th>            
                    </tr>
                </thead>
                <tbody>    
                   {% for key, value in response['data']['deployment']['data'].items() %}
                    <tr>
                        <td><b>{{ key }}</b></td>  
                        <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <p></p>
        <div class="tab-pane fade show" id="hosting_config" role="tabpanel" aria-labelledby="tab_review_quote">

            <p class="text-info"><b>{{ response['data']['hosting']['messages'] }}</b></p>
            <table class="table">
                    <thead>
                        <tr>
                            {% for key in response['data']['hosting']['data'][0] %}
                                <th scope="col"> {{ key }} </th>        
                            {% endfor %}  
                        </tr>
                    </thead>
                    <tbody>                
                        
                        {% for i in range (0, response['data']['hosting']['data']|length) %}
                        <tr>
                            {% for key, value in response['data']['hosting']['data'][i].items() %}
                                <th class="font-weight-light" scope="col"> {{ value }} </th>        
                            {% endfor %}     
                        </tr>  
                        {% endfor %}  
                    </tbody>
            </table>
        </div>

        <div class="tab-pane fade show" id="ipam_config" role="tabpanel" aria-labelledby="tab_ipam_config">

            <p class="text-info"><b>{{ response['data']['ipconfig']['messages'] }}</b></p>
            <table class="table">
                <thead>
                    <tr>
                        <th scope="col">Item</th> <th scope="col">Detail</th>            
                    </tr>
                </thead>
                <tbody>    
                   {% for key, value in response['data']['ipconfig']['data'].items() %}
                    <tr>
                        <td><b>{{ key }}</b></td>  
                        <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="tab-pane fade show" id="os_config" role="tabpanel" aria-labelledby="tab_os_config">

            <p class="text-info"><b>{{ response['data']['osconfig']['messages'] }}</b></p>
            <table class="table">
                <thead>
                    <tr>
                        <th scope="col">Item</th> <th scope="col">Detail</th>            
                    </tr>
                </thead>
                <tbody>    
                   {% for key, value in response['data']['osconfig']['data'].items() %}
                    <tr>
                        <td><b>{{ key }}</b></td>  
                        <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <!-- Portrait view -->
        <div class="tab-pane fade" id="ad_config" role="tabpanel" aria-labelledby="tab_review_request">
            
            <table class="table">
                <thead>
                    <tr>
                        <th scope="col">Item</th> <th scope="col">Detail</th>            
                    </tr>
                </thead>
                <tbody>            
                    <tr>
                        <td><b>Key</b></td>  <td>Value</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <!-- Tabs content -->


{% endblock %}


   