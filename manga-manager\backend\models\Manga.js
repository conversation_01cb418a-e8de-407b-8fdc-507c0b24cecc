module.exports = (sequelize, DataTypes) => {
  const Manga = sequelize.define('Manga', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false
    },
    originalTitle: {
      type: DataTypes.STRING,
      allowNull: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    author: {
      type: DataTypes.STRING,
      allowNull: true
    },
    artist: {
      type: DataTypes.STRING,
      allowNull: true
    },
    coverImage: {
      type: DataTypes.STRING,
      allowNull: true
    },
    isExplicit: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    status: {
      type: DataTypes.ENUM('ongoing', 'completed', 'hiatus', 'cancelled'),
      defaultValue: 'ongoing'
    },
    releaseYear: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    filePath: {
      type: DataTypes.STRING,
      allowNull: true
    },
    fileFormat: {
      type: DataTypes.ENUM('cbr', 'cbz', 'pdf', 'folder'),
      allowNull: true
    },
    totalPages: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    uploadedBy: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    source: {
      type: DataTypes.STRING,
      allowNull: true
    },
    metadataLastUpdated: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    timestamps: true
  });

  Manga.associate = (models) => {
    Manga.belongsToMany(models.Category, {
      through: 'MangaCategories',
      foreignKey: 'mangaId',
      as: 'categories'
    });
    Manga.hasMany(models.Chapter, {
      foreignKey: 'mangaId',
      as: 'chapters'
    });
    Manga.hasMany(models.Bookmark, {
      foreignKey: 'mangaId',
      as: 'bookmarks'
    });
    Manga.hasMany(models.ReadingHistory, {
      foreignKey: 'mangaId',
      as: 'readingHistory'
    });
    Manga.belongsTo(models.User, {
      foreignKey: 'uploadedBy',
      as: 'uploader'
    });
  };

  return Manga;
};