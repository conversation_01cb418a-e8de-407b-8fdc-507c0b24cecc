# SCCM Agent Installation Component

This AWS Image Builder component installs the Microsoft System Center Configuration Manager (SCCM) client agent on Windows Server instances.

## Overview

The `install-sccm-agent.yml` component provides a comprehensive solution for installing and configuring the SCCM client agent during the image building process. It includes proper error handling, validation, and cleanup procedures.

## Prerequisites

1. **SCCM Infrastructure**: A functioning SCCM environment with:
   - SCCM Management Point server accessible from the build environment
   - SCCM site configured and operational
   - Network connectivity between ImageBuilder instances and SCCM infrastructure

2. **Installation Files**: Access to `ccmsetup.exe` either via:
   - Direct download URL (recommended for cloud environments)
   - UNC path to SCCM server shares
   - Pre-staged installation files

3. **Network Access**: Ensure ImageBuilder build instances can reach:
   - SCCM Management Point server
   - SCCM distribution points (if using UNC paths)
   - Download URLs (if using direct download)

## Required Environment Variables

Configure these environment variables in AWS Systems Manager Parameter Store:

### Required Variables

- **SCCM_SITE_CODE**: Your SCCM site code (e.g., "P01", "LAB", "PRD")
- **SCCM_MANAGEMENT_POINT**: FQDN of your SCCM management point server (e.g., "sccm-mp.domain.com")

### Optional Variables

- **SCCM_INSTALLER_URL**: Direct URL to download ccmsetup.exe (recommended for cloud deployments)
- **SCCM_INSTALL_PROPERTIES**: Additional installation properties (space-separated)

## Usage Examples

### Basic Usage

Add the component to your ImageBuilder recipe:

```yaml
components:
  - name: win-server-sccm-agent
    parameters: []
```

### With Custom Properties

```yaml
components:
  - name: win-server-sccm-agent
    parameters:
      - name: SCCM_INSTALL_PROPERTIES
        value: "CCMLOGMAXSIZE=5242880 CCMLOGMAXHISTORY=3"
```

## Environment Variable Configuration

### Using AWS Systems Manager Parameter Store

Create parameters in Parameter Store:

```bash
# Required parameters
aws ssm put-parameter \
    --name "/imagebuilder/sccm/site-code" \
    --value "P01" \
    --type "String" \
    --description "SCCM Site Code"

aws ssm put-parameter \
    --name "/imagebuilder/sccm/management-point" \
    --value "sccm-mp.yourdomain.com" \
    --type "String" \
    --description "SCCM Management Point FQDN"

# Optional parameters
aws ssm put-parameter \
    --name "/imagebuilder/sccm/installer-url" \
    --value "https://your-s3-bucket.s3.amazonaws.com/sccm/ccmsetup.exe" \
    --type "String" \
    --description "SCCM Installer Download URL"

aws ssm put-parameter \
    --name "/imagebuilder/sccm/install-properties" \
    --value "CCMLOGMAXSIZE=5242880 CCMLOGMAXHISTORY=3" \
    --type "String" \
    --description "Additional SCCM Installation Properties"
```

### Infrastructure Configuration

Reference these parameters in your ImageBuilder infrastructure configuration:

```yaml
# infrastructure/build-infrastructure.yml
instanceProfileName: EC2InstanceProfileForImageBuilder
environmentVariables:
  - name: SCCM_SITE_CODE
    value: "{{ssm:/imagebuilder/sccm/site-code}}"
  - name: SCCM_MANAGEMENT_POINT
    value: "{{ssm:/imagebuilder/sccm/management-point}}"
  - name: SCCM_INSTALLER_URL
    value: "{{ssm:/imagebuilder/sccm/installer-url}}"
  - name: SCCM_INSTALL_PROPERTIES
    value: "{{ssm:/imagebuilder/sccm/install-properties}}"
```

## Installation Process

The component performs the following steps:

1. **Pre-installation Check**: Verifies if SCCM client is already installed
2. **Environment Validation**: Checks required environment variables
3. **Installer Acquisition**: Downloads or locates ccmsetup.exe
4. **Installation**: Runs SCCM client installation with proper parameters
5. **Service Initialization**: Waits for SCCM service to start
6. **Verification**: Validates installation success
7. **Cleanup**: Removes temporary files
8. **Final Validation**: Confirms SCCM client is operational

## Installation Parameters

The component automatically configures these installation parameters:

- `/mp:` - Management Point server
- `SMSSITECODE=` - Site code assignment
- `/UsePKICert` - Use PKI certificates if available
- `/NoCRLCheck` - Skip certificate revocation list check
- `/ForceInstall` - Force installation even if client exists

Additional parameters can be provided via `SCCM_INSTALL_PROPERTIES`.

## Common Installation Properties

You can customize the installation using `SCCM_INSTALL_PROPERTIES`:

```
CCMLOGMAXSIZE=5242880          # Maximum log file size (5MB)
CCMLOGMAXHISTORY=3             # Number of log files to keep
CCMHTTPPORT=80                 # HTTP port for client communication
CCMHTTPSPORT=443               # HTTPS port for client communication
CCMFIRSTCERT=1                 # Use first available certificate
RESETKEYINFORMATION=TRUE       # Reset key information
CCMALWAYSINF=1                 # Always use internet-facing settings
```

## Troubleshooting

### Common Issues

1. **Network Connectivity**: Ensure ImageBuilder instances can reach SCCM infrastructure
2. **Installer Access**: Verify ccmsetup.exe is accessible via provided URL or UNC path
3. **Permissions**: Ensure proper permissions for SCCM client installation
4. **Site Assignment**: Verify site code and management point are correct

### Log Locations

- ImageBuilder logs: Available in AWS CloudWatch Logs
- SCCM client logs: `C:\Windows\CCM\Logs\` (after installation)
- Installation logs: `C:\Windows\ccmsetup\Logs\`

### Validation Commands

After image creation, you can validate SCCM installation:

```powershell
# Check service status
Get-Service -Name "CcmExec"

# Check client version
Get-WmiObject -Namespace "root\ccm" -Class "SMS_Client" | Select-Object ClientVersion

# Check site assignment
Get-WmiObject -Namespace "root\ccm" -Class "SMS_Authority" | Select-Object Name
```

## Security Considerations

1. **Parameter Store**: Use SecureString parameters for sensitive data
2. **IAM Permissions**: Ensure minimal required permissions for ImageBuilder role
3. **Network Security**: Use security groups to restrict SCCM communication
4. **Certificate Management**: Configure PKI certificates for secure communication

## Integration with Existing Recipes

Add this component to your existing Windows Server recipes:

```yaml
# recipes/windows-server-2022-custom.yml
components:
  - name: update-windows
    parameters: []
  - name: InstallDotNet48
    parameters: []
  - name: win-server-sccm-agent  # Add SCCM agent installation
    parameters: []
  - name: ConfigureRegistryTweaks
    parameters: []
  - name: reboot-windows
    parameters: []
```

## Support

For issues related to:
- **Component functionality**: Check ImageBuilder logs and component validation
- **SCCM configuration**: Consult SCCM documentation and logs
- **Network connectivity**: Verify security groups and network ACLs
- **Parameter configuration**: Validate Systems Manager Parameter Store values
