$headers = New-Object "System.Collections.Generic.Dictionary[[String],[String]]"
$headers.Add("Content-Type", "application/json")

$bodyObject = @{
    key = "18adecad-df09-47e2-ae05-de7686ae3156"
    Script = "Test-ADObject.ps1"
    param = "-jobId 'DCS-Test' -objectName 'Server-Name' -objectDescription 'Server-Description' -vmOS 'Windows' -domain 'mud.internal.co.za' -ouPath 'OU=Test,DC=mud,DC=internal,DC=co,DC=za' -appType 'Shared' -clusterNodes 'srv-01,srv-02'"
}

$body = $bodyObject | ConvertTo-Json
$response = Invoke-RestMethod 'http://srv009484.mud.internal.co.za:8080/webhook/v1' -Method 'POST' -Headers $headers -Body $body
$response | ConvertTo-Json