# AWS Image Builder Component: Install CrowdStrike Falcon Agent
# This component installs the CrowdStrike Falcon agent on Windows Server
# Prerequisites: CrowdStrike Customer ID (CID) and installer package
# Required Environment Variables (via AWS Systems Manager Parameter Store):
# CROWDSTRIKE_CID - Your CrowdStrike customer ID
# CROWDSTRIKE_INSTALLER_URL - Custom URL for installer

name: win-server-crowdstrike-agent
description: Install CrowdStrike Falcon agent on Windows Server
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: InstallCrowdStrike
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                $csService = Get-Service -Name "CSFalconService" -ErrorAction SilentlyContinue
                if ($csService) { Write-Host "CrowdStrike already installed"; exit 0 }

                $cid = $env:CROWDSTRIKE_CID
                $installerUrl = $env:CROWDSTRIKE_INSTALLER_URL

                if (-not $cid) { Write-Error "CROWDSTRIKE_CID required"; exit 1 }
                if (-not $installerUrl) { Write-Error "CROWDSTRIKE_INSTALLER_URL required"; exit 1 }

                $tempDir = "C:\temp\crowdstrike"
                $installerPath = "$tempDir\WindowsSensor.exe"

                New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
                Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath -UseBasicParsing

                if ((Get-Item $installerPath).Length -lt 1MB) { Write-Error "Download failed"; exit 1 }

                $process = Start-Process -FilePath $installerPath -ArgumentList "/install","/quiet","/norestart","CID=$cid" -Wait -PassThru -NoNewWindow
                if ($process.ExitCode -ne 0) { Write-Error "Installation failed: $($process.ExitCode)"; exit 1 }

      - name: VerifyAndCleanup
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Start-Sleep -Seconds 10

                $csService = Get-Service -Name "CSFalconService" -ErrorAction SilentlyContinue
                if (!$csService) { Write-Error "CrowdStrike service not found"; exit 1 }

                if ($csService.Status -ne "Running") {
                    Start-Service -Name "CSFalconService" -ErrorAction SilentlyContinue
                }

                Remove-Item -Path "C:\temp\crowdstrike" -Recurse -Force -ErrorAction SilentlyContinue

  - name: validate
    steps:
      - name: ValidateService
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                $csService = Get-Service -Name "CSFalconService" -ErrorAction SilentlyContinue
                if (!$csService -or $csService.Status -ne "Running") {
                    Write-Error "CrowdStrike validation failed"; exit 1
                }
                Write-Host "CrowdStrike Falcon agent validated successfully"
