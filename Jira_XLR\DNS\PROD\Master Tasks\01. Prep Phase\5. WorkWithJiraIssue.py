from datetime import datetime, timedelta
from java.util import Calendar, Date
from java.text import SimpleDateFormat

rawdata = releaseVariables['jira_response']
# Access nested fields properly
scheduledDate = rawdata["fields.customfield_21260"]
print scheduledDate

dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ")
scheduledDate = dateFormat.parse(scheduledDate)

releaseVariables["scheduledDate"] = scheduledDate
print scheduledDate