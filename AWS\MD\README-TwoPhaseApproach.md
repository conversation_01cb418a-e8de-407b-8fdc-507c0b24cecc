# Two-Phase User-Data Approach

This document explains the two distinct user-data scripts used in our AWS infrastructure deployment process.

## Overview

Our infrastructure uses a **two-phase approach** for user-data scripts:

1. **Phase 1: ImageBuilder Recipe Build** - Creates clean, standardized base AMIs
2. **Phase 2: EC2 Instance Deployment** - Applies business-specific configurations

## 🏗️ Phase 1: ImageBuilder Recipe Build

### Purpose
Create a clean, standardized base AMI that can be used across all environments and business units.

### Script Location
- **File**: `AWS\ImageBuilder\templates\user-data-template.ps1`
- **Used by**: ImageBuilder recipes during AMI creation
- **Execution**: During the ImageBuilder build process

### What it does
✅ **Image Preparation Tasks:**
- Enables WinRM for remote management
- Configures HTTPS certificates for WinRM
- Sets up firewall rules for remote access
- Applies Windows server optimizations (power plan, services)
- Configures system-managed page file
- Creates directory structure for future deployment scripts
- Sets appropriate permissions

❌ **What it does NOT do:**
- Configure business-specific admin groups
- Join domains
- Install business-specific software
- Apply environment-specific settings

### Key Features
```powershell
# Creates directory structure for deployment
$directories = @(
    "C:\Scripts",
    "C:\Scripts\Logs", 
    "C:\Scripts\Config",
    "C:\Scripts\Tools"
)

# Optimizes Windows for server use
# - High performance power plan
# - Disables unnecessary services
# - Configures system-managed page file

# Creates image info file for reference
$imageInfo = @{
    ImageBuildDate = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    ImageBuilderVersion = "1.0"
    BaseAMI = "Windows Server 2022"
    PreparedBy = "ImageBuilder"
    WinRMEnabled = $true
    OptimizedForServer = $true
    ReadyForDeployment = $true
}
```

## 🚀 Phase 2: EC2 Instance Deployment

### Purpose
Configure EC2 instances launched from the base AMI with business-specific settings.

### Script Location
- **File**: `AWS\EC2-Deployment\ec2-deployment-userdata.ps1`
- **Used by**: EC2 launch templates or direct instance launches
- **Execution**: When EC2 instances are launched from the base AMI

### What it does
✅ **Business-Specific Configuration:**
- Downloads SLM_Config.json from S3
- **Configures admin groups based on environment**
- Retrieves configuration from EC2 tags
- Sets environment variables for other scripts
- Applies role-specific configurations (SQL Server, IIS, etc.)
- Creates deployment audit trail

### Key Features
```powershell
# Retrieves configuration from EC2 tags
$businessUnit = "SPF"  # From Business-Unit tag
$environment = "PRD"   # From Environment tag
$serverRole = "Shared-2022"  # From Server-Role tag

# Downloads admin groups from S3 configuration
aws s3 cp "s3://$s3Bucket/configs/SLM_Config.json" $slmConfigPath

# Configures admin groups based on environment
$adminGroups = $envConfig.DEFAULT_ADM
foreach ($group in $adminGroups) {
    Add-LocalGroupMember -Group "Administrators" -Member $group
}

# Role-specific configuration
switch ($serverRole) {
    "MSSQL-2022" { Enable-WindowsOptionalFeature -FeatureName NetFx3 }
    "WebServer-2022" { Enable-WindowsOptionalFeature -FeatureName IIS-WebServerRole }
}
```

## 📋 Comparison Table

| Aspect | ImageBuilder Phase | EC2 Deployment Phase |
|--------|-------------------|----------------------|
| **Purpose** | Create clean base AMI | Business-specific configuration |
| **Script** | `user-data-template.ps1` | `ec2-deployment-userdata.ps1` |
| **Admin Groups** | ❌ Not configured | ✅ **Configured from S3** |
| **Domain Join** | ❌ Not configured | ✅ Can be configured |
| **Business Config** | ❌ Generic only | ✅ **S3-based configuration** |
| **Environment** | ❌ Environment-agnostic | ✅ **Environment-specific** |
| **WinRM** | ✅ Enabled | ✅ Already available |
| **Optimization** | ✅ Server optimizations | ✅ Role-specific features |

## 🔄 Workflow

### Step 1: ImageBuilder Build
```
Base Windows AMI → ImageBuilder Recipe → user-data-template.ps1 → Clean Base AMI
```

### Step 2: S3 Configuration Upload
```
SLM_Config.json → S3 Bucket → Available for deployment
```

### Step 3: EC2 Deployment
```
Clean Base AMI → EC2 Launch → ec2-deployment-userdata.ps1 → Configured Instance
                                        ↓
                               Downloads from S3 → Admin Groups Applied
```

## 🎯 Benefits of Two-Phase Approach

### ✅ Advantages

1. **Clean Base Images**: Base AMIs are environment-agnostic and reusable
2. **Centralized Configuration**: Admin groups managed in S3-stored JSON
3. **Environment Isolation**: Different admin groups per environment automatically
4. **Faster Deployment**: Base AMI already optimized, only business config needed
5. **Audit Trail**: Complete logging of both phases
6. **Flexibility**: Same base AMI used for DEV, PPE, and PRD with different configs

### 🔧 Configuration Management

- **ImageBuilder Phase**: Hardcoded optimizations (same for all)
- **Deployment Phase**: S3-based configuration (environment-specific)

## 📁 File Structure

```
AWS/
├── ImageBuilder/
│   └── templates/
│       └── user-data-template.ps1          # Phase 1: Image preparation
├── EC2-Deployment/
│   ├── ec2-deployment-userdata.ps1         # Phase 2: Business deployment
│   ├── SLM_Config.json                     # Admin groups configuration
│   └── Deploy-ConfigToS3.ps1              # Upload config to S3
└── SystemsManagerAutomation/
    └── Configs/
        └── SLM_Config.json                 # Source configuration
```

## 🚀 Usage

### For ImageBuilder
```powershell
# Use user-data-template.ps1 in ImageBuilder recipes
# This creates the clean base AMI
```

### For EC2 Deployment
```powershell
# 1. Upload configuration to S3
.\Deploy-ConfigToS3.ps1 -S3Bucket "your-deployment-bucket"

# 2. Launch EC2 with deployment user-data
# Use ec2-deployment-userdata.ps1 as user-data
# Set EC2 tags: Business-Unit, Environment, Server-Role
```

## 🔍 Troubleshooting

### ImageBuilder Phase Issues
- Check `C:\Scripts\imagebuilder-prep.log`
- Verify WinRM is enabled
- Check directory permissions

### Deployment Phase Issues
- Check `C:\Scripts\ec2-deployment.log`
- Verify S3 bucket access
- Check EC2 tags are set correctly
- Validate SLM_Config.json structure

This two-phase approach ensures **clean, reusable base images** while providing **flexible, environment-specific deployment configurations** with **centralized admin group management**.
