name: win-server-bginfo
description: Install and configure BGInfo to display system information on desktop background
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: InstallBGInfo
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                $bgInfoDir = "${env:ProgramFiles}\BGInfo"
                $tempDir = "C:\DesktopInfo\bginfo"
                $bgInfoUrl = "https://download.sysinternals.com/files/BGInfo.zip"
                $zipPath = "$tempDir\BGInfo.zip"

                if ((Test-Path "$bgInfoDir\Bginfo.exe") -or (Test-Path "$bgInfoDir\Bginfo64.exe")) {
                    Write-Host "BGInfo already installed"; exit 0
                }

                New-Item -ItemType Directory -Path $bgInfoDir,$tempDir -Force | Out-Null
                Invoke-WebRequest -Uri $bgInfoUrl -OutFile $zipPath -UseBasicParsing

                if ((Get-Item $zipPath).Length -lt 10KB) {
                    Write-Error "Download failed"; exit 1
                }

                Expand-Archive -Path $zipPath -DestinationPath $tempDir -Force
                Get-ChildItem -Path $tempDir -Filter "*.exe" | Copy-Item -Destination $bgInfoDir -Force

      - name: ConfigureBGInfo
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                $bgInfoDir = "${env:ProgramFiles}\BGInfo"
                $environment = if ($env:BGINFO_ENVIRONMENT) { $env:BGINFO_ENVIRONMENT } else { "DEV" }

                $envColors = @{
                    "DEV" = @{ Red = 0; Green = 128; Blue = 0; Name = "Development" }
                    "PPE" = @{ Red = 228; Green = 199; Blue = 27; Name = "Pre-Production" }
                    "PRD" = @{ Red = 255; Green = 0; Blue = 0; Name = "Production" }
                }

                $envConfig = $envColors[$environment.ToUpper()]
                $configPath = "$bgInfoDir\bginfo.bgi"
                $desktopColor = $envConfig.Red + ($envConfig.Green * 256) + ($envConfig.Blue * 65536)

                $rtf = "{\rtf1\ansi\deff0{\fonttbl{\f0\fnil\fcharset0 Arial;}}{\colortbl ;\red255\green255\blue255;\red0\green128\blue0;\red255\green0\blue0;}\viewkind4\uc1\pard\cf2\ul\b\f0\fs28 <Computer Name>\ulnone\b0\fs20\par\par\cf1\b Environment:\b0\tab $($envConfig.Name) ($($environment.ToUpper()))\par\par\b User:\b0\tab <Logon Domain>\\<User Name>\par\b Domain:\b0\tab <Machine Domain>\par\par\b OS Version:\b0\tab <OS Version>\par\b Boot Time:\b0\tab <Boot Time>\par\par\b CPU:\b0\tab <CPU>\par\b Memory:\b0\tab <Memory>\par\par\b IP Address:\b0\tab <IP Address>\par\b DNS Server:\b0\tab <DNS Server>\par}"
                $configContent = "[BGInfo]`r`nRTF=$rtf`r`nPosition=0`r`nTextWidth2=400`r`nTextHeight2=300`r`nOpaqueTextBox=1`r`nDesktopColor=$desktopColor"

                Set-Content -Path $configPath -Value $configContent -Encoding ASCII

      - name: ConfigureAutostart
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                $bgInfoDir = "${env:ProgramFiles}\BGInfo"
                $bgInfoExe = if (Test-Path "$bgInfoDir\Bginfo64.exe") { "$bgInfoDir\Bginfo64.exe" } else { "$bgInfoDir\Bginfo.exe" }
                $configPath = "$bgInfoDir\bginfo.bgi"

                Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" -Name "BGInfo" -Value "`"$bgInfoExe`" `"$configPath`" /timer:0 /nolicprompt /silent" -Force

                Start-Process -FilePath $bgInfoExe -ArgumentList "`"$configPath`" /timer:0 /nolicprompt /silent" -Wait -NoNewWindow

                Remove-Item -Path "C:\DesktopInfo\bginfo" -Recurse -Force -ErrorAction SilentlyContinue

  - name: validate
    steps:
      - name: ValidateInstallation
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                $bgInfoDir = "${env:ProgramFiles}\BGInfo"
                $bgInfoExe = if (Test-Path "$bgInfoDir\Bginfo64.exe") { "$bgInfoDir\Bginfo64.exe" } else { "$bgInfoDir\Bginfo.exe" }
                $configPath = "$bgInfoDir\bginfo.bgi"

                if (!(Test-Path $bgInfoExe)) { Write-Error "BGInfo executable not found"; exit 1 }
                if (!(Test-Path $configPath)) { Write-Error "BGInfo config not found"; exit 1 }

                $regValue = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" -Name "BGInfo" -ErrorAction SilentlyContinue
                if (!$regValue) { Write-Error "BGInfo startup registry entry not found"; exit 1 }

                Write-Host "BGInfo installation validated successfully"
