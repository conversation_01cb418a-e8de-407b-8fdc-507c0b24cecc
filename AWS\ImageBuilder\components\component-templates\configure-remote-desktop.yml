# AWS Image Builder Component: Configure Remote Desktop Services
# This component configures Remote Desktop Services for secure remote access

name: win-server-remote-desktop
description: Configure Remote Desktop Services with security best practices
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: CheckCurrentRDPSettings
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Checking current Remote Desktop settings..."
        
        # Check if Remote Desktop is enabled
        $rdpEnabled = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server" -Name "fDenyTSConnections" -ErrorAction SilentlyContinue
        if ($rdpEnabled) {
            if ($rdpEnabled.fDenyTSConnections -eq 0) {
                Write-Host "Remote Desktop is currently ENABLED"
            } else {
                Write-Host "Remote Desktop is currently DISABLED"
            }
        }
        
        # Check Terminal Services service status
        $tsService = Get-Service -Name "TermService" -ErrorAction SilentlyContinue
        if ($tsService) {
            Write-Host "Terminal Services status: $($tsService.Status)"
        }
        
        # Check RDP-TCP listener port
        $rdpPort = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\Winstations\RDP-Tcp" -Name "PortNumber" -ErrorAction SilentlyContinue
        if ($rdpPort) {
            Write-Host "Current RDP port: $($rdpPort.PortNumber)"
        }
        
        # Check Network Level Authentication
        $nlaEnabled = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "UserAuthentication" -ErrorAction SilentlyContinue
        if ($nlaEnabled) {
            Write-Host "Network Level Authentication: $(if ($nlaEnabled.UserAuthentication -eq 1) { 'Enabled' } else { 'Disabled' })"
        }

  - name: EnableRemoteDesktop
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Enabling Remote Desktop..."
        
        # Enable Remote Desktop
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server" -Name "fDenyTSConnections" -Value 0 -Type DWord
        Write-Host "Remote Desktop enabled"
        
        # Enable Remote Desktop through Windows Firewall
        Write-Host "Configuring Windows Firewall for Remote Desktop..."
        Enable-NetFirewallRule -DisplayGroup "Remote Desktop"
        Write-Host "Windows Firewall rules enabled for Remote Desktop"
        
        # Start and configure Terminal Services
        Write-Host "Starting Terminal Services..."
        Set-Service -Name "TermService" -StartupType Automatic
        Start-Service -Name "TermService" -ErrorAction SilentlyContinue
        
        $tsService = Get-Service -Name "TermService"
        Write-Host "Terminal Services status: $($tsService.Status)"

  - name: ConfigureSecuritySettings
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Remote Desktop security settings..."
        
        # Enable Network Level Authentication (NLA) for enhanced security
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "UserAuthentication" -Value 1 -Type DWord
        Write-Host "Network Level Authentication enabled"
        
        # Set security layer to SSL (TLS 1.0)
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "SecurityLayer" -Value 2 -Type DWord
        Write-Host "Security layer set to SSL/TLS"
        
        # Set minimum encryption level to High
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "MinEncryptionLevel" -Value 3 -Type DWord
        Write-Host "Minimum encryption level set to High"
        
        # Disable clipboard redirection for security (can be re-enabled if needed)
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "fDisableClip" -Value 1 -Type DWord
        Write-Host "Clipboard redirection disabled for security"
        
        # Disable drive redirection for security (can be re-enabled if needed)
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "fDisableCdm" -Value 1 -Type DWord
        Write-Host "Drive redirection disabled for security"

  - name: ConfigureSessionSettings
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Remote Desktop session settings..."
        
        # Set session timeout (30 minutes = 1800000 milliseconds)
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "MaxConnectionTime" -Value 1800000 -Type DWord
        Write-Host "Maximum connection time set to 30 minutes"
        
        # Set disconnection timeout (15 minutes = 900000 milliseconds)
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "MaxDisconnectionTime" -Value 900000 -Type DWord
        Write-Host "Maximum disconnection time set to 15 minutes"
        
        # Set idle timeout (15 minutes = 900000 milliseconds)
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "MaxIdleTime" -Value 900000 -Type DWord
        Write-Host "Maximum idle time set to 15 minutes"
        
        # Limit concurrent sessions (set to 2 for server environments)
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server" -Name "MaxInstanceCount" -Value 2 -Type DWord
        Write-Host "Maximum concurrent sessions set to 2"

  - name: ConfigureAdvancedSettings
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring advanced Remote Desktop settings..."
        
        # Configure RDP port (default 3389, can be changed for security)
        # Keeping default port for now, but this can be customized
        $rdpPort = 3389
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\Winstations\RDP-Tcp" -Name "PortNumber" -Value $rdpPort -Type DWord
        Write-Host "RDP port set to: $rdpPort"
        
        # Enable single session per user
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server" -Name "fSingleSessionPerUser" -Value 1 -Type DWord
        Write-Host "Single session per user enabled"
        
        # Configure temporary folders deletion
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server" -Name "DeleteTempDirsOnExit" -Value 1 -Type DWord
        Write-Host "Temporary folders deletion on exit enabled"
        
        # Disable automatic logon
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" -Name "AutoAdminLogon" -Value 0 -Type DWord
        Write-Host "Automatic logon disabled"

  - name: ConfigureUserRights
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring user rights for Remote Desktop..."
        
        # Add Administrators group to Remote Desktop Users (if not already present)
        try {
            $rdpGroup = [ADSI]"WinNT://./Remote Desktop Users,group"
            $adminGroup = [ADSI]"WinNT://./Administrators,group"
            
            # Check if Administrators are already in RDP group
            $members = @($rdpGroup.psbase.Invoke("Members"))
            $adminInRDP = $false
            
            foreach ($member in $members) {
                $memberPath = $member.GetType().InvokeMember("ADsPath", 'GetProperty', $null, $member, $null)
                if ($memberPath -like "*Administrators*") {
                    $adminInRDP = $true
                    break
                }
            }
            
            if (-not $adminInRDP) {
                $rdpGroup.psbase.Invoke("Add", $adminGroup.psbase.path)
                Write-Host "Administrators group added to Remote Desktop Users"
            } else {
                Write-Host "Administrators group already in Remote Desktop Users"
            }
        } catch {
            Write-Warning "Failed to configure Remote Desktop Users group: $($_.Exception.Message)"
        }

  - name: ConfigureAuditing
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Remote Desktop auditing..."
        
        # Enable logon/logoff auditing
        try {
            auditpol /set /subcategory:"Logon" /success:enable /failure:enable
            auditpol /set /subcategory:"Logoff" /success:enable /failure:enable
            auditpol /set /subcategory:"Other Logon/Logoff Events" /success:enable /failure:enable
            Write-Host "Logon/Logoff auditing enabled"
        } catch {
            Write-Warning "Failed to configure auditing: $($_.Exception.Message)"
        }
        
        # Configure event log sizes for security monitoring
        try {
            wevtutil sl Security /ms:209715200  # 200MB
            wevtutil sl System /ms:104857600    # 100MB
            Write-Host "Event log sizes configured for security monitoring"
        } catch {
            Write-Warning "Failed to configure event log sizes: $($_.Exception.Message)"
        }

  - name: RestartTerminalServices
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Restarting Terminal Services to apply changes..."
        
        try {
            # Restart Terminal Services
            Restart-Service -Name "TermService" -Force
            Write-Host "Terminal Services restarted successfully"
            
            # Wait for service to start
            Start-Sleep -Seconds 10
            
            # Verify service status
            $tsService = Get-Service -Name "TermService"
            Write-Host "Terminal Services status after restart: $($tsService.Status)"
            
        } catch {
            Write-Warning "Failed to restart Terminal Services: $($_.Exception.Message)"
        }

- name: validate
  steps:
  - name: ValidateRemoteDesktopConfiguration
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Validating Remote Desktop configuration..."
        
        # Check if Remote Desktop is enabled
        $rdpEnabled = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server" -Name "fDenyTSConnections" -ErrorAction SilentlyContinue
        if ($rdpEnabled -and $rdpEnabled.fDenyTSConnections -eq 0) {
            Write-Host "✓ Remote Desktop is enabled"
        } else {
            Write-Error "✗ Remote Desktop is not enabled"
            exit 1
        }
        
        # Check Terminal Services status
        $tsService = Get-Service -Name "TermService" -ErrorAction SilentlyContinue
        if ($tsService -and $tsService.Status -eq "Running") {
            Write-Host "✓ Terminal Services is running"
        } else {
            Write-Error "✗ Terminal Services is not running"
            exit 1
        }
        
        # Check Network Level Authentication
        $nlaEnabled = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "UserAuthentication" -ErrorAction SilentlyContinue
        if ($nlaEnabled -and $nlaEnabled.UserAuthentication -eq 1) {
            Write-Host "✓ Network Level Authentication is enabled"
        } else {
            Write-Warning "✗ Network Level Authentication is not enabled"
        }
        
        # Check firewall rules
        $firewallRules = Get-NetFirewallRule -DisplayGroup "Remote Desktop" | Where-Object { $_.Enabled -eq $true }
        if ($firewallRules) {
            Write-Host "✓ Windows Firewall rules for Remote Desktop are enabled"
        } else {
            Write-Warning "✗ Windows Firewall rules for Remote Desktop are not enabled"
        }
        
        # Display configuration summary
        $rdpPort = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\Winstations\RDP-Tcp" -Name "PortNumber" -ErrorAction SilentlyContinue
        $securityLayer = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "SecurityLayer" -ErrorAction SilentlyContinue
        $encryptionLevel = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "MinEncryptionLevel" -ErrorAction SilentlyContinue
        
        Write-Host "`nRemote Desktop Configuration Summary:"
        Write-Host "- RDP Port: $($rdpPort.PortNumber)"
        Write-Host "- Security Layer: $($securityLayer.SecurityLayer)"
        Write-Host "- Minimum Encryption Level: $($encryptionLevel.MinEncryptionLevel)"
        Write-Host "- Network Level Authentication: $(if ($nlaEnabled.UserAuthentication -eq 1) { 'Enabled' } else { 'Disabled' })"
        
        Write-Host "`nVALIDATION SUCCESS: Remote Desktop is properly configured"
        Write-Host "SECURITY NOTE: Consider changing the default RDP port (3389) for enhanced security"
