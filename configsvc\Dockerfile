#FROM tiangolo/uvicorn-gunicorn-fastapi:python3.11
FROM python:3.12-slim
# Set environment varibles
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV TZ="Africa/Johannesburg"
ENV UVICORN_HOST="0.0.0.0"
ENV UVICORN_PORT=8000

# Install apt packages
USER root
RUN ls -al
RUN whoami
RUN apt-get update && apt-get upgrade -y
RUN apt-get install -y gcc libmariadb3 libmariadb-dev
RUN apt-get install libreoffice-base libreoffice-writer --no-install-recommends -y

WORKDIR /src
COPY requirements.txt .

RUN pip install --no-cache-dir --upgrade -r requirements.txt
# Copy the application code to the working directory
COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
#CMD ["uvicorn", "app.main:app"]