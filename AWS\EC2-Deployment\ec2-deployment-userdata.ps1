<powershell>
# EC2 Deployment User Data Script
# This script configures EC2 instances launched from the base AMI with business-specific settings
# Including admin groups, domain join, and role-specific configurations

# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Create scripts directory
if (!(Test-Path "C:\Scripts")) {
    New-Item -ItemType Directory -Path "C:\Scripts" -Force | Out-Null
}

# Create log file
$logFile = "C:\Scripts\ec2-deployment.log"
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

Write-Log "Starting EC2 deployment user data script..."

try {
    # Configuration parameters - these should come from EC2 tags or launch template
    $region = "af-south-1"
    $s3Bucket = "your-deployment-bucket"

    Write-Log "Configuration: Region = $region, S3 Bucket = $s3Bucket"

    # Get instance metadata and tags
    Write-Log "Retrieving instance metadata and tags..."

    try {
        # Get instance ID
        $instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id" -TimeoutSec 10
        Write-Log "Instance ID: $instanceId"

        # Get instance tags
        $token = Invoke-RestMethod -Uri "http://***************/latest/api/token" -Method PUT -Headers @{"X-aws-ec2-metadata-token-ttl-seconds" = "21600"} -TimeoutSec 10
        $tagsResponse = Invoke-RestMethod -Uri "http://***************/latest/meta-data/tags/instance" -Headers @{"X-aws-ec2-metadata-token" = $token} -TimeoutSec 10

        # Parse tags - mapping EC2 tags to config structure
        $businessUnit = $null  #BU
        $environment = $null   #ENV
        $serverRole = $null    #APP_TYPE

        if ($tagsResponse) {
            $tags = $tagsResponse -split "`n"
            foreach ($tag in $tags) {
                $tagValue = Invoke-RestMethod -Uri "http://***************/latest/meta-data/tags/instance/$tag" -Headers @{"X-aws-ec2-metadata-token" = $token} -TimeoutSec 10
                switch ($tag) {
                    "Business-Unit" { $businessUnit = $tagValue }  #BU (SPF/SC)
                    "Environment" { $environment = $tagValue }     #ENV (PRD/PPE/DEV)
                    "Server-Role" { $serverRole = $tagValue }      #APP_TYPE (Shared/MSSQL)
                }
            }
        }

    } catch {
        Write-Log "Could not retrieve instance metadata/tags: $($_.Exception.Message)" "WARNING"
    }

    # Set defaults if tags not found
    if (!$businessUnit) { $businessUnit = "SPF"; Write-Log "Using default business unit: $businessUnit" "WARNING" }
    if (!$environment) { $environment = "PRD"; Write-Log "Using default environment: $environment" "WARNING" }
    if (!$serverRole) { $serverRole = "Shared"; Write-Log "Using default server role: $serverRole" "WARNING" }

    Write-Log "Deployment parameters: BU=$businessUnit, ENV=$environment, APP_TYPE=$serverRole"
    
    # Download SLM configuration from S3
    Write-Log "Downloading SLM configuration from S3..."
    $slmConfigS3Key = "configs/SLM_Config.json"
    $slmConfigPath = "C:\Scripts\SLM_Config.json"
    
    aws s3 cp "s3://$s3Bucket/$slmConfigS3Key" $slmConfigPath --region $region 2>&1 | Out-Null
    
    if ($LASTEXITCODE -ne 0 -or !(Test-Path $slmConfigPath)) {
        throw "Failed to download SLM_Config.json from S3: s3://$s3Bucket/$slmConfigS3Key"
    }
    
    Write-Log "Successfully downloaded SLM configuration"
    
    # Load SLM configuration and extract admin groups
    $slmConfig = Get-Content $slmConfigPath -Raw | ConvertFrom-Json

    # Navigate to the correct business unit using flattened SLM_Config.json structure
    $buConfig = $slmConfig.$businessUnit
    if (!$buConfig) {
        $availableBUs = $slmConfig.PSObject.Properties.Name | Where-Object { $_ -ne "CLIENT" }
        throw "Business unit '$businessUnit' not found in SLM configuration. Available: $($availableBUs -join ', ')"
    }

    $envConfig = $buConfig.environments.$environment
    if (!$envConfig) {
        throw "Environment '$environment' not found for business unit '$businessUnit'. Available: $($buConfig.environments.PSObject.Properties.Name -join ', ')"
    }

    # Get the serverOUs configuration for the server role
    $serverOUPath = $null
    if ($envConfig.serverOUs) {
        # Get OS version - you may want to detect this from the instance
        $osVersion = "2022"  # Default, could be retrieved from instance metadata or registry
        $ouKey = "${serverRole}-${osVersion}"
        
        if ($envConfig.serverOUs.PSObject.Properties.Name -contains $ouKey) {
            $serverOUPath = $envConfig.serverOUs.$ouKey
            Write-Log "Found serverOU configuration for ${serverRole}-${osVersion}: $serverOUPath"
        } else {
            # Try to find any OU for this server role regardless of OS version
            $availableOUs = $envConfig.serverOUs.PSObject.Properties.Name | Where-Object { $_ -like "${serverRole}-*" }
            if ($availableOUs.Count -gt 0) {
                $serverOUPath = $envConfig.serverOUs.($availableOUs[0])
                Write-Log "Using first available OU for server role '$serverRole': $serverOUPath" "WARNING"
            } else {
                Write-Log "No serverOU found for server role '$serverRole'. Available OUs: $($envConfig.serverOUs.PSObject.Properties.Name -join ', ')" "WARNING"
            }
        }
    } else {
        Write-Log "No serverOUs configuration found for ${businessUnit}/${environment}" "WARNING"
    }
    
    # Configure admin groups
    Write-Log "Configuring administrator groups..."
    
    if ($envConfig.DEFAULT_ADM -and $envConfig.DEFAULT_ADM.Count -gt 0) {
        $adminGroups = $envConfig.DEFAULT_ADM
        Write-Log "Found admin groups for ${businessUnit}/${environment}: $($adminGroups -join ', ')"
        
        # Get current administrators
        $currentAdmins = @()
        try {
            $currentAdmins = Get-LocalGroupMember -Group "Administrators" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Name
        } catch {
            Write-Log "Warning: Could not retrieve current administrators: $($_.Exception.Message)" "WARNING"
        }
        
        $addedGroups = @()
        $skippedGroups = @()
        $failedGroups = @()
        
        foreach ($group in $adminGroups) {
            try {
                if ($currentAdmins -contains $group) {
                    Write-Log "Group '$group' is already in Administrators group"
                    $skippedGroups += $group
                    continue
                }
                
                Add-LocalGroupMember -Group "Administrators" -Member $group -ErrorAction Stop
                Write-Log "Successfully added '$group' to Administrators group"
                $addedGroups += $group
                
            } catch {
                Write-Log "Failed to add '$group' to Administrators group: $($_.Exception.Message)" "WARNING"
                $failedGroups += $group
            }
        }
        
        Write-Log "Admin groups configuration completed - Added: $($addedGroups.Count), Skipped: $($skippedGroups.Count), Failed: $($failedGroups.Count)"
        
    } else {
        Write-Log "No DEFAULT_ADM groups defined for ${businessUnit}/${environment}" "WARNING"
    }

    # Set environment variables for other scripts
    [Environment]::SetEnvironmentVariable("BUSINESS_UNIT", $businessUnit, "Machine")
    [Environment]::SetEnvironmentVariable("ENVIRONMENT", $environment, "Machine")
    [Environment]::SetEnvironmentVariable("SERVER_ROLE", $serverRole, "Machine")
    [Environment]::SetEnvironmentVariable("DOMAIN", $envConfig.domain, "Machine")
    [Environment]::SetEnvironmentVariable("BASE_PATH", $envConfig.basePath, "Machine")

    # Set the specific OU path for this server role if available
    if ($serverOUPath) {
        [Environment]::SetEnvironmentVariable("SERVER_OU_PATH", $serverOUPath, "Machine")
        Write-Log "Set SERVER_OU_PATH to: $serverOUPath"
    }

    Write-Log "Environment variables set for deployment"

    # Role-specific configuration based on APP_TYPE from config
    Write-Log "Applying role-specific configuration for APP_TYPE: $serverRole"

    switch ($serverRole) {
        "MSSQL" {
            Write-Log "Preparing for SQL Server"
            # Enable .NET Framework 3.5 for SQL Server
            try {
                Enable-WindowsOptionalFeature -Online -FeatureName NetFx3 -All -NoRestart -ErrorAction Stop
                Write-Log "Enabled .NET Framework 3.5 for SQL Server"
            } catch {
                Write-Log "Failed to enable .NET Framework 3.5: $($_.Exception.Message)" "WARNING"
            }
        }
        "WebServer" {
            Write-Log "Configuring IIS Web Server"
            try {
                Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole -All -NoRestart -ErrorAction Stop
                Enable-WindowsOptionalFeature -Online -FeatureName IIS-ASPNET45 -All -NoRestart -ErrorAction Stop
                Write-Log "Enabled IIS and ASP.NET features"
            } catch {
                Write-Log "Failed to configure IIS: $($_.Exception.Message)" "WARNING"
            }
        }
        "Shared" {
            Write-Log "Applying shared server configuration"
            # Add any shared server specific configurations here
        }
        default {
            Write-Log "No specific configuration for APP_TYPE: $serverRole"
        }
    }
    
    # Create deployment summary
    $deploymentInfo = @{
        DeploymentDate = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        InstanceId = $instanceId
        BusinessUnit = $businessUnit
        Environment = $environment
        ServerRole = $serverRole
        Domain = $envConfig.domain
        BasePath = $envConfig.basePath
        ServerOUPath = $serverOUPath
        AdminGroupsConfigured = $adminGroups
        S3ConfigSource = "s3://$s3Bucket/$slmConfigS3Key"
        ConfigStructure = @{
            BU = $businessUnit
            ENV = $environment
            APP_TYPE = $serverRole
        }
        DeploymentStatus = "Success"
    }
    
    $deploymentInfoPath = "C:\Scripts\deployment-info.json"
    $deploymentInfo | ConvertTo-Json -Depth 10 | Out-File -FilePath $deploymentInfoPath -Encoding UTF8
    Write-Log "Deployment information saved to: $deploymentInfoPath"
    
    Write-Log "EC2 deployment user data completed successfully"
    
} catch {
    Write-Log "EC2 deployment failed: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.Exception.StackTrace)" "ERROR"
    
    # Create error info for troubleshooting
    $errorInfo = @{
        Error = $_.Exception.Message
        StackTrace = $_.ScriptStackTrace
        Timestamp = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        DeploymentStatus = "Failed"
    }
    $errorInfo | ConvertTo-Json | Out-File -FilePath "C:\Scripts\deployment-error.json" -Encoding UTF8
    
    exit 1
}
</powershell>

