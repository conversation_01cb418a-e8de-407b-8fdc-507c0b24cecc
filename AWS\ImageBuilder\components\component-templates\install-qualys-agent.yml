# AWS Image Builder Component: Install Qualys VMDR Agent
# This component installs the Qualys VMDR (Vulnerability Management, Detection and Response) agent on Windows Server
# Required Environment Variables (via AWS Systems Manager Parameter Store):
#QUALYS_ACTIVATION_ID - Your Qualys activation ID
#QUALYS_CUSTOMER_ID - Your Qualys customer ID
#QUALYS_SERVER_URI - Qualys server URI (optional, defaults to qagpublic.qg2.apps.qualys.com)
#QUALYS_INSTALLER_URL - Custom URL for installer (optional)

name: win-server-qualys-agent
description: Install Qualys VMDR agent on Windows Server
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: CheckExistingQualys
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
            commands:
              - |
                Write-Host "Checking for existing Qualys agent..."

                # Check if Qualys service is already installed
                $qualysService = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue
                if ($qualysService) {
                    Write-Host "Qualys agent is already installed"
                    Write-Host "Service Status: $($qualysService.Status)"
                    
                    # Get agent version if available
                    try {
                        $qualysPath = "${env:ProgramFiles}\Qualys\QualysAgent\QualysAgent.exe"
                        if (Test-Path $qualysPath) {
                            $version = (Get-ItemProperty $qualysPath).VersionInfo.FileVersion
                            Write-Host "Qualys agent version: $version"
                        }
                    } catch {
                        Write-Warning "Could not determine Qualys version: $($_.Exception.Message)"
                    }
                    
                    exit 0
                } else {
                    Write-Host "Qualys agent is not installed. Proceeding with installation..."
                }

      - name: ValidateParameters
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating Qualys installation parameters..."

                # Check for required environment variables or parameters
                # These should be set via Systems Manager Parameter Store or environment variables
                $activationId = $env:QUALYS_ACTIVATION_ID
                $customerId = $env:QUALYS_CUSTOMER_ID
                $serverUri = $env:QUALYS_SERVER_URI
                $installerUrl = $env:QUALYS_INSTALLER_URL

                if (-not $activationId) {
                    Write-Error "QUALYS_ACTIVATION_ID environment variable is required"
                    exit 1
                }

                if (-not $customerId) {
                    Write-Error "QUALYS_CUSTOMER_ID environment variable is required"
                    exit 1
                }

                if (-not $serverUri) {
                    Write-Warning "QUALYS_SERVER_URI not provided. Will use default server URI."
                    $serverUri = "qagpublic.qg2.apps.qualys.com"
                }

                if (-not $installerUrl) {
                    Write-Warning "QUALYS_INSTALLER_URL not provided. Will attempt to use default download method."
                }

                Write-Host "Qualys Activation ID: $($activationId.Substring(0,8))..." # Only show first 8 chars for security
                Write-Host "Qualys Customer ID: $($customerId.Substring(0,8))..."
                Write-Host "Qualys Server URI: $serverUri"
                Write-Host "Validation completed successfully"

      - name: DownloadQualysAgent
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Downloading Qualys agent..."

                # Create temp directory
                $tempDir = "C:\temp\qualys"
                if (!(Test-Path $tempDir)) {
                    New-Item -ItemType Directory -Path $tempDir -Force
                    Write-Host "Created temp directory: $tempDir"
                }

                $installerPath = "$tempDir\QualysCloudAgent.exe"
                $installerUrl = $env:QUALYS_INSTALLER_URL

                if ($installerUrl) {
                    Write-Host "Downloading from provided URL..."
                    try {
                        # Download from provided URL (could be S3, internal repository, etc.)
                        Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath -UseBasicParsing
                        Write-Host "Downloaded Qualys installer to: $installerPath"
                    } catch {
                        Write-Error "Failed to download Qualys installer: $($_.Exception.Message)"
                        exit 1
                    }
                } else {
                    # Attempt to download from Qualys portal (requires authentication)
                    Write-Host "Attempting to download from Qualys portal..."
                    $serverUri = $env:QUALYS_SERVER_URI
                    if (-not $serverUri) {
                        $serverUri = "qagpublic.qg2.apps.qualys.com"
                    }
                    
                    $downloadUrl = "https://$serverUri/CloudAgent/QualysCloudAgent.exe"
                    
                    try {
                        Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing
                        Write-Host "Downloaded Qualys installer from portal"
                    } catch {
                        Write-Error "Failed to download from Qualys portal. Please provide QUALYS_INSTALLER_URL or place installer at $installerPath"
                        Write-Error "Error: $($_.Exception.Message)"
                        exit 1
                    }
                }

                # Verify file exists and has reasonable size
                if (Test-Path $installerPath) {
                    $fileSize = (Get-Item $installerPath).Length
                    Write-Host "Installer file size: $([math]::Round($fileSize/1MB, 2)) MB"
                    
                    if ($fileSize -lt 1MB) {
                        Write-Error "Installer file appears to be too small. Download may have failed."
                        exit 1
                    }
                } else {
                    Write-Error "Installer file not found after download"
                    exit 1
                }

      - name: InstallQualysAgent
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Installing Qualys agent..."

                $tempDir = "C:\temp\qualys"
                $installerPath = "$tempDir\QualysCloudAgent.exe"
                $activationId = $env:QUALYS_ACTIVATION_ID
                $customerId = $env:QUALYS_CUSTOMER_ID
                $serverUri = $env:QUALYS_SERVER_URI

                if (-not $serverUri) {
                    $serverUri = "qagpublic.qg2.apps.qualys.com"
                }

                if (!(Test-Path $installerPath)) {
                    Write-Error "Qualys installer not found at: $installerPath"
                    exit 1
                }

                # Install Qualys agent with activation parameters
                Write-Host "Running Qualys installer with activation parameters..."
                try {
                    $installArgs = @(
                        "CustomerId=$customerId"
                        "ActivationId=$activationId"
                        "ServerUri=$serverUri"
                    )
                    
                    $process = Start-Process -FilePath $installerPath -ArgumentList $installArgs -Wait -PassThru -NoNewWindow
                    
                    if ($process.ExitCode -eq 0) {
                        Write-Host "Qualys agent installed successfully"
                    } else {
                        Write-Error "Qualys installation failed with exit code: $($process.ExitCode)"
                        exit 1
                    }
                } catch {
                    Write-Error "Failed to install Qualys agent: $($_.Exception.Message)"
                    exit 1
                }

      - name: VerifyInstallation
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Verifying Qualys agent installation..."

                # Wait a moment for service to initialize
                Start-Sleep -Seconds 15

                # Check if service is installed and running
                $qualysService = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue
                if ($qualysService) {
                    Write-Host "Qualys agent service found"
                    Write-Host "Service Status: $($qualysService.Status)"
                    
                    if ($qualysService.Status -eq "Running") {
                        Write-Host "Qualys agent is running successfully"
                    } else {
                        Write-Warning "Qualys service is installed but not running. Status: $($qualysService.Status)"
                        
                        # Attempt to start the service
                        try {
                            Start-Service -Name "QualysAgent"
                            Write-Host "Successfully started Qualys service"
                        } catch {
                            Write-Warning "Could not start Qualys service: $($_.Exception.Message)"
                        }
                    }
                } else {
                    Write-Error "Qualys agent service not found after installation"
                    exit 1
                }

                # Check agent registration status
                Write-Host "Checking agent registration status..."
                try {
                    $qualysPath = "${env:ProgramFiles}\Qualys\QualysAgent"
                    if (Test-Path "$qualysPath\QualysAgent.exe") {
                        # Run agent status command if available
                        $statusOutput = & "$qualysPath\QualysAgent.exe" -status 2>&1
                        Write-Host "Agent status: $statusOutput"
                    }
                } catch {
                    Write-Warning "Could not check agent status: $($_.Exception.Message)"
                }

                Write-Host "Installation verification completed"

      - name: CleanupTempFiles
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
            commands:
              - |
                Write-Host "Cleaning up temporary files..."

                $tempDir = "C:\temp\qualys"
                if (Test-Path $tempDir) {
                    try {
                        Remove-Item -Path $tempDir -Recurse -Force
                        Write-Host "Cleaned up temp directory: $tempDir"
                    } catch {
                        Write-Warning "Could not clean up temp directory: $($_.Exception.Message)"
                    }
                }

  - name: validate
    steps:
      - name: ValidateQualysService
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Final validation of Qualys agent..."

                $qualysService = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue
                if ($qualysService -and $qualysService.Status -eq "Running") {
                    Write-Host "✓ Qualys agent is installed and running"
                    
                    # Get additional info if available
                    try {
                        $qualysPath = "${env:ProgramFiles}\Qualys\QualysAgent\QualysAgent.exe"
                        if (Test-Path $qualysPath) {
                            $version = (Get-ItemProperty $qualysPath).VersionInfo.FileVersion
                            Write-Host "✓ Qualys agent version: $version"
                        }
                    } catch {
                        Write-Host "Could not retrieve version information"
                    }
                    
                    Write-Host "Qualys agent validation completed successfully"
                } else {
                    Write-Error "Qualys agent validation failed"
                    exit 1
                }
