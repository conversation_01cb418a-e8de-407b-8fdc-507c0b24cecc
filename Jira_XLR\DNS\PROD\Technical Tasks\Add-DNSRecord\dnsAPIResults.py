import json

dnsAPIResponse_json = json.loads('${dnsAddAPIResponse}')

releaseVariables['dnsAddAPIResponse']['success'] = dnsAPIResponse_json['success']
releaseVariables['dnsAddAPIResponse']['status'] = dnsAPIResponse_json['status'] 
releaseVariables['dnsAddAPIResponse']['message'] = dnsAPIResponse_json['message']
releaseVariables['dnsAddAPIResponse']['date'] = dnsAPIResponse_json['date']
releaseVariables['dnsAddAPIResponse']['data'] = dnsAPIResponse_json['data']

print releaseVariables['dnsAddAPIResponse']['success']
print releaseVariables['dnsAddAPIResponse']['status']
print releaseVariables['dnsAddAPIResponse']['message']
print releaseVariables['dnsAddAPIResponse']['date']
print releaseVariables['dnsAddAPIResponse']['data']
print releaseVariables['dnsAddAPIResponse']
