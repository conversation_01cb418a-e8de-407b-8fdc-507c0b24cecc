const express = require('express');
const { Category, Manga } = require('../models');
const { isAuthenticated, isAdmin } = require('../../middleware/auth/auth-middleware');

const router = express.Router();

// Get all categories
router.get('/', async (req, res) => {
  try {
    const categories = await Category.findAll({
      order: [['name', 'ASC']]
    });
    
    res.status(200).json({ categories });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get category by ID
router.get('/:id', async (req, res) => {
  try {
    const category = await Category.findByPk(req.params.id, {
      include: [
        {
          model: Manga,
          as: 'manga',
          through: { attributes: [] }
        }
      ]
    });
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }
    
    res.status(200).json({ category });
  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create category (admin only)
router.post('/', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { name, description } = req.body;
    
    if (!name) {
      return res.status(400).json({ message: 'Name is required' });
    }
    
    // Check if category already exists
    const existingCategory = await Category.findOne({
      where: { name }
    });
    
    if (existingCategory) {
      return res.status(400).json({ message: 'Category already exists' });
    }
    
    // Create category
    const category = await Category.create({
      name,
      description
    });
    
    res.status(201).json({
      message: 'Category created successfully',
      category
    });
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update category (admin only)
router.put('/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { name, description } = req.body;
    
    if (!name) {
      return res.status(400).json({ message: 'Name is required' });
    }
    
    // Check if category exists
    const category = await Category.findByPk(req.params.id);
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }
    
    // Check if name is already taken by another category
    if (name !== category.name) {
      const existingCategory = await Category.findOne({
        where: { name }
      });
      
      if (existingCategory) {
        return res.status(400).json({ message: 'Category name already exists' });
      }
    }
    
    // Update category
    category.name = name;
    category.description = description;
    await category.save();
    
    res.status(200).json({
      message: 'Category updated successfully',
      category
    });
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete category (admin only)
router.delete('/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    // Check if category exists
    const category = await Category.findByPk(req.params.id);
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }
    
    // Delete category
    await category.destroy();
    
    res.status(200).json({
      message: 'Category deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get user's preferred categories
router.get('/user/preferred', isAuthenticated, async (req, res) => {
  try {
    const user = await req.user.getPreferredCategories();
    
    res.status(200).json({
      categories: user
    });
  } catch (error) {
    console.error('Error fetching preferred categories:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update user's preferred categories
router.post('/user/preferred', isAuthenticated, async (req, res) => {
  try {
    const { categoryIds } = req.body;
    
    if (!Array.isArray(categoryIds)) {
      return res.status(400).json({ message: 'categoryIds must be an array' });
    }
    
    // Check if all categories exist
    const categories = await Category.findAll({
      where: { id: categoryIds }
    });
    
    if (categories.length !== categoryIds.length) {
      return res.status(400).json({ message: 'One or more categories not found' });
    }
    
    // Update user's preferred categories
    await req.user.setPreferredCategories(categories);
    
    res.status(200).json({
      message: 'Preferred categories updated successfully',
      categories
    });
  } catch (error) {
    console.error('Error updating preferred categories:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;