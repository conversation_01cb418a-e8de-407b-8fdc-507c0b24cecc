# Use a slim Python image for a smaller final image size.
FROM python:3.12.3-slim

# Set the working directory in the container.
WORKDIR /api

# Copy the requirements file, install required dependencies.
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
# Copy the core script.
COPY api.py .
COPY security/zscaler_root.cer /usr/local/share/ca-certificates/zscaler_root.crt
# Update the CA certificates.
RUN update-ca-certificates
# Install the docker CLI within the container.
# This is necessary for the python script and API to run docker commands.
RUN apt-get update && apt-get install -y docker.io && rm -rf /var/lib/apt/lists/*

# Expose the port the API will listen on
EXPOSE 3333

# Command to run the application via uvicorn.
CMD ["uvicorn", "api:api", "--host", "0.0.0.0", "--port", "3333"]