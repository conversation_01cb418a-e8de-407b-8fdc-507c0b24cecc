# AWS Image Builder Component: Install .NET Core/5/6/7/8
# This component installs multiple versions of .NET Core and .NET

name: win-server-dotnet-core
description: Install .NET Core 3.1, .NET 5, 6, 7, and 8 runtimes and SDKs
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: CheckExistingDotNet
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Checking for existing .NET installations..."
        
        # Check if dotnet CLI is available
        try {
            $dotnetVersion = dotnet --version 2>$null
            if ($dotnetVersion) {
                Write-Host "Found .NET CLI version: $dotnetVersion"
                
                # List installed SDKs
                Write-Host "`nInstalled .NET SDKs:"
                dotnet --list-sdks
                
                # List installed runtimes
                Write-Host "`nInstalled .NET Runtimes:"
                dotnet --list-runtimes
            }
        } catch {
            Write-Host ".NET CLI not found or not working properly"
        }

  - name: PrepareInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Preparing .NET installation..."
        
        # Create temp directory
        $tempDir = "C:\temp\dotnet"
        if (!(Test-Path $tempDir)) {
            New-Item -ItemType Directory -Path $tempDir -Force
            Write-Host "Created temp directory: $tempDir"
        }
        
        # Download .NET install script
        $scriptUrl = "https://dot.net/v1/dotnet-install.ps1"
        $scriptPath = "$tempDir\dotnet-install.ps1"
        
        Write-Host "Downloading .NET installation script..."
        try {
            Invoke-WebRequest -Uri $scriptUrl -OutFile $scriptPath -UseBasicParsing
            Write-Host "Download completed successfully"
        } catch {
            Write-Error "Failed to download .NET install script: $($_.Exception.Message)"
            exit 1
        }

  - name: InstallDotNetCore31
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing .NET Core 3.1..."
        
        $tempDir = "C:\temp\dotnet"
        $scriptPath = "$tempDir\dotnet-install.ps1"
        
        # Install .NET Core 3.1 Runtime
        Write-Host "Installing .NET Core 3.1 Runtime..."
        & $scriptPath -Channel 3.1 -Runtime dotnet -InstallDir "C:\Program Files\dotnet"
        
        # Install .NET Core 3.1 SDK
        Write-Host "Installing .NET Core 3.1 SDK..."
        & $scriptPath -Channel 3.1 -InstallDir "C:\Program Files\dotnet"
        
        # Install ASP.NET Core 3.1 Runtime
        Write-Host "Installing ASP.NET Core 3.1 Runtime..."
        & $scriptPath -Channel 3.1 -Runtime aspnetcore -InstallDir "C:\Program Files\dotnet"

  - name: InstallDotNet5
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing .NET 5..."
        
        $tempDir = "C:\temp\dotnet"
        $scriptPath = "$tempDir\dotnet-install.ps1"
        
        # Install .NET 5 Runtime
        Write-Host "Installing .NET 5 Runtime..."
        & $scriptPath -Channel 5.0 -Runtime dotnet -InstallDir "C:\Program Files\dotnet"
        
        # Install .NET 5 SDK
        Write-Host "Installing .NET 5 SDK..."
        & $scriptPath -Channel 5.0 -InstallDir "C:\Program Files\dotnet"
        
        # Install ASP.NET Core 5 Runtime
        Write-Host "Installing ASP.NET Core 5 Runtime..."
        & $scriptPath -Channel 5.0 -Runtime aspnetcore -InstallDir "C:\Program Files\dotnet"

  - name: InstallDotNet6
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing .NET 6..."
        
        $tempDir = "C:\temp\dotnet"
        $scriptPath = "$tempDir\dotnet-install.ps1"
        
        # Install .NET 6 Runtime
        Write-Host "Installing .NET 6 Runtime..."
        & $scriptPath -Channel 6.0 -Runtime dotnet -InstallDir "C:\Program Files\dotnet"
        
        # Install .NET 6 SDK
        Write-Host "Installing .NET 6 SDK..."
        & $scriptPath -Channel 6.0 -InstallDir "C:\Program Files\dotnet"
        
        # Install ASP.NET Core 6 Runtime
        Write-Host "Installing ASP.NET Core 6 Runtime..."
        & $scriptPath -Channel 6.0 -Runtime aspnetcore -InstallDir "C:\Program Files\dotnet"

  - name: InstallDotNet7
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing .NET 7..."
        
        $tempDir = "C:\temp\dotnet"
        $scriptPath = "$tempDir\dotnet-install.ps1"
        
        # Install .NET 7 Runtime
        Write-Host "Installing .NET 7 Runtime..."
        & $scriptPath -Channel 7.0 -Runtime dotnet -InstallDir "C:\Program Files\dotnet"
        
        # Install .NET 7 SDK
        Write-Host "Installing .NET 7 SDK..."
        & $scriptPath -Channel 7.0 -InstallDir "C:\Program Files\dotnet"
        
        # Install ASP.NET Core 7 Runtime
        Write-Host "Installing ASP.NET Core 7 Runtime..."
        & $scriptPath -Channel 7.0 -Runtime aspnetcore -InstallDir "C:\Program Files\dotnet"

  - name: InstallDotNet8
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing .NET 8..."
        
        $tempDir = "C:\temp\dotnet"
        $scriptPath = "$tempDir\dotnet-install.ps1"
        
        # Install .NET 8 Runtime
        Write-Host "Installing .NET 8 Runtime..."
        & $scriptPath -Channel 8.0 -Runtime dotnet -InstallDir "C:\Program Files\dotnet"
        
        # Install .NET 8 SDK
        Write-Host "Installing .NET 8 SDK..."
        & $scriptPath -Channel 8.0 -InstallDir "C:\Program Files\dotnet"
        
        # Install ASP.NET Core 8 Runtime
        Write-Host "Installing ASP.NET Core 8 Runtime..."
        & $scriptPath -Channel 8.0 -Runtime aspnetcore -InstallDir "C:\Program Files\dotnet"

  - name: ConfigureDotNet
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring .NET installation..."
        
        # Add .NET to system PATH if not already there
        $dotnetPath = "C:\Program Files\dotnet"
        $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
        
        if ($currentPath -notlike "*$dotnetPath*") {
            Write-Host "Adding .NET to system PATH..."
            $newPath = "$currentPath;$dotnetPath"
            [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
            Write-Host ".NET added to system PATH"
        } else {
            Write-Host ".NET is already in system PATH"
        }
        
        # Refresh environment variables for current session
        $env:PATH = [Environment]::GetEnvironmentVariable("PATH", "Machine")
        
        # Configure .NET telemetry (disable for enterprise environments)
        [Environment]::SetEnvironmentVariable("DOTNET_CLI_TELEMETRY_OPTOUT", "1", "Machine")
        Write-Host "Disabled .NET CLI telemetry"
        
        # Set .NET to use system-wide installation
        [Environment]::SetEnvironmentVariable("DOTNET_ROOT", "C:\Program Files\dotnet", "Machine")
        Write-Host "Set DOTNET_ROOT environment variable"

  - name: VerifyInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Verifying .NET installation..."
        
        # Refresh PATH for verification
        $env:PATH = [Environment]::GetEnvironmentVariable("PATH", "Machine")
        
        # Test .NET CLI
        try {
            $dotnetVersion = dotnet --version
            Write-Host "SUCCESS: .NET CLI is working, version: $dotnetVersion"
        } catch {
            Write-Error "FAILED: .NET CLI is not working: $($_.Exception.Message)"
            exit 1
        }
        
        # List installed SDKs
        Write-Host "`nInstalled .NET SDKs:"
        dotnet --list-sdks
        
        # List installed runtimes
        Write-Host "`nInstalled .NET Runtimes:"
        dotnet --list-runtimes
        
        # Test creating a simple console app
        Write-Host "`nTesting .NET functionality..."
        $testDir = "C:\temp\dotnet-test"
        if (Test-Path $testDir) {
            Remove-Item $testDir -Recurse -Force
        }
        
        try {
            dotnet new console -o $testDir --force
            Set-Location $testDir
            dotnet build
            $output = dotnet run
            Write-Host "Test app output: $output"
            Set-Location C:\
            Remove-Item $testDir -Recurse -Force
            Write-Host "SUCCESS: .NET functionality test passed"
        } catch {
            Write-Warning "FAILED: .NET functionality test failed: $($_.Exception.Message)"
        }

  - name: Cleanup
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Cleaning up installation files..."
        
        $tempDir = "C:\temp\dotnet"
        if (Test-Path $tempDir) {
            try {
                Remove-Item $tempDir -Recurse -Force
                Write-Host "Cleanup completed successfully"
            } catch {
                Write-Warning "Failed to clean up temp directory: $($_.Exception.Message)"
            }
        }

- name: validate
  steps:
  - name: ValidateDotNetInstallation
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Final validation of .NET installation..."
        
        # Check if .NET CLI is accessible
        try {
            $dotnetVersion = dotnet --version
            Write-Host "✓ .NET CLI is accessible, version: $dotnetVersion"
        } catch {
            Write-Error "VALIDATION FAILED: .NET CLI is not accessible"
            exit 1
        }
        
        # Check for expected .NET versions
        $expectedVersions = @("3.1", "5.0", "6.0", "7.0", "8.0")
        $installedSDKs = dotnet --list-sdks
        $installedRuntimes = dotnet --list-runtimes
        
        Write-Host "`nValidating installed versions:"
        foreach ($version in $expectedVersions) {
            $sdkFound = $installedSDKs | Where-Object { $_ -like "*$version*" }
            $runtimeFound = $installedRuntimes | Where-Object { $_ -like "*$version*" }
            
            if ($sdkFound -or $runtimeFound) {
                Write-Host "✓ .NET $version is installed"
            } else {
                Write-Warning "✗ .NET $version not found"
            }
        }
        
        Write-Host "`nVALIDATION SUCCESS: .NET installation completed"
        Write-Host "Installed SDKs:"
        dotnet --list-sdks
        Write-Host "`nInstalled Runtimes:"
        dotnet --list-runtimes
