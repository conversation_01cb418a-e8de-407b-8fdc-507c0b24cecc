# Install AWS Tools and Drivers
# This script installs AWS-specific tools and drivers for optimal EC2 performance

Write-Host "Installing AWS Tools and Drivers..."

# Set TLS 1.2 for downloads
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

# Install AWS CLI v2
Write-Host "Installing AWS CLI v2..."
try {
    $awsCliUrl = "https://awscli.amazonaws.com/AWSCLIV2.msi"
    $awsCliPath = "$env:TEMP\AWSCLIV2.msi"
    
    Invoke-WebRequest -Uri $awsCliUrl -OutFile $awsCliPath
    Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$awsCliPath`" /quiet" -Wait
    Remove-Item $awsCliPath -Force
    
    Write-Host "AWS CLI v2 installed successfully."
} catch {
    Write-Host "Failed to install AWS CLI v2: $($_.Exception.Message)"
}

# Install AWS PowerShell Module
Write-Host "Installing AWS PowerShell Module..."
try {
    Install-PackageProvider -Name NuGet -MinimumVersion ********* -Force
    Set-PSRepository -Name PSGallery -InstallationPolicy Trusted
    Install-Module -Name AWS.Tools.Installer -Force
    Install-AWSToolsModule AWS.Tools.EC2,AWS.Tools.S3,AWS.Tools.CloudWatch -Force
    
    Write-Host "AWS PowerShell Module installed successfully."
} catch {
    Write-Host "Failed to install AWS PowerShell Module: $($_.Exception.Message)"
}

# Install CloudWatch Agent
Write-Host "Installing CloudWatch Agent..."
try {
    $cloudWatchUrl = "https://s3.amazonaws.com/amazoncloudwatch-agent/windows/amd64/latest/amazon-cloudwatch-agent.msi"
    $cloudWatchPath = "$env:TEMP\amazon-cloudwatch-agent.msi"
    
    Invoke-WebRequest -Uri $cloudWatchUrl -OutFile $cloudWatchPath
    Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$cloudWatchPath`" /quiet" -Wait
    Remove-Item $cloudWatchPath -Force
    
    Write-Host "CloudWatch Agent installed successfully."
} catch {
    Write-Host "Failed to install CloudWatch Agent: $($_.Exception.Message)"
}

# Install SSM Agent (usually pre-installed on Windows AMIs)
Write-Host "Checking SSM Agent..."
$ssmService = Get-Service -Name "AmazonSSMAgent" -ErrorAction SilentlyContinue

if ($ssmService) {
    Write-Host "SSM Agent is already installed and running."
    Set-Service -Name "AmazonSSMAgent" -StartupType Automatic
} else {
    Write-Host "Installing SSM Agent..."
    try {
        $ssmUrl = "https://s3.amazonaws.com/ec2-downloads-windows/SSMAgent/latest/windows_amd64/AmazonSSMAgentSetup.exe"
        $ssmPath = "$env:TEMP\AmazonSSMAgentSetup.exe"
        
        Invoke-WebRequest -Uri $ssmUrl -OutFile $ssmPath
        Start-Process -FilePath $ssmPath -ArgumentList "/S" -Wait
        Remove-Item $ssmPath -Force
        
        Write-Host "SSM Agent installed successfully."
    } catch {
        Write-Host "Failed to install SSM Agent: $($_.Exception.Message)"
    }
}

# Install EC2 Instance Connect (for newer instances)
Write-Host "Installing EC2 Instance Connect..."
try {
    $ec2ConnectUrl = "https://s3.amazonaws.com/ec2-instance-connect/windows/latest/ec2-instance-connect.msi"
    $ec2ConnectPath = "$env:TEMP\ec2-instance-connect.msi"
    
    Invoke-WebRequest -Uri $ec2ConnectUrl -OutFile $ec2ConnectPath -ErrorAction SilentlyContinue
    if (Test-Path $ec2ConnectPath) {
        Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$ec2ConnectPath`" /quiet" -Wait
        Remove-Item $ec2ConnectPath -Force
        Write-Host "EC2 Instance Connect installed successfully."
    } else {
        Write-Host "EC2 Instance Connect not available for this Windows version."
    }
} catch {
    Write-Host "EC2 Instance Connect installation skipped: $($_.Exception.Message)"
}

# Configure services
Write-Host "Configuring AWS services..."

# Ensure EC2Config or EC2Launch is properly configured
$ec2ConfigPath = "C:\Program Files\Amazon\Ec2ConfigService\Ec2Config.exe"
$ec2LaunchPath = "C:\ProgramData\Amazon\EC2-Windows\Launch\Config\LaunchConfig.json"

if (Test-Path $ec2ConfigPath) {
    Write-Host "EC2Config service found - configuring..."
    # EC2Config is used in older Windows versions
    Set-Service -Name "Ec2Config" -StartupType Automatic
} elseif (Test-Path $ec2LaunchPath) {
    Write-Host "EC2Launch found - configuring..."
    # EC2Launch is used in newer Windows versions
    Set-Service -Name "EC2Launch" -StartupType Automatic
}

Write-Host "AWS Tools and Drivers installation completed."
