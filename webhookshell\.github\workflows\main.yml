name: .NET

on:
  push:
    branches: [ "master" ]
    paths:
      - 'src/*'
      - '**/main.yml'
jobs:
  docker-build-and-push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push
        run: |
          docker build --platform linux/amd64 -t mtokarevv/webhookshell:latest --push .