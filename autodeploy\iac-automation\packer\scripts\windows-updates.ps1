# Install Windows Updates
# This script installs all available Windows updates

Write-Host "Starting Windows Updates installation..."

# Install PSWindowsUpdate module if not present
if (!(Get-Module -ListAvailable -Name PSWindowsUpdate)) {
    Write-Host "Installing PSWindowsUpdate module..."
    Install-PackageProvider -Name NuGet -MinimumVersion ********* -Force
    Set-PSRepository -Name PSGallery -InstallationPolicy Trusted
    Install-Module -Name PSWindowsUpdate -Force
}

# Import the module
Import-Module PSWindowsUpdate

# Get and install all available updates
Write-Host "Checking for available updates..."
$updates = Get-WUList

if ($updates.Count -gt 0) {
    Write-Host "Found $($updates.Count) updates. Installing..."
    
    # Install updates with automatic reboot if required
    Install-WindowsUpdate -AcceptAll -AutoReboot -Confirm:$false
    
    Write-Host "Windows updates installation completed."
} else {
    Write-Host "No updates available."
}

# Alternative method using Windows Update API if PSW<PERSON>owsUpdate fails
if ($updates.Count -eq 0) {
    Write-Host "Trying alternative update method..."
    
    try {
        $updateSession = New-Object -ComObject Microsoft.Update.Session
        $updateSearcher = $updateSession.CreateUpdateSearcher()
        
        Write-Host "Searching for updates..."
        $searchResult = $updateSearcher.Search("IsInstalled=0 and Type='Software'")
        
        if ($searchResult.Updates.Count -gt 0) {
            Write-Host "Found $($searchResult.Updates.Count) updates via Windows Update API"
            
            $updatesToDownload = New-Object -ComObject Microsoft.Update.UpdateColl
            foreach ($update in $searchResult.Updates) {
                $updatesToDownload.Add($update) | Out-Null
            }
            
            # Download updates
            $downloader = $updateSession.CreateUpdateDownloader()
            $downloader.Updates = $updatesToDownload
            $downloadResult = $downloader.Download()
            
            # Install updates
            $installer = $updateSession.CreateUpdateInstaller()
            $installer.Updates = $updatesToDownload
            $installationResult = $installer.Install()
            
            Write-Host "Installation result: $($installationResult.ResultCode)"
            
            if ($installationResult.RebootRequired) {
                Write-Host "Reboot required after updates."
            }
        }
    }
    catch {
        Write-Host "Error with Windows Update API: $($_.Exception.Message)"
    }
}

Write-Host "Windows Updates script completed."
