// Authentication Module
const Auth = (() => {
  // Private variables
  let currentUser = null;
  let token = null;
  
  // Check for token in localStorage or URL
  const init = () => {
    // Check for token in localStorage
    token = localStorage.getItem('token');
    
    // Check for token in URL (for OAuth redirects)
    const urlParams = new URLSearchParams(window.location.search);
    const urlToken = urlParams.get('token');
    
    if (urlToken) {
      token = urlToken;
      localStorage.setItem('token', token);
      // Remove token from URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
    
    // If token exists, fetch user data
    if (token) {
      fetchCurrentUser();
    }
  };
  
  // Fetch current user data
  const fetchCurrentUser = async () => {
    try {
      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        currentUser = data.user;
        UI.updateUserInterface(true);
      } else {
        // Token is invalid or expired
        logout();
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      logout();
    }
  };
  
  // Register new user
  const register = async (username, email, password) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, email, password })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        UI.showAlert('Registration successful! Please login.', 'success');
        return true;
      } else {
        UI.showAlert(data.message || 'Registration failed', 'danger');
        return false;
      }
    } catch (error) {
      console.error('Registration error:', error);
      UI.showAlert('An error occurred during registration', 'danger');
      return false;
    }
  };
  
  // Login user
  const login = async (email, password) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        if (data.requireMfa) {
          // Show MFA form
          document.getElementById('login-form').classList.add('d-none');
          document.getElementById('mfa-form').classList.remove('d-none');
          // Store user ID for MFA verification
          sessionStorage.setItem('mfaUserId', data.userId);
          return { requireMfa: true };
        } else {
          // Login successful
          token = data.token;
          currentUser = data.user;
          localStorage.setItem('token', token);
          return { success: true };
        }
      } else {
        return { error: data.message || 'Login failed' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { error: 'An error occurred during login' };
    }
  };
  
  // Verify MFA token
  const verifyMfa = async (token) => {
    try {
      const userId = sessionStorage.getItem('mfaUserId');
      
      if (!userId) {
        return { error: 'MFA session expired' };
      }
      
      const response = await fetch('/api/auth/verify-mfa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userId, token })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        // MFA verification successful
        Auth.token = data.token;
        currentUser = data.user;
        localStorage.setItem('token', data.token);
        sessionStorage.removeItem('mfaUserId');
        return { success: true };
      } else {
        return { error: data.message || 'MFA verification failed' };
      }
    } catch (error) {
      console.error('MFA verification error:', error);
      return { error: 'An error occurred during MFA verification' };
    }
  };
  
  // Setup MFA
  const setupMfa = async () => {
    try {
      if (!currentUser) {
        return { error: 'Not authenticated' };
      }
      
      const response = await fetch('/api/auth/setup-mfa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ userId: currentUser.id })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        return {
          success: true,
          secret: data.secret,
          qrCode: data.qrCode
        };
      } else {
        return { error: data.message || 'MFA setup failed' };
      }
    } catch (error) {
      console.error('MFA setup error:', error);
      return { error: 'An error occurred during MFA setup' };
    }
  };
  
  // Enable MFA
  const enableMfa = async (token) => {
    try {
      if (!currentUser) {
        return { error: 'Not authenticated' };
      }
      
      const response = await fetch('/api/auth/enable-mfa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${Auth.token}`
        },
        body: JSON.stringify({ userId: currentUser.id, token })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        currentUser.mfaEnabled = true;
        return { success: true };
      } else {
        return { error: data.message || 'Failed to enable MFA' };
      }
    } catch (error) {
      console.error('Enable MFA error:', error);
      return { error: 'An error occurred while enabling MFA' };
    }
  };
  
  // Disable MFA
  const disableMfa = async (token) => {
    try {
      if (!currentUser) {
        return { error: 'Not authenticated' };
      }
      
      const response = await fetch('/api/auth/disable-mfa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${Auth.token}`
        },
        body: JSON.stringify({ userId: currentUser.id, token })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        currentUser.mfaEnabled = false;
        return { success: true };
      } else {
        return { error: data.message || 'Failed to disable MFA' };
      }
    } catch (error) {
      console.error('Disable MFA error:', error);
      return { error: 'An error occurred while disabling MFA' };
    }
  };
  
  // Logout user
  const logout = () => {
    fetch('/api/auth/logout', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }).catch(error => console.error('Logout error:', error));
    
    // Clear user data and token
    currentUser = null;
    token = null;
    localStorage.removeItem('token');
    
    // Update UI
    UI.updateUserInterface(false);
  };
  
  // Google OAuth login
  const googleLogin = () => {
    window.location.href = '/api/auth/google';
  };
  
  // Public methods and properties
  return {
    init,
    register,
    login,
    verifyMfa,
    setupMfa,
    enableMfa,
    disableMfa,
    logout,
    googleLogin,
    get currentUser() {
      return currentUser;
    },
    get token() {
      return token;
    },
    set token(value) {
      token = value;
    },
    get isAuthenticated() {
      return !!currentUser;
    }
  };
})();