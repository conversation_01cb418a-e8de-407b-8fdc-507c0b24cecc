from datetime import datetime, timedelta
from java.util import Calendar, Date, TimeZone

# Get UTC timezone
utc_timezone = TimeZone.getTimeZone("Africa/Johannesburg")

# Create Calendar instance with UTC timezone
calendar = Calendar.getInstance(utc_timezone)

actionDate = releaseVariables['act_date']
# Convert string date to datetime and subtract 2 hours
if isinstance(actionDate, str):
    # Parse the ISO format string to datetime
    dt = datetime.strptime(actionDate, '%Y-%m-%dT%H:%M:%SZ')
    # Subtract 2 hours
    dt = dt - timedelta(hours=4)
    # Convert datetime back to Java Date
    calendar.set(dt.year, dt.month - 1, dt.day, dt.hour, dt.minute, dt.second)
    actionDate = calendar.getTime()
elif isinstance(actionDate, Date):
    calendar.setTime(actionDate)
    actionDate = calendar.getTime()

print("PHASE START: ", type(actionDate), actionDate)

release.title = releaseVariables['jira_ref']
releaseApi.updateRelease(release)

phases = []
for p in release.phases:
    print("ID: ", p.id)
    phases.append(p.id)
actionPhase = phaseApi.getPhase(phases[1])

# Create a new Calendar instance for start date
startDateCal = Calendar.getInstance(utc_timezone)
startDateCal.setTime(actionDate)
startDateCal.add(Calendar.HOUR_OF_DAY, -4)
startDate = startDateCal.getTime()

# Set Start date to Action Date entered by User
actionPhase.setStartDate(startDate)
actionPhase.setScheduledStartDate(startDate)
actionPhase.setTitle("COOLDOWN until " + str(startDate))
actionPhase.setFlagComment("COOLDOWN")
# Apply updated properties
phaseApi.updatePhase(actionPhase)

taskslist = []
for item in actionPhase.tasks:
    if item.title == "Check to Proceed":
        t = taskApi.getTask(item.id)
        t.setScheduledStartDate(startDate)
        t.setStartDate(startDate)
        t.setFlagComment("COOLDOWN")
        t.setDescription("Scheduled for " + str(actionDate))
        taskApi.updateTask(t)