<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Manga Manager</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
  <!-- Custom CSS -->
  <link rel="stylesheet" href="css/style.css">
  <!-- Favicon -->
  <link rel="icon" href="images/favicon.png" type="image/png">
</head>
<body>
  <div class="wrapper">
    <!-- Sidebar -->
    <nav id="sidebar" class="sidebar">
      <div class="sidebar-header">
        <h3>Manga Manager</h3>
        <div class="sidebar-close d-md-none">
          <i class="bi bi-x"></i>
        </div>
      </div>

      <div class="sidebar-user">
        <div id="user-profile" class="d-flex align-items-center mb-3">
          <img src="images/default-avatar.png" alt="User Avatar" class="avatar me-2">
          <div class="user-info">
            <div id="username">Guest</div>
            <small id="user-email"></small>
          </div>
        </div>
      </div>

      <ul class="list-unstyled components">
        <li class="active">
          <a href="#" data-page="dashboard">
            <i class="bi bi-speedometer2"></i> Dashboard
          </a>
        </li>
        <li>
          <a href="#" data-page="library">
            <i class="bi bi-book"></i> My Library
          </a>
        </li>
        <li>
          <a href="#" data-page="search">
            <i class="bi bi-search"></i> Search
          </a>
        </li>
        <li>
          <a href="#" data-page="categories">
            <i class="bi bi-tags"></i> Categories
          </a>
        </li>
        <li>
          <a href="#" data-page="bookmarks">
            <i class="bi bi-bookmark"></i> Bookmarks
          </a>
        </li>
        <li>
          <a href="#" data-page="upload">
            <i class="bi bi-upload"></i> Upload
          </a>
        </li>
        <li>
          <a href="#" data-page="settings">
            <i class="bi bi-gear"></i> Settings
          </a>
        </li>
      </ul>

      <div class="sidebar-footer">
        <div class="theme-toggle">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="darkModeToggle">
            <label class="form-check-label" for="darkModeToggle">Dark Mode</label>
          </div>
        </div>
        <div id="logout-btn" class="d-none">
          <a href="#" class="btn btn-outline-danger btn-sm w-100">
            <i class="bi bi-box-arrow-right"></i> Logout
          </a>
        </div>
      </div>
    </nav>

    <!-- Page Content -->
    <div id="content">
      <!-- Navbar -->
      <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container-fluid">
          <button type="button" id="sidebarCollapse" class="btn btn-primary">
            <i class="bi bi-list"></i>
          </button>
          <div class="ms-auto d-flex">
            <div id="auth-buttons">
              <button id="login-btn" class="btn btn-outline-primary me-2">Login</button>
              <button id="register-btn" class="btn btn-primary">Register</button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content Area -->
      <div class="container-fluid content-area">
        <!-- Dashboard Page -->
        <div id="dashboard-page" class="content-page active">
          <h2>Dashboard</h2>
          <div class="row" id="dashboard-content">
            <div class="col-12">
              <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> Welcome to Manga Manager! Please login to see your personalized dashboard.
              </div>
            </div>
          </div>
        </div>

        <!-- Library Page -->
        <div id="library-page" class="content-page">
          <h2>My Library</h2>
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="input-group">
                <input type="text" class="form-control" placeholder="Filter library...">
                <button class="btn btn-outline-secondary" type="button">
                  <i class="bi bi-search"></i>
                </button>
              </div>
            </div>
            <div class="col-md-6 text-md-end mt-2 mt-md-0">
              <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary">
                  <i class="bi bi-grid"></i>
                </button>
                <button type="button" class="btn btn-outline-primary">
                  <i class="bi bi-list-ul"></i>
                </button>
              </div>
              <div class="btn-group ms-2" role="group">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                  Sort By
                </button>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="#">Title (A-Z)</a></li>
                  <li><a class="dropdown-item" href="#">Title (Z-A)</a></li>
                  <li><a class="dropdown-item" href="#">Recently Added</a></li>
                  <li><a class="dropdown-item" href="#">Recently Read</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="row" id="library-content">
            <div class="col-12">
              <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> Please login to view your library.
              </div>
            </div>
          </div>
        </div>

        <!-- Search Page -->
        <div id="search-page" class="content-page">
          <h2>Search Manga</h2>
          <div class="row mb-4">
            <div class="col-md-8 mx-auto">
              <div class="card">
                <div class="card-body">
                  <div class="input-group mb-3">
                    <input type="text" id="search-input" class="form-control form-control-lg" placeholder="Search for manga...">
                    <button class="btn btn-primary" type="button" id="search-button">
                      <i class="bi bi-search"></i> Search
                    </button>
                  </div>
                  <div class="d-flex justify-content-between">
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="include-explicit">
                      <label class="form-check-label" for="include-explicit">Include Explicit Content</label>
                    </div>
                    <div>
                      <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#advancedSearch">
                        Advanced Search
                      </button>
                    </div>
                  </div>
                  <div class="collapse mt-3" id="advancedSearch">
                    <div class="row g-3">
                      <div class="col-md-6">
                        <label class="form-label">Category</label>
                        <select class="form-select" id="category-filter">
                          <option value="">All Categories</option>
                        </select>
                      </div>
                      <div class="col-md-6">
                        <label class="form-label">Sort By</label>
                        <select class="form-select" id="sort-option">
                          <option value="relevance">Relevance</option>
                          <option value="newest">Newest</option>
                          <option value="popularity">Popularity</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="search-results" class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-4">
            <!-- Search results will be displayed here -->
          </div>
          <div id="search-pagination" class="d-flex justify-content-center mt-4">
            <!-- Pagination will be displayed here -->
          </div>
        </div>

        <!-- Categories Page -->
        <div id="categories-page" class="content-page">
          <h2>Categories</h2>
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="input-group">
                <input type="text" class="form-control" placeholder="Filter categories...">
                <button class="btn btn-outline-secondary" type="button">
                  <i class="bi bi-search"></i>
                </button>
              </div>
            </div>
            <div class="col-md-6 text-md-end mt-2 mt-md-0">
              <button class="btn btn-primary" id="manage-categories-btn">
                <i class="bi bi-gear"></i> Manage Categories
              </button>
            </div>
          </div>
          <div class="row" id="categories-content">
            <div class="col-12">
              <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> Please login to view and manage your categories.
              </div>
            </div>
          </div>
        </div>

        <!-- Bookmarks Page -->
        <div id="bookmarks-page" class="content-page">
          <h2>Bookmarks</h2>
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="input-group">
                <input type="text" class="form-control" placeholder="Filter bookmarks...">
                <button class="btn btn-outline-secondary" type="button">
                  <i class="bi bi-search"></i>
                </button>
              </div>
            </div>
            <div class="col-md-6 text-md-end mt-2 mt-md-0">
              <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary">
                  <i class="bi bi-grid"></i>
                </button>
                <button type="button" class="btn btn-outline-primary">
                  <i class="bi bi-list-ul"></i>
                </button>
              </div>
            </div>
          </div>
          <div class="row" id="bookmarks-content">
            <div class="col-12">
              <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> Please login to view your bookmarks.
              </div>
            </div>
          </div>
        </div>

        <!-- Upload Page -->
        <div id="upload-page" class="content-page">
          <h2>Upload Manga</h2>
          <div class="row">
            <div class="col-md-8 mx-auto">
              <div class="card">
                <div class="card-body">
                  <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> Please login to upload manga.
                  </div>
                  <div id="upload-form" class="d-none">
                    <div class="mb-3">
                      <label for="manga-files" class="form-label">Select Files</label>
                      <input class="form-control" type="file" id="manga-files" multiple accept=".cbz,.cbr,.zip,.rar,.png,.jpg,.jpeg">
                      <div class="form-text">Supported formats: CBZ, CBR, ZIP, RAR, or image files</div>
                    </div>
                    <div class="mb-3">
                      <label for="manga-title" class="form-label">Title</label>
                      <input type="text" class="form-control" id="manga-title">
                    </div>
                    <div class="mb-3">
                      <label for="manga-category" class="form-label">Category</label>
                      <select class="form-select" id="manga-category">
                        <option value="">Select Category</option>
                      </select>
                    </div>
                    <div class="mb-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is-explicit">
                        <label class="form-check-label" for="is-explicit">
                          Mark as Explicit Content
                        </label>
                      </div>
                    </div>
                    <div class="d-grid">
                      <button class="btn btn-primary" type="button" id="upload-button">
                        <i class="bi bi-upload"></i> Upload
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Settings Page -->
        <div id="settings-page" class="content-page">
          <h2>Settings</h2>
          <div class="row">
            <div class="col-md-8 mx-auto">
              <div class="card mb-4">
                <div class="card-header">
                  <h5 class="mb-0">Account Settings</h5>
                </div>
                <div class="card-body">
                  <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> Please login to manage your account settings.
                  </div>
                  <div id="account-settings" class="d-none">
                    <div class="mb-3">
                      <label for="settings-username" class="form-label">Username</label>
                      <input type="text" class="form-control" id="settings-username">
                    </div>
                    <div class="mb-3">
                      <label for="settings-email" class="form-label">Email</label>
                      <input type="email" class="form-control" id="settings-email" readonly>
                    </div>
                    <div class="mb-3">
                      <label class="form-label">Profile Picture</label>
                      <div class="d-flex align-items-center">
                        <img src="images/default-avatar.png" alt="Profile Picture" class="avatar me-3" style="width: 64px; height: 64px;">
                        <button class="btn btn-outline-primary btn-sm">Change Picture</button>
                      </div>
                    </div>
                    <div class="mb-3">
                      <label class="form-label">Multi-Factor Authentication</label>
                      <div class="d-flex align-items-center">
                        <div class="form-check form-switch me-3">
                          <input class="form-check-input" type="checkbox" id="mfa-toggle">
                          <label class="form-check-label" for="mfa-toggle">Enable MFA</label>
                        </div>
                        <button class="btn btn-outline-primary btn-sm" id="setup-mfa-btn">Setup MFA</button>
                      </div>
                    </div>
                    <div class="d-grid">
                      <button class="btn btn-primary" type="button" id="save-account-settings">
                        Save Changes
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card mb-4">
                <div class="card-header">
                  <h5 class="mb-0">Appearance</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label class="form-label">Theme</label>
                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="settings-dark-mode">
                      <label class="form-check-label" for="settings-dark-mode">Dark Mode</label>
                    </div>
                  </div>
                  <div class="mb-3">
                    <label for="dashboard-categories" class="form-label">Dashboard Categories</label>
                    <select class="form-select" id="dashboard-categories" multiple disabled>
                      <option>Please login to select categories</option>
                    </select>
                    <div class="form-text">Select categories to display on your dashboard</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modals -->
  <!-- Login Modal -->
  <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="loginModalLabel">Login</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="login-form">
            <div class="mb-3">
              <label for="login-email" class="form-label">Email</label>
              <input type="email" class="form-control" id="login-email" required>
            </div>
            <div class="mb-3">
              <label for="login-password" class="form-label">Password</label>
              <input type="password" class="form-control" id="login-password" required>
            </div>
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary">Login</button>
              <button type="button" class="btn btn-outline-primary" id="google-login-btn">
                <i class="bi bi-google"></i> Login with Google
              </button>
            </div>
          </form>
          <div id="mfa-form" class="d-none mt-3">
            <div class="alert alert-info">
              <i class="bi bi-shield-lock"></i> Multi-Factor Authentication Required
            </div>
            <div class="mb-3">
              <label for="mfa-token" class="form-label">Enter 6-digit code from your authenticator app</label>
              <input type="text" class="form-control" id="mfa-token" maxlength="6" required>
            </div>
            <div class="d-grid">
              <button type="button" class="btn btn-primary" id="verify-mfa-btn">Verify</button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="w-100 text-center">
            <p class="mb-0">Don't have an account? <a href="#" id="switch-to-register">Register</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Register Modal -->
  <div class="modal fade" id="registerModal" tabindex="-1" aria-labelledby="registerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="registerModalLabel">Register</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="register-form">
            <div class="mb-3">
              <label for="register-username" class="form-label">Username</label>
              <input type="text" class="form-control" id="register-username" required>
            </div>
            <div class="mb-3">
              <label for="register-email" class="form-label">Email</label>
              <input type="email" class="form-control" id="register-email" required>
            </div>
            <div class="mb-3">
              <label for="register-password" class="form-label">Password</label>
              <input type="password" class="form-control" id="register-password" required>
            </div>
            <div class="mb-3">
              <label for="register-confirm-password" class="form-label">Confirm Password</label>
              <input type="password" class="form-control" id="register-confirm-password" required>
            </div>
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary">Register</button>
              <button type="button" class="btn btn-outline-primary" id="google-register-btn">
                <i class="bi bi-google"></i> Register with Google
              </button>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <div class="w-100 text-center">
            <p class="mb-0">Already have an account? <a href="#" id="switch-to-login">Login</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- MFA Setup Modal -->
  <div class="modal fade" id="mfaSetupModal" tabindex="-1" aria-labelledby="mfaSetupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="mfaSetupModalLabel">Setup Multi-Factor Authentication</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="text-center mb-3">
            <p>Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)</p>
            <div id="qrcode-container" class="d-flex justify-content-center mb-3">
              <img id="mfa-qrcode" src="" alt="QR Code" class="img-fluid border">
            </div>
            <p>Or enter this code manually:</p>
            <div class="input-group mb-3">
              <input type="text" id="mfa-secret" class="form-control text-center" readonly>
              <button class="btn btn-outline-secondary" type="button" id="copy-secret-btn">
                <i class="bi bi-clipboard"></i>
              </button>
            </div>
          </div>
          <div class="mb-3">
            <label for="mfa-setup-token" class="form-label">Enter 6-digit code from your authenticator app</label>
            <input type="text" class="form-control" id="mfa-setup-token" maxlength="6" required>
          </div>
          <div class="d-grid">
            <button type="button" class="btn btn-primary" id="enable-mfa-btn">Enable MFA</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Manga Details Modal -->
  <div class="modal fade" id="mangaDetailsModal" tabindex="-1" aria-labelledby="mangaDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="mangaDetailsModalLabel">Manga Details</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-4 text-center">
              <img id="manga-cover" src="" alt="Manga Cover" class="img-fluid rounded mb-3">
              <div class="d-grid gap-2">
                <button class="btn btn-primary" id="read-manga-btn">
                  <i class="bi bi-book"></i> Read
                </button>
                <button class="btn btn-outline-primary" id="download-manga-btn">
                  <i class="bi bi-download"></i> Download
                </button>
                <button class="btn btn-outline-secondary" id="bookmark-manga-btn">
                  <i class="bi bi-bookmark"></i> Bookmark
                </button>
              </div>
            </div>
            <div class="col-md-8">
              <h4 id="manga-title-detail"></h4>
              <div class="mb-3">
                <span class="badge bg-primary me-1" id="manga-category-badge"></span>
                <span class="badge bg-danger me-1 d-none" id="manga-explicit-badge">Explicit</span>
              </div>
              <div class="mb-3">
                <strong>Chapters:</strong> <span id="manga-chapters-count"></span>
              </div>
              <div class="mb-3">
                <strong>Last Read:</strong> <span id="manga-last-read">Never</span>
              </div>
              <div class="mb-3">
                <strong>Added On:</strong> <span id="manga-added-date"></span>
              </div>
              <div class="mb-3">
                <strong>Description:</strong>
                <p id="manga-description" class="mt-2"></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Reader Modal -->
  <div class="modal fade" id="readerModal" tabindex="-1" aria-labelledby="readerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="readerModalLabel">Reading: <span id="reading-title"></span></h5>
          <div class="ms-auto me-2">
            <span id="page-indicator">Page 1 of 1</span>
          </div>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-0">
          <div class="reader-container">
            <div class="reader-controls reader-prev">
              <button class="btn btn-light rounded-circle" id="prev-page-btn">
                <i class="bi bi-chevron-left"></i>
              </button>
            </div>
            <div class="reader-image-container">
              <img id="reader-image" src="" alt="Manga Page" class="img-fluid">
            </div>
            <div class="reader-controls reader-next">
              <button class="btn btn-light rounded-circle" id="next-page-btn">
                <i class="bi bi-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="d-flex justify-content-between w-100">
            <div>
              <button class="btn btn-outline-secondary" id="first-page-btn">
                <i class="bi bi-chevron-double-left"></i> First
              </button>
            </div>
            <div>
              <div class="btn-group" role="group">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                  Chapters
                </button>
                <ul class="dropdown-menu" id="chapter-selector">
                  <!-- Chapter list will be populated here -->
                </ul>
              </div>
              <button class="btn btn-outline-primary ms-2" id="bookmark-page-btn">
                <i class="bi bi-bookmark"></i> Bookmark
              </button>
            </div>
            <div>
              <button class="btn btn-outline-secondary" id="last-page-btn">
                Last <i class="bi bi-chevron-double-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS Bundle with Popper -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <!-- Custom JS -->
  <script src="js/auth.js"></script>
  <script src="js/ui.js"></script>
  <script src="js/manga.js"></script>
  <script src="js/reader.js"></script>
  <script src="js/app.js"></script>
</body>
</html>