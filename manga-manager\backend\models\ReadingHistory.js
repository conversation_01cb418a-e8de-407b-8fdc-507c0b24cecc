module.exports = (sequelize, DataTypes) => {
  const ReadingHistory = sequelize.define('ReadingHistory', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    mangaId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Mangas',
        key: 'id'
      }
    },
    chapterId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'Chapters',
        key: 'id'
      }
    },
    pageNumber: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    readDate: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    timeSpent: {
      type: DataTypes.INTEGER, // in seconds
      allowNull: true
    },
    completed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    }
  }, {
    timestamps: true
  });

  ReadingHistory.associate = (models) => {
    ReadingHistory.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    ReadingHistory.belongsTo(models.Manga, {
      foreignKey: 'mangaId',
      as: 'manga'
    });
    ReadingHistory.belongsTo(models.Chapter, {
      foreignKey: 'chapterId',
      as: 'chapter'
    });
  };

  return ReadingHistory;
};