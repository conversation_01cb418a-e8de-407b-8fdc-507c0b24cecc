# Initialize default values
paramAddRecord = ''
updateMessageReceived = ''
#deleteRecord = False
#addRecord = False
#updateRecord = False
dnsDevURL = r'https://ad-api.dev.mud.internal.co.za/webhook/v1?'
key = r'key=18adecad-df09-47e2-ae05-de7686ae3156'
script = r'&script=dnschange.ps1'#'&script=dnschange.ps1' '&healthTesth.ps1'
processor = r'dev\svcDevDnsAutomation'
devuser = r'svcDevSlmDcsRelease'
record_type = releaseVariables['record_type']
category = releaseVariables['category']
releaseVariables['processor'] = processor
releaseVariables['devuser'] = devuser

# Validate required fields
required_fields = ['record_type', 'category']
for field in required_fields:
    if field not in releaseVariables:
        raise Exception('Missing required field: {0}'.format(field))
    
if releaseVariables['record_type'] == 'A-Record' and releaseVariables['category'] == 'Update Record':
    updateMessageReceived = 'We will {0} of type {1} from {2} - {3} to {4} - {5}'.format(
        releaseVariables['category'],
        releaseVariables['record_type'],
        releaseVariables['hostname'],
        releaseVariables['ip_address'],
        releaseVariables['new_hostname'],
        releaseVariables['new_ip_address']
    )
    paramAddRecord = '&param=-reference {0} -requester {1} -processor {2} -task AddA -source {3} -destination {4} -sequence 1'.format(
        releaseVariables['jira_ref'],
        releaseVariables['devuser'],
        releaseVariables['processor'],
        releaseVariables['new_hostname'],
        releaseVariables['new_ip_address']
    )    
elif releaseVariables['record_type'] == 'C-Record' and releaseVariables['category'] == 'Update Record':
    updateMessageReceived = 'We will {0} of type {1} from {2} - {3} to {4} - {5}'.format(
        releaseVariables['category'],
        releaseVariables['record_type'],
        releaseVariables['host_alias'],
        releaseVariables['hostname'],
        releaseVariables['new_host_alias'],
        releaseVariables['new_hostname']
    )
    paramAddRecord = '&param=-reference {0} -requester {1} -processor {2} -task AddC -source {3} -destination {4} -sequence 1'.format(
        releaseVariables['jira_ref'],
        releaseVariables['devuser'],
        releaseVariables['processor'],
        releaseVariables['new_host_alias'],
        releaseVariables['new_hostname']
    )
elif releaseVariables['record_type'] == 'A-Record' and releaseVariables['category'] == 'Add Record':
    updateMessageReceived = 'We will {0} of type {1} - {2} - {3}'.format(
        releaseVariables['category'],
        releaseVariables['record_type'],
        releaseVariables['hostname'],
        releaseVariables['ip_address']
    )
    paramAddRecord = '&param=-reference {0} -requester {1} -processor {2} -task AddA -source {3} -destination {4}'.format(
        releaseVariables['jira_ref'],
        releaseVariables['devuser'],
        releaseVariables['processor'],
        releaseVariables['hostname'],
        releaseVariables['ip_address']
    )
elif releaseVariables['record_type'] == 'C-Record' and releaseVariables['category'] == 'Add Record':
    updateMessageReceived = 'We will {0} of type {1} - {2} - {3}'.format(
        releaseVariables['category'],
        releaseVariables['record_type'],
        releaseVariables['host_alias'],
        releaseVariables['hostname']
    )
    paramAddRecord = '&param=-reference {0} -requester {1} -processor {2} -task AddC -source {3} -destination {4}'.format(
        releaseVariables['jira_ref'],
        releaseVariables['devuser'],
        releaseVariables['processor'],
        releaseVariables['host_alias'],
        releaseVariables['hostname']
    )
else:
    updateMessageReceived = 'This request has missing information! Unable to proceed.'
    raise ValueError('This request has missing information! Unable to proceed.')

releaseVariables['updateMessageReceived'] = updateMessageReceived
releaseVariables['paramAddRecord'] = paramAddRecord

final_dnsAddDevURL = dnsDevURL + key + script + paramAddRecord if paramAddRecord else ''
if final_dnsAddDevURL:
    dnsAddProceed = True
    releaseVariables['dnsAddProceed'] = dnsAddProceed
else:
    dnsAddProceed = False
    releaseVariables['dnsAddProceed'] = dnsAddProceed

releaseVariables['dnsDevURL'] = dnsDevURL
releaseVariables['key'] = key
releaseVariables['script'] = script
releaseVariables['processor'] = processor
releaseVariables['final_dnsAddDevURL'] = final_dnsAddDevURL if final_dnsAddDevURL else ''