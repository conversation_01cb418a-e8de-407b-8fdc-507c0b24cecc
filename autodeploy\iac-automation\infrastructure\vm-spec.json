{"metadata": {"name": "multi-datacenter-deployment", "version": "1.0.0", "workflow_id": "WF-2025-001"}, "infrastructure": {"jobspecs": [{"jobspec_id": "JS-001", "vcenter_endpoint": "srv009972.mud.internal.co.za", "datacenter": "DC1", "cluster": "Cluster1", "vm_configuration": {"vm_name": "web-server-dc1", "vm_count": 1, "vm_memory": 4096, "vm_cpu": 4, "network_configuration": {"primary_interface": {"vswitch": "vSwitch-Production", "port_group": "PG-Web-Servers-VLAN100", "vlan_id": 100, "ip_address": "**************", "subnet_mask": "*************", "gateway": "*************", "dns_servers": ["************", "************"], "dns_domain": "company.local", "dhcp_enabled": false}, "secondary_interface": {"vswitch": "vSwitch-Backup", "port_group": "PG-Backup-VLAN200", "vlan_id": 200, "ip_address": "**************", "subnet_mask": "*************", "gateway": "*************", "dhcp_enabled": false}}, "disk_configuration": [{"drive_letter": "C", "size_gb": 80, "datastore": "DS_SSD_DC1", "type": "thin", "disk_mode": "persistent", "controller_type": "scsi", "unit_number": 0, "block_size": 4096, "purpose": "os", "notes": "TODO: Document parameters for disk configuration modes and types."}, {"drive_letter": "D", "size_gb": 100, "datastore": "DS_DATA_DC1", "type": "thick_eager_zeroed", "disk_mode": "persistent", "controller_type": "scsi", "unit_number": 1, "block_size": 4096, "purpose": "application_data", "notes": "TODO: Document parameters for disk configuration modes and types."}]}}, {"jobspec_id": "JS-002", "vcenter_endpoint": "srv009972.mud.internal.co.za", "datacenter": "DC2", "cluster": "Cluster2", "vm_configuration": {"vm_name": "mssql-server-dc2", "vm_count": 1, "vm_memory": 8192, "vm_cpu": 4, "network_configuration": {"primary_interface": {"vswitch": "vSwitch-Database", "port_group": "PG-Database-VLAN300", "vlan_id": 300, "ip_address": "192.168.300.20", "subnet_mask": "*************", "gateway": "192.168.300.1", "dns_servers": ["************", "************"], "dns_domain": "company.local", "dhcp_enabled": false}}, "disk_configuration": [{"drive_letter": "C", "size_gb": 80, "datastore": "DS_SSD_DC2", "type": "thick_eager_zeroed", "disk_mode": "persistent", "controller_type": "scsi", "unit_number": 0, "block_size": 4096, "purpose": "os", "notes": "Default 4K block size for OS disk"}, {"drive_letter": "D", "size_gb": 500, "datastore": "DS_DATABASE_DC2", "type": "thick_eager_zeroed", "disk_mode": "persistent", "controller_type": "scsi", "unit_number": 1, "block_size": 65536, "purpose": "mssql_data", "notes": "MSSQL files - 64K block size for optimal performance"}, {"drive_letter": "L", "size_gb": 200, "datastore": "DS_LOGS_DC2", "type": "thick_eager_zeroed", "disk_mode": "persistent", "controller_type": "scsi", "unit_number": 2, "block_size": 65536, "purpose": "mssql_logs", "notes": "MSSQL transaction logs - 64K block size for sequential writes"}, {"drive_letter": "T", "size_gb": 100, "datastore": "DS_TEMP_DC2", "type": "thick_eager_zeroed", "disk_mode": "persistent", "controller_type": "scsi", "unit_number": 3, "block_size": 65536, "purpose": "mssql_tempdb", "notes": "MSSQL TempDB - 64K block size for high I/O workloads"}]}}]}}