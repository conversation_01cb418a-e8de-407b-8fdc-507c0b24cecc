# Example AWS Image Builder Recipe with SCCM Agent
# This recipe creates a Windows Server 2022 image with SCCM agent pre-installed

name: WindowsServer2022WithSCCM
description: Windows Server 2022 base image with SCCM agent, .NET 4.8, and enterprise configurations
schemaVersion: 1.0
version: 1.0.0

# Base image - Windows Server 2022 (automatically uses latest)
parentImage: Windows_Server-2022-English-Full-Base

# Build and test components
components:
# AWS managed component for Windows updates
- name: update-windows
  parameters:
  - name: exclude
    value:
    - "KB5005463" # Example: exclude specific updates if needed
  - name: include
    value: []

# Install .NET Framework 4.8 (prerequisite for many enterprise applications)
- name: InstallDotNet48
  parameters: []

# Install SCCM client agent (our new component)
- name: win-server-sccm-agent
  parameters: []

# Configure Windows features and roles
- name: win-server-features
  parameters:
  - name: features
    value:
    - "RSAT-AD-PowerShell"
    - "RSAT-DNS-Server"
    - "Telnet-Client"
    - "SNMP-Service"

# Install Visual C++ Redistributables
- name: win-server-vcredist
  parameters: []

# Configure registry optimizations
- name: ConfigureRegistryTweaks
  parameters: []

# Configure firewall rules for SCCM communication
- name: ConfigureFirewallRules
  parameters:
  - name: additional_rules
    value:
    - name: "SCCM-HTTP-In"
      direction: "Inbound"
      action: "Allow"
      protocol: "TCP"
      port: "80"
      description: "SCCM HTTP Communication"
    - name: "SCCM-HTTPS-In"
      direction: "Inbound"
      action: "Allow"
      protocol: "TCP"
      port: "443"
      description: "SCCM HTTPS Communication"
    - name: "SCCM-WMI-In"
      direction: "Inbound"
      action: "Allow"
      protocol: "TCP"
      port: "135"
      description: "SCCM WMI Communication"

# Configure Windows Update settings for SCCM management
- name: win-server-windows-updates
  parameters:
  - name: wsus_server
    value: "{{ssm:/imagebuilder/wsus/server}}"
  - name: auto_update_option
    value: "2" # Download but don't install (let SCCM manage)

# Install PowerShell 7 for modern management
- name: win-server-powershell7
  parameters: []

# Configure remote desktop settings
- name: configure-remote-desktop
  parameters:
  - name: enable_rdp
    value: "true"
  - name: require_nla
    value: "true"

# Final reboot to ensure all changes take effect
- name: reboot-windows
  parameters: []

# Working directory for build process
workingDirectory: C:\ImageBuilder

# Additional metadata
tags:
  Environment: Production
  OS: Windows Server 2022
  Version: "1.0.0"
  Components: "SCCM Agent, .NET Framework 4.8, PowerShell 7, RSAT Tools"
  CreatedBy: AWS Image Builder
  Purpose: Enterprise Windows Server with SCCM Management
  SCCMEnabled: "true"
  LastUpdated: "2024-01-15"

# Test components (optional - for validation)
tests:
- name: ValidateSCCMInstallation
  action: ExecutePowerShell
  inputs:
    commands:
    - |
      # Test SCCM service
      $sccmService = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
      if (!$sccmService -or $sccmService.Status -ne "Running") {
          Write-Error "SCCM service validation failed"
          exit 1
      }
      Write-Host "✓ SCCM service is running"

      # Test .NET Framework
      $dotNetVersion = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue
      if (!$dotNetVersion -or $dotNetVersion.Release -lt 528040) {
          Write-Error ".NET Framework 4.8 validation failed"
          exit 1
      }
      Write-Host "✓ .NET Framework 4.8 is installed"

      # Test PowerShell 7
      $pwsh7 = Get-Command pwsh -ErrorAction SilentlyContinue
      if (!$pwsh7) {
          Write-Error "PowerShell 7 validation failed"
          exit 1
      }
      Write-Host "✓ PowerShell 7 is installed"

      Write-Host "✓ All component validations passed"

# Distribution settings (example)
distributions:
- region: af-south-1
  amiDistributionConfiguration:
    name: "WindowsServer2022-SCCM-{{ imagebuilder:buildDate }}"
    description: "Windows Server 2022 with SCCM agent - Built on {{ imagebuilder:buildDate }}"
    targetAccountIds:
    - "************" # Replace with your account ID
    amiTags:
      Name: "WindowsServer2022-SCCM-{{ imagebuilder:buildDate }}"
      OS: "Windows Server 2022"
      SCCMEnabled: "true"
      BuildDate: "{{ imagebuilder:buildDate }}"
      BaseAMI: "{{ imagebuilder:parentImage }}"
    licenseConfigurationArns:
    - "arn:aws:license-manager:af-south-1:************:license-configuration:lic-0123456789abcdef0" # Replace with your license configuration

# Build timeout (in minutes)
buildTimeout: 120

# Instance types for building
buildInstanceTypes:
- m5.large
- m5.xlarge
