<#
    .SYNOPSIS
    Sends messages to Teams channels

    .DESCRIPTION
    Sends curated messages to Teams channels

    .EXAMPLE
    HealthChecks_Teams.ps1

    .NOTES
    File Name: HealthChecks_Teams.ps1
    Author: <PERSON><PERSON>
    Version 1.0 - 12/06/2023
#>
#requires -Version 5
#requires -Modules PSPHPIPAM

Function checkIPAM {
    $phpSite = Test-Connection ipam.sanlam.co.za -quiet -Count 1
    if($phpSite){
        $apiURL = "https://ipam.sanlam.co.za/ipam/api"
        $apiAppID = "automon"
        $apiKey = "2MFYKYtHs3BxGenwqVDeIvbO98g1U8PH"
        New-PhpIpamSession -UseStaticAppKeyAuth -PhpIpamApiUrl $apiURL -AppID $apiAppID -AppKey $apiKey | Out-Null
        $ipamSubnet = Get-PhpIpamSubnet -CIDR *********/22
        if ($ipamSubnet) {
            $timeDiff =  New-TimeSpan -Start $ipamSubnet.lastScan -End $timeStamp
            if ($timeDiff.Days -gt 0 | Out-Null) {
                $phpSiteCheck = "The last scan date was $($timeDiff.Days) days, $($timeDiff.Hours) hours, $($timeDiff.Minutes) minutes ago - Failed"
            }
            else {
                $phpSiteCheck = "The last scan date was $($timeDiff.Days) days, $($timeDiff.Hours) hours, $($timeDiff.Minutes) minutes ago - Passed"
            }
        }
        else {
            $phpSiteCheck = "There was an issue getting the subnet detail - Failed"
        }
    }
    $phpSiteCheck
}