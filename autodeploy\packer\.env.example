# Packer Docker Environment Configuration
# Copy this file to .env and customize for your environment

# =============================================================================
# AWS Configuration
# =============================================================================
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_DEFAULT_REGION=us-east-1
AWS_PROFILE=default
# AWS_SESSION_TOKEN=your_session_token_here  # For temporary credentials

# =============================================================================
# VMware vSphere Configuration
# =============================================================================
VCENTER_SERVER=your-vcenter.domain.com
VCENTER_USERNAME=<EMAIL>
VCENTER_PASSWORD=your-password-here
VCENTER_DATACENTER=Your-Datacenter
VCENTER_CLUSTER=Your-Cluster
VCENTER_DATASTORE=Your-Datastore
VCENTER_NETWORK=Your-Network-PortGroup

# =============================================================================
# Azure Configuration (Optional)
# =============================================================================
# AZURE_CLIENT_ID=your-client-id
# AZURE_CLIENT_SECRET=your-client-secret
# AZURE_TENANT_ID=your-tenant-id
# AZURE_SUBSCRIPTION_ID=your-subscription-id

# =============================================================================
# Build Configuration
# =============================================================================
BUILD_PLATFORM=both                    # Options: vmware, aws, both
BUILD_VERSION=both                     # Options: 2019, 2022, both
BUILD_DEBUG=false                      # Enable debug logging
BUILD_PARALLEL=false                   # Run builds in parallel

# =============================================================================
# Packer Configuration
# =============================================================================
PACKER_LOG=0                          # Set to 1 for debug logging
PACKER_VERSION=1.10.0                 # Packer version to use
TERRAFORM_VERSION=1.6.6               # Terraform version to use

# =============================================================================
# Container Configuration
# =============================================================================
MEMORY_LIMIT=4G                       # Container memory limit
CPU_LIMIT=2.0                         # Container CPU limit
MEMORY_RESERVATION=2G                 # Container memory reservation
CPU_RESERVATION=1.0                   # Container CPU reservation
NETWORK_MODE=host                     # Network mode (host, bridge)

# =============================================================================
# Path Configuration
# =============================================================================
ISO_PATH=./ISO                        # Path to ISO files
CACHE_PATH=./packer_cache             # Path for Packer cache
LOGS_PATH=./logs                      # Path for build logs

# =============================================================================
# System Configuration
# =============================================================================
TZ=America/New_York                   # Timezone
DEBIAN_FRONTEND=noninteractive        # Avoid interactive prompts

# =============================================================================
# Development Configuration
# =============================================================================
# Uncomment for development mode
# PACKER_LOG=1
# BUILD_DEBUG=true
# MEMORY_LIMIT=8G
# CPU_LIMIT=4.0
