FROM node:18-alpine

# Install dependencies for image processing and archive extraction
RUN apk add --no-cache \
    unrar \
    unzip \
    python3 \
    build-base \
    vips-dev \
    poppler-utils

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy app source
COPY . .

# Create upload directories
RUN mkdir -p uploads/temp uploads/manga

# Expose port
EXPOSE 8080

# Start app
CMD ["npm", "start"]