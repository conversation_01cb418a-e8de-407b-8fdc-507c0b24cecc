FROM ubuntu:24.04

# Build arguments for non-sensitive data.
ARG POWERSHELL_VERSION="7.5.2"
ARG TERRAFORM_VERSION="1.13.0"
ARG PACKER_VERSION="1.14.1"
ARG TF_VAR_infrastructure_spec_file="infrastructure/vm-spec_test.json"
ARG TF_VAR_guest_id="windows9Server64Guest"
ARG TF_VAR_vsphere_server="srv009972.mud.internal.co.za"
# TODO: Replace with a valid IPAM testing URL to test.
ARG TF_VAR_ipam_api_endpoint="https://ipam.example.com/api"
# TODO: Replace with a valid domain controller URL to test.
ARG TF_VAR_ad_domain_controller="ad.example.com"
# Temporary for testing and debugging. (Otherwise everything breaks, especially Terraform.)
# FIXME: Turn this off once we can confirm it's no longer required for testing.
ARG TF_VAR_allow_unverified_ssl="true"

WORKDIR /
# Copy .env file to /tmp/<environment>.env
COPY .env /tmp/dev.env
COPY infrastructure/ ./infrastructure/
COPY output/ ./output/
COPY *.tf /
COPY security/zscaler_root.cer /usr/local/share/ca-certificates/zscaler_root.crt
COPY security/AWS_CLI_gpg_key.pub /security/AWS_CLI_gpg_key.pub
# Set all environment variables.
ENV POWERSHELL_VERSION=${POWERSHELL_VERSION} \
    TERRAFORM_VERSION=${TERRAFORM_VERSION} \
    PACKER_VERSION=${PACKER_VERSION} \
    TF_VAR_infrastructure_spec_file=${TF_VAR_infrastructure_spec_file} \
    TF_VAR_guest_id=${TF_VAR_guest_id} \
    TF_VAR_vsphere_server=${TF_VAR_vsphere_server} \
    TF_VAR_allow_unverified_ssl=${TF_VAR_allow_unverified_ssl} \
    TF_VAR_ipam_api_endpoint=${TF_VAR_ipam_api_endpoint} \
    TF_VAR_ad_domain_controller=${TF_VAR_ad_domain_controller}
# Update and install required packages, install public Zscaler certificate and add to cert store.
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    apt-transport-https \
    ca-certificates \
    wget \
    curl \
    jq \
    unzip \
    git \
    python3 \
    python3-pip \
    openssh-client \
    gnupg \
    lsb-release \
    && update-ca-certificates \
    && ls -la /usr/local/share/ca-certificates/ \
    && ls -la /etc/ssl/certs/ | grep -i zscaler \
    && rm -rf /var/lib/apt/lists/*
# Install PowerShell and verify SHA256 checksum file is valid before installation.
RUN mkdir -p /tmp/pwsh && cd /tmp/pwsh && \
    wget -q "https://github.com/PowerShell/PowerShell/releases/download/v${POWERSHELL_VERSION}/powershell_${POWERSHELL_VERSION}-1.deb_amd64.deb" && \
    wget -q "https://github.com/PowerShell/PowerShell/releases/download/v${POWERSHELL_VERSION}/hashes.sha256" && \
    # Clean the hash file from BOM and remove null bytes.
    tr -d '\000' < hashes.sha256 | sed 's/\xEF\xBB\xBF//g' > hashes.sha256.clean && \
    grep "powershell_${POWERSHELL_VERSION}-1.deb_amd64.deb" hashes.sha256.clean | tee powershell.sha256 && \
    sha256sum -c powershell.sha256 && \
    apt-get update && \
    apt-get install -y ./powershell_${POWERSHELL_VERSION}-1.deb_amd64.deb && \
    pwsh --version && \
    cd / && \
    rm -rf /tmp/pwsh
# Install Terraform binary and verify SHA256 checksum file is valid before installation.
RUN set -x \
    && wget -q "https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_SHA256SUMS" \
    && wget -q "https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip" \
    && grep "terraform_${TERRAFORM_VERSION}_linux_amd64.zip" terraform_${TERRAFORM_VERSION}_SHA256SUMS > terraform_SHA256SUM \
    && sha256sum --check terraform_SHA256SUM \
    && unzip -o terraform_${TERRAFORM_VERSION}_linux_amd64.zip \
    && mv terraform /usr/local/bin/ \
    && chmod +x /usr/local/bin/terraform \
    && rm terraform_${TERRAFORM_VERSION}_linux_amd64.zip terraform_${TERRAFORM_VERSION}_SHA256SUMS terraform_SHA256SUM \
    \
    && wget -q "https://releases.hashicorp.com/packer/${PACKER_VERSION}/packer_${PACKER_VERSION}_SHA256SUMS" \
    && wget -q "https://releases.hashicorp.com/packer/${PACKER_VERSION}/packer_${PACKER_VERSION}_linux_amd64.zip" \
    && grep "packer_${PACKER_VERSION}_linux_amd64.zip" packer_${PACKER_VERSION}_SHA256SUMS > packer_SHA256SUM \
    && sha256sum --check packer_SHA256SUM \
    && unzip -o packer_${PACKER_VERSION}_linux_amd64.zip \
    && mv packer /usr/local/bin/ \
    && chmod +x /usr/local/bin/packer \
    && rm packer_${PACKER_VERSION}_linux_amd64.zip packer_${PACKER_VERSION}_SHA256SUMS packer_SHA256SUM
    # Install AWS CLI v2
RUN gpg --import security/AWS_CLI_gpg_key.pub \
    && wget -q "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" \
    && wget -q "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip.sig" \
    && gpg --verify awscli-exe-linux-x86_64.zip.sig awscli-exe-linux-x86_64.zip \
    && unzip awscli-exe-linux-x86_64.zip \
    && ./aws/install \
    && rm -rf aws awscli-exe-linux-x86_64.zip
    # Install Azure CLI (for potential Azure builds)
RUN curl -sL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor | tee /etc/apt/trusted.gpg.d/microsoft.gpg > /dev/null \
    && echo "deb [arch=amd64] https://packages.microsoft.com/repos/azure-cli/ $(lsb_release -cs) main" | tee /etc/apt/sources.list.d/azure-cli.list \
    && apt-get update \
    && apt-get install azure-cli
    # Bake providers and infrastructure JSON specifications into the final image layers.
RUN terraform init \
    && terraform fmt -check -recursive ./infrastructure/ \
    && terraform validate ./infrastructure/
# Verify all tool versions and confirm successful installations.
RUN terraform --version \
    && packer --version \
    && pwsh --version \
    && aws --version \
    && az version
