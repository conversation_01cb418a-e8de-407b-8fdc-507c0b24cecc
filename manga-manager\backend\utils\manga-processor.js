const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const childProcess = require('child_process');
const sharp = require('sharp');
const AdmZip = require('adm-zip');
const { v4: uuidv4 } = require('uuid');

const exec = promisify(childProcess.exec);
const mkdir = promisify(fs.mkdir);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const copyFile = promisify(fs.copyFile);

// Extract archive (CBZ, CBR, ZIP, RAR)
async function extractArchive(filePath, outputPath, fileExt) {
  try {
    await mkdir(outputPath, { recursive: true });
    
    if (['.cbz', '.zip'].includes(fileExt)) {
      const zip = new AdmZip(filePath);
      zip.extractAllTo(outputPath, true);
      return outputPath;
    } else if (['.cbr', '.rar'].includes(fileExt)) {
      await exec(`unrar x "${filePath}" "${outputPath}"`);
      return outputPath;
    } else if (fileExt === '.pdf') {
      const imagesDir = path.join(outputPath, 'images');
      await mkdir(imagesDir, { recursive: true });
      await exec(`pdftoppm -jpeg -r 300 "${filePath}" "${path.join(imagesDir, 'page')}"`);
      return outputPath;
    } else {
      throw new Error('Unsupported file format');
    }
  } catch (error) {
    console.error('Error extracting archive:', error);
    throw error;
  }
}

// Process images from extracted archive
async function processImages(extractedPath, mangaId) {
  try {
    const mangaDir = path.join(__dirname, `../../uploads/manga/${mangaId}`);
    const thumbsDir = path.join(mangaDir, 'thumbnails');
    
    await mkdir(thumbsDir, { recursive: true });
    
    // Get all files recursively
    const files = await getAllFiles(extractedPath);
    
    // Filter image files
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);
    });
    
    // Sort files naturally (chapter1, chapter2, etc.)
    imageFiles.sort((a, b) => {
      return naturalSort(a, b);
    });
    
    // Detect chapters
    const chapters = detectChapters(imageFiles);
    
    // Process each chapter
    const processedChapters = [];
    let coverImage = null;
    
    for (let i = 0; i < chapters.length; i++) {
      const chapterFiles = chapters[i].files;
      const chapterNumber = chapters[i].number;
      const chapterTitle = chapters[i].title;
      
      const chapterDir = path.join(mangaDir, `chapter_${chapterNumber}`);
      await mkdir(chapterDir, { recursive: true });
      
      const processedPages = [];
      
      for (let j = 0; j < chapterFiles.length; j++) {
        const file = chapterFiles[j];
        const pageNumber = j + 1;
        
        // Generate unique filename
        const uniqueId = uuidv4();
        const ext = path.extname(file);
        const newFilename = `page_${pageNumber}_${uniqueId}${ext}`;
        const newPath = path.join(chapterDir, newFilename);
        
        // Copy file
        await copyFile(file, newPath);
        
        // Generate thumbnail
        const thumbFilename = `thumb_${pageNumber}_${uniqueId}${ext}`;
        const thumbPath = path.join(thumbsDir, thumbFilename);
        
        await sharp(file)
          .resize(300, null, { fit: 'inside' })
          .toFile(thumbPath);
        
        // Get image metadata
        const metadata = await sharp(file).metadata();
        
        processedPages.push({
          path: newPath,
          thumbnail: thumbPath,
          width: metadata.width,
          height: metadata.height,
          size: metadata.size,
          type: metadata.format
        });
        
        // Use first page of first chapter as cover if not set
        if (!coverImage && i === 0 && j === 0) {
          const coverFilename = `cover_${uniqueId}${ext}`;
          const coverPath = path.join(mangaDir, coverFilename);
          
          await sharp(file)
            .resize(600, null, { fit: 'inside' })
            .toFile(coverPath);
          
          coverImage = coverPath;
        }
      }
      
      processedChapters.push({
        number: chapterNumber,
        title: chapterTitle,
        pages: processedPages
      });
    }
    
    return {
      coverImage,
      chapters: processedChapters
    };
  } catch (error) {
    console.error('Error processing images:', error);
    throw error;
  }
}

// Get all files recursively
async function getAllFiles(dir) {
  const files = [];
  
  async function traverse(currentDir) {
    const entries = await readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        await traverse(fullPath);
      } else {
        files.push(fullPath);
      }
    }
  }
  
  await traverse(dir);
  return files;
}

// Detect chapters based on directory structure or naming patterns
function detectChapters(files) {
  // Group files by directory
  const dirGroups = {};
  
  files.forEach(file => {
    const dir = path.dirname(file);
    if (!dirGroups[dir]) {
      dirGroups[dir] = [];
    }
    dirGroups[dir].push(file);
  });
  
  // Check if we have multiple directories that might be chapters
  const dirs = Object.keys(dirGroups);
  
  if (dirs.length > 1) {
    // Sort directories naturally
    dirs.sort(naturalSort);
    
    // Create chapters from directories
    return dirs.map((dir, index) => {
      const dirName = path.basename(dir);
      
      // Try to extract chapter number from directory name
      const chapterMatch = dirName.match(/chapter[_\s-]*(\d+(\.\d+)?)/i) || 
                          dirName.match(/ch[_\s-]*(\d+(\.\d+)?)/i) ||
                          dirName.match(/(\d+(\.\d+)?)/);
      
      const chapterNumber = chapterMatch ? parseFloat(chapterMatch[1]) : index + 1;
      
      return {
        number: chapterNumber,
        title: dirName,
        files: dirGroups[dir].sort(naturalSort)
      };
    });
  } else {
    // Single directory, try to detect chapters from filenames
    const allFiles = files.slice();
    
    // Try to find chapter patterns in filenames
    const chapterPatterns = [
      /chapter[_\s-]*(\d+(\.\d+)?)/i,
      /ch[_\s-]*(\d+(\.\d+)?)/i
    ];
    
    const chapters = [];
    let currentChapter = null;
    let currentChapterFiles = [];
    
    allFiles.forEach((file, index) => {
      const fileName = path.basename(file);
      let chapterNumber = null;
      
      // Try to extract chapter number from filename
      for (const pattern of chapterPatterns) {
        const match = fileName.match(pattern);
        if (match) {
          chapterNumber = parseFloat(match[1]);
          break;
        }
      }
      
      if (chapterNumber !== null && (currentChapter === null || chapterNumber !== currentChapter)) {
        // New chapter detected
        if (currentChapter !== null) {
          chapters.push({
            number: currentChapter,
            title: `Chapter ${currentChapter}`,
            files: currentChapterFiles
          });
        }
        
        currentChapter = chapterNumber;
        currentChapterFiles = [file];
      } else {
        currentChapterFiles.push(file);
      }
      
      // Last file
      if (index === allFiles.length - 1 && currentChapterFiles.length > 0) {
        chapters.push({
          number: currentChapter !== null ? currentChapter : 1,
          title: currentChapter !== null ? `Chapter ${currentChapter}` : 'Chapter 1',
          files: currentChapterFiles
        });
      }
    });
    
    // If no chapters detected, create a single chapter
    if (chapters.length === 0) {
      return [{
        number: 1,
        title: 'Chapter 1',
        files: allFiles
      }];
    }
    
    return chapters;
  }
}

// Natural sort function for filenames
function naturalSort(a, b) {
  const aParts = path.basename(a).split(/(\d+)/);
  const bParts = path.basename(b).split(/(\d+)/);
  
  for (let i = 0; i < Math.min(aParts.length, bParts.length); i++) {
    if (aParts[i] !== bParts[i]) {
      const aNum = parseInt(aParts[i]);
      const bNum = parseInt(bParts[i]);
      
      if (!isNaN(aNum) && !isNaN(bNum)) {
        return aNum - bNum;
      }
      
      return aParts[i].localeCompare(bParts[i]);
    }
  }
  
  return aParts.length - bParts.length;
}

// Extract metadata from filename or content
async function getMetadata(filename, extractedPath) {
  try {
    const metadata = {};
    
    // Extract year from filename
    const yearMatch = filename.match(/\((\d{4})\)/);
    if (yearMatch) {
      metadata.releaseYear = parseInt(yearMatch[1]);
    }
    
    // Extract status from filename
    if (filename.includes('[Completed]') || filename.includes('(Complete)')) {
      metadata.status = 'completed';
    } else if (filename.includes('[Ongoing]') || filename.includes('(Ongoing)')) {
      metadata.status = 'ongoing';
    }
    
    // Look for info.json or metadata.json
    const infoFiles = ['info.json', 'metadata.json', 'comic.json'];
    
    for (const infoFile of infoFiles) {
      const infoPath = path.join(extractedPath, infoFile);
      
      if (fs.existsSync(infoPath)) {
        try {
          const info = JSON.parse(fs.readFileSync(infoPath, 'utf8'));
          
          if (info.title) metadata.originalTitle = info.title;
          if (info.year) metadata.releaseYear = parseInt(info.year);
          if (info.status) metadata.status = info.status.toLowerCase();
          
          break;
        } catch (e) {
          console.error('Error parsing metadata file:', e);
        }
      }
    }
    
    return metadata;
  } catch (error) {
    console.error('Error extracting metadata:', error);
    return {};
  }
}

module.exports = {
  extractArchive,
  processImages,
  getMetadata
};