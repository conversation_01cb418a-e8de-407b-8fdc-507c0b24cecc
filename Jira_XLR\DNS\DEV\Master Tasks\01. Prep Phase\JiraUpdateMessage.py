if releaseVariables["record_type"] == "C-Record" and releaseVariables["category"] == "Update Record":
    updateMessageReceived = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'From: ${host_alias} - ${hostname}\n'
        'To: ${new_host_alias} - ${new_hostname}'
    )
    releaseVariables['action_proceed'] = True
elif releaseVariables["record_type"] == "C-Record" and releaseVariables["category"] == "Remove Record":
    updateMessageReceived = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'Record: ${host_alias} pointing to ${hostname}'
    )
    releaseVariables['action_proceed'] = True
elif releaseVariables["record_type"] == "C-Record" and releaseVariables["category"] == "Add Record":
    updateMessageReceived = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'Record: ${host_alias} pointing to ${hostname}'
    )
    releaseVariables['action_proceed'] = True
elif releaseVariables["record_type"] == "A-Record" and releaseVariables["category"] == "Update Record":
    updateMessageReceived = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'From: ${hostname} - ${ip_address}\n'
        'To: ${new_hostname} - ${new_ip_address}'
    )
    releaseVariables['action_proceed'] = True
elif releaseVariables["record_type"] == "A-Record" and releaseVariables["category"] == "Remove Record":
    updateMessageReceived = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'Record: ${hostname} with an IP of ${ip_address}'
    )
    releaseVariables['action_proceed'] = True
elif releaseVariables["record_type"] == "A-Record" and releaseVariables["category"] == "Add Record":
    updateMessageReceived = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'Record: ${hostname} with an IP of ${ip_address}'
    )
    releaseVariables['action_proceed'] = True
else:
    updateMessageReceived = 'This request has missing information! Unable to proceed.'
    releaseVariables["updateMessageReceived"] = updateMessageReceived
    releaseVariables['action_proceed'] = False

releaseVariables["updateMessageReceived"] = updateMessageReceived