:root {
  --primary-color: #4e73df;
  --secondary-color: #858796;
  --success-color: #1cc88a;
  --info-color: #36b9cc;
  --warning-color: #f6c23e;
  --danger-color: #e74a3b;
  --light-color: #f8f9fc;
  --dark-color: #5a5c69;
  --sidebar-width: 250px;
  --sidebar-collapsed-width: 80px;
  --transition-speed: 0.3s;
}

/* Light/Dark Mode Variables */
body {
  --bg-color: #f8f9fc;
  --text-color: #333;
  --card-bg: #fff;
  --border-color: #e3e6f0;
  --sidebar-bg: #4e73df;
  --sidebar-text: #fff;
  --navbar-bg: #fff;
  --navbar-border: #e3e6f0;
}

body.dark-mode {
  --bg-color: #1e1e2d;
  --text-color: #e0e0e0;
  --card-bg: #2c2c40;
  --border-color: #3f3f5a;
  --sidebar-bg: #1e1e2d;
  --sidebar-text: #e0e0e0;
  --navbar-bg: #2c2c40;
  --navbar-border: #3f3f5a;
}

/* General Styles */
body {
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  transition: background-color var(--transition-speed), color var(--transition-speed);
}

.wrapper {
  display: flex;
  width: 100%;
  align-items: stretch;
}

/* Sidebar Styles */
.sidebar {
  min-width: var(--sidebar-width);
  max-width: var(--sidebar-width);
  background: var(--sidebar-bg);
  color: var(--sidebar-text);
  transition: all var(--transition-speed);
  z-index: 999;
  height: 100vh;
  position: fixed;
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed {
  min-width: var(--sidebar-collapsed-width);
  max-width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  padding: 20px;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-user {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.sidebar .components {
  padding: 20px 0;
  flex-grow: 1;
  overflow-y: auto;
}

.sidebar ul li a {
  padding: 10px 20px;
  display: flex;
  align-items: center;
  color: var(--sidebar-text);
  text-decoration: none;
  transition: all 0.3s;
}

.sidebar ul li a:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar ul li.active > a {
  background: rgba(255, 255, 255, 0.2);
  border-left: 4px solid #fff;
}

.sidebar ul li a i {
  margin-right: 10px;
}

.sidebar-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar.collapsed .sidebar-header h3,
.sidebar.collapsed .sidebar-user .user-info,
.sidebar.collapsed ul li a span,
.sidebar.collapsed .sidebar-footer .theme-toggle label {
  display: none;
}

.sidebar.collapsed ul li a {
  justify-content: center;
  padding: 15px 0;
}

.sidebar.collapsed ul li a i {
  margin-right: 0;
  font-size: 1.2rem;
}

/* Content Styles */
#content {
  width: 100%;
  min-height: 100vh;
  transition: all var(--transition-speed);
  margin-left: var(--sidebar-width);
}

#content.expanded {
  margin-left: var(--sidebar-collapsed-width);
}

.navbar {
  background-color: var(--navbar-bg) !important;
  border-bottom: 1px solid var(--navbar-border);
  padding: 10px 20px;
}

.content-area {
  padding: 20px;
}

.content-page {
  display: none;
}

.content-page.active {
  display: block;
}

/* Card Styles */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.35rem;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  transition: all var(--transition-speed);
}

.card-header {
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid var(--border-color);
}

/* Manga Card Styles */
.manga-card {
  height: 100%;
  transition: transform 0.2s;
}

.manga-card:hover {
  transform: translateY(-5px);
}

.manga-card .card-img-top {
  height: 250px;
  object-fit: cover;
}

.manga-card.explicit .card-img-top {
  filter: blur(10px);
}

.manga-card.explicit:hover .card-img-top {
  filter: none;
}

/* Reader Styles */
.reader-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 130px);
  background-color: #000;
}

.reader-image-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
}

#reader-image {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
}

.reader-controls {
  width: 80px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.3;
  transition: opacity 0.3s;
}

.reader-controls:hover {
  opacity: 1;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .sidebar {
    margin-left: calc(-1 * var(--sidebar-width));
  }
  
  .sidebar.active {
    margin-left: 0;
  }
  
  #content {
    margin-left: 0;
  }
  
  #content.active {
    margin-left: var(--sidebar-width);
  }
  
  .sidebar-close {
    display: block;
    cursor: pointer;
  }
}

/* Form Controls */
.form-control, .form-select {
  background-color: var(--bg-color);
  color: var(--text-color);
  border-color: var(--border-color);
}

.form-control:focus, .form-select:focus {
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* Dropdown Styles */
.dropdown-menu {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.dropdown-item {
  color: var(--text-color);
}

.dropdown-item:hover, .dropdown-item:focus {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Modal Styles */
.modal-content {
  background-color: var(--card-bg);
  color: var(--text-color);
}

.modal-header, .modal-footer {
  border-color: var(--border-color);
}

/* Utilities */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-light { color: var(--light-color) !important; }
.text-dark { color: var(--dark-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-light { background-color: var(--light-color) !important; }
.bg-dark { background-color: var(--dark-color) !important; }