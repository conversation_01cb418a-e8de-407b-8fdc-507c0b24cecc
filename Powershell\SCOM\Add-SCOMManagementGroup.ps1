<#
.SYNOPSIS
    Adds a management group to a SCOM agent.

.DESCRIPTION
    Adds a first or additional management group to a SCOM agent. Use this to multihome an agent
    when migrating to a new management group or to configure a new agent.

.PARAMETER ManagementServer
    The FQDN of the primary management server to connect to.

.PARAMETER MGMTGroupName
    The name of the management group to add.

.PARAMETER Port
    The port to use for the management server connection. Default is 5723.

.PARAMETER Force
    Suppresses confirmation prompts.

.EXAMPLE
    Add-SCOMManagementGroup.ps1 -ManagementServer SCOM01.contoso.com -MGMTGroupName SCOMMG

    Adds SCOMMG to the agent and sets SCOM01 as primary management server using default port 5723.

.EXAMPLE
    Add-SCOMManagementGroup.ps1 -ManagementServer SCOM01.contoso.com -MGMTGroupName SCOMMG -Port 5724

    Adds SCOMMG to the agent with a custom port.

.EXAMPLE
    Add-SCOMManagementGroup.ps1 -ManagementServer SCOM01.contoso.com -MGMTGroupName SCOMMG -Force

    Adds the management group without confirmation prompt.

.NOTES
    Author: <PERSON> /<PERSON> AS
    Updated: April 16, 2025
#>

[CmdletBinding(SupportsShouldProcess=$true, ConfirmImpact='High')]
Param(
    [Parameter(Mandatory=$true, Position=0)]
    [ValidateNotNullOrEmpty()]
    [string]$ManagementServer,
    
    [Parameter(Mandatory=$true, Position=1)]
    [ValidateNotNullOrEmpty()]
    [string]$MGMTGroupName,

    [Parameter(Mandatory=$false)]
    [ValidateRange(1, 65535)]
    [int]$Port = 5723,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

Begin {
    $ErrorActionPreference = 'Stop'
    
    # Check if running as administrator
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    if (-not $isAdmin) {
        throw "This script requires administrator privileges. Please run PowerShell as administrator."
    }
    
    # Check if health service is running
    $healthService = Get-Service -Name HealthService -ErrorAction SilentlyContinue
    if (-not $healthService) {
        throw "SCOM Health Service not found on this machine. Please verify the SCOM agent is installed."
    }
}

Process {
    if ($Force -or $PSCmdlet.ShouldProcess($env:COMPUTERNAME, "Add management group '$MGMTGroupName' and restart Health Service")) {
        try {
            Write-Verbose "Creating COM object for agent configuration"
            $Agent = New-Object -ComObject AgentConfigManager.MgmtSvcCfg
            
            # Check if management group already exists
            $existingGroups = $Agent.GetManagementGroups() | Where-Object { $_ -eq $MGMTGroupName }
            if ($existingGroups) {
                Write-Warning "Management group '$MGMTGroupName' is already configured on this agent."
            }
            else {
                Write-Verbose "Adding management group '$MGMTGroupName' with server '$ManagementServer' on port $Port"
                $Agent.AddManagementGroup($MGMTGroupName, $ManagementServer, $Port)
                Write-Host "Successfully added management group '$MGMTGroupName'" -ForegroundColor Green
            }
            
            Write-Verbose "Restarting Health Service"
            Restart-Service -Name HealthService -Force
            
            # Wait for service to fully restart
            $waitTime = 0
            $maxWait = 30
            Write-Host "Waiting for Health Service to restart..." -NoNewline
            while (($healthService.Status -ne 'Running') -and ($waitTime -lt $maxWait)) {
                Start-Sleep -Seconds 1
                $waitTime++
                Write-Host "." -NoNewline
                $healthService.Refresh()
            }
            Write-Host ""
            
            if ($healthService.Status -ne 'Running') {
                Write-Warning "Health Service did not start within $maxWait seconds. Please check service status manually."
            }
            else {
                Write-Host "Health Service successfully restarted" -ForegroundColor Green
            }
            
            # Display current management groups
            Write-Host "`nCurrent management groups:" -ForegroundColor Cyan
            $Agent.GetManagementGroups() | ForEach-Object {
                Write-Host " - $_"
            }
        }
        catch {
            Write-Host "An error occurred while configuring the SCOM agent:" -ForegroundColor Red
            Write-Error $_.Exception.Message
            return
        }
    }
}