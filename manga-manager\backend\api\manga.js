const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const { Manga, Chapter, Page, Category, Bookmark, ReadingHistory, User, sequelize } = require('../models');
const { Op } = require('sequelize');
const { isAuthenticated } = require('../../middleware/auth/auth-middleware');
const { extractArchive, processImages, getMetadata } = require('../utils/manga-processor');

const router = express.Router();
const mkdir = promisify(fs.mkdir);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/temp');
    try {
      await mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 500 * 1024 * 1024 }, // 500MB limit
  fileFilter: (req, file, cb) => {
    const allowedExtensions = ['.cbz', '.cbr', '.zip', '.rar', '.pdf'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedExtensions.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only CBZ, CBR, ZIP, RAR, and PDF files are allowed.'));
    }
  }
});

// Get all manga with pagination and filtering
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      category,
      sort = 'createdAt',
      order = 'DESC',
      isExplicit
    } = req.query;
    
    const offset = (page - 1) * limit;
    
    // Build where clause
    const where = {};
    
    if (search) {
      where[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { originalTitle: { [Op.iLike]: `%${search}%` } },
        { author: { [Op.iLike]: `%${search}%` } },
        { artist: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (isExplicit !== undefined) {
      where.isExplicit = isExplicit === 'true';
    }
    
    // Build include for categories
    const include = [
      {
        model: Category,
        as: 'categories',
        through: { attributes: [] }
      }
    ];
    
    if (category) {
      include[0].where = { id: category };
    }
    
    // Get total count
    const count = await Manga.count({
      where,
      include: category ? include : undefined
    });
    
    // Get manga
    const mangas = await Manga.findAll({
      where,
      include,
      order: [[sort, order]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.status(200).json({
      mangas,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching manga:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get manga by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const manga = await Manga.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'categories',
          through: { attributes: [] }
        },
        {
          model: Chapter,
          as: 'chapters',
          order: [['number', 'ASC']]
        },
        {
          model: User,
          as: 'uploader',
          attributes: ['id', 'username', 'profilePicture']
        }
      ]
    });
    
    if (!manga) {
      return res.status(404).json({ message: 'Manga not found' });
    }
    
    res.status(200).json({ manga });
  } catch (error) {
    console.error('Error fetching manga:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Upload new manga
router.post('/', isAuthenticated, upload.single('file'), async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { title, author, artist, description, categories, isExplicit } = req.body;
    const file = req.file;
    
    if (!file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }
    
    // Create manga record
    const manga = await Manga.create({
      title,
      author,
      artist,
      description,
      isExplicit: isExplicit === 'true',
      uploadedBy: req.user.id,
      fileFormat: path.extname(file.originalname).substring(1),
      filePath: file.path
    }, { transaction });
    
    // Process the uploaded file
    const { coverImage, chapters, metadata } = await processUploadedManga(file, manga.id);
    
    // Update manga with metadata
    manga.coverImage = coverImage;
    manga.totalPages = chapters.reduce((total, chapter) => total + chapter.pages.length, 0);
    
    if (metadata) {
      manga.originalTitle = metadata.originalTitle || manga.originalTitle;
      manga.releaseYear = metadata.releaseYear || manga.releaseYear;
      manga.status = metadata.status || manga.status;
    }
    
    await manga.save({ transaction });
    
    // Add categories
    if (categories) {
      const categoryIds = categories.split(',');
      await manga.setCategories(categoryIds, { transaction });
    }
    
    // Create chapters and pages
    for (const chapter of chapters) {
      const newChapter = await Chapter.create({
        mangaId: manga.id,
        number: chapter.number,
        title: chapter.title,
        totalPages: chapter.pages.length
      }, { transaction });
      
      // Create pages
      for (let i = 0; i < chapter.pages.length; i++) {
        await Page.create({
          chapterId: newChapter.id,
          pageNumber: i + 1,
          filePath: chapter.pages[i].path,
          width: chapter.pages[i].width,
          height: chapter.pages[i].height,
          fileSize: chapter.pages[i].size,
          fileType: chapter.pages[i].type
        }, { transaction });
      }
    }
    
    await transaction.commit();
    
    res.status(201).json({
      message: 'Manga uploaded successfully',
      manga
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error uploading manga:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update manga
router.put('/:id', isAuthenticated, async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const { title, author, artist, description, categories, isExplicit, status } = req.body;
    
    const manga = await Manga.findByPk(id);
    
    if (!manga) {
      return res.status(404).json({ message: 'Manga not found' });
    }
    
    // Update manga
    manga.title = title || manga.title;
    manga.author = author || manga.author;
    manga.artist = artist || manga.artist;
    manga.description = description || manga.description;
    manga.isExplicit = isExplicit !== undefined ? isExplicit === 'true' : manga.isExplicit;
    manga.status = status || manga.status;
    
    await manga.save({ transaction });
    
    // Update categories
    if (categories) {
      const categoryIds = categories.split(',');
      await manga.setCategories(categoryIds, { transaction });
    }
    
    await transaction.commit();
    
    res.status(200).json({
      message: 'Manga updated successfully',
      manga
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating manga:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete manga
router.delete('/:id', isAuthenticated, async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    
    const manga = await Manga.findByPk(id);
    
    if (!manga) {
      return res.status(404).json({ message: 'Manga not found' });
    }
    
    // Delete manga and all related data (cascade)
    await manga.destroy({ transaction });
    
    await transaction.commit();
    
    res.status(200).json({
      message: 'Manga deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting manga:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Search manga with accuracy rating
router.get('/search/:query', async (req, res) => {
  try {
    const { query } = req.params;
    const { limit = 20 } = req.query;
    
    // Search in title, original title, author, and artist
    const results = await Manga.findAll({
      where: {
        [Op.or]: [
          { title: { [Op.iLike]: `%${query}%` } },
          { originalTitle: { [Op.iLike]: `%${query}%` } },
          { author: { [Op.iLike]: `%${query}%` } },
          { artist: { [Op.iLike]: `%${query}%` } }
        ]
      },
      include: [
        {
          model: Category,
          as: 'categories',
          through: { attributes: [] }
        }
      ],
      limit: parseInt(limit)
    });
    
    // Calculate accuracy rating
    const searchResults = results.map(manga => {
      const mangaData = manga.toJSON();
      
      // Calculate accuracy based on match position and field
      let accuracy = 0;
      const lowerQuery = query.toLowerCase();
      
      // Title match (highest weight)
      if (manga.title) {
        const titleLower = manga.title.toLowerCase();
        if (titleLower === lowerQuery) {
          accuracy += 100;
        } else if (titleLower.startsWith(lowerQuery)) {
          accuracy += 90;
        } else if (titleLower.includes(lowerQuery)) {
          accuracy += 80;
        }
      }
      
      // Original title match
      if (manga.originalTitle) {
        const originalTitleLower = manga.originalTitle.toLowerCase();
        if (originalTitleLower === lowerQuery) {
          accuracy += 80;
        } else if (originalTitleLower.startsWith(lowerQuery)) {
          accuracy += 70;
        } else if (originalTitleLower.includes(lowerQuery)) {
          accuracy += 60;
        }
      }
      
      // Author match
      if (manga.author) {
        const authorLower = manga.author.toLowerCase();
        if (authorLower === lowerQuery) {
          accuracy += 70;
        } else if (authorLower.startsWith(lowerQuery)) {
          accuracy += 60;
        } else if (authorLower.includes(lowerQuery)) {
          accuracy += 50;
        }
      }
      
      // Artist match
      if (manga.artist) {
        const artistLower = manga.artist.toLowerCase();
        if (artistLower === lowerQuery) {
          accuracy += 70;
        } else if (artistLower.startsWith(lowerQuery)) {
          accuracy += 60;
        } else if (artistLower.includes(lowerQuery)) {
          accuracy += 50;
        }
      }
      
      // Normalize accuracy to 0-100 scale
      accuracy = Math.min(100, accuracy);
      
      return {
        ...mangaData,
        accuracy
      };
    });
    
    // Sort by accuracy
    searchResults.sort((a, b) => b.accuracy - a.accuracy);
    
    res.status(200).json({
      results: searchResults,
      query
    });
  } catch (error) {
    console.error('Error searching manga:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Helper function to process uploaded manga
async function processUploadedManga(file, mangaId) {
  const uploadDir = path.join(__dirname, `../../uploads/manga/${mangaId}`);
  await mkdir(uploadDir, { recursive: true });
  
  const fileExt = path.extname(file.originalname).toLowerCase();
  
  // Extract archive
  const extractedPath = await extractArchive(file.path, uploadDir, fileExt);
  
  // Process images
  const { coverImage, chapters } = await processImages(extractedPath, mangaId);
  
  // Get metadata
  const metadata = await getMetadata(file.originalname, extractedPath);
  
  return { coverImage, chapters, metadata };
}

module.exports = router;