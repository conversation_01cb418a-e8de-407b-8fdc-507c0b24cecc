releaseVariables['action_proceed'] = False
releaseVariables['action_cancelled'] = False
releaseVariables['dnsRemove_proceed'] = False

rawdata = releaseVariables['jira_response']
print rawdata["fields.status.name"]
var_progress = rawdata["fields.status.name"]

if var_progress == 'In Progress':
  releaseVariables['action_proceed'] = True
elif rawdata["fields.status.name"] == 'Administrator Assistance':
  releaseVariables['dnsRemove_proceed'] = False
elif rawdata["fields.status.name"] == 'Review':
  releaseVariables['dnsRemove_proceed'] = False
elif rawdata["fields.status.name"] == 'Closed':
  releaseVariables['dnsRemove_proceed'] = False
elif rawdata["fields.status.name"] == 'Done':
  releaseVariables['dnsRemove_proceed'] = False
elif rawdata["fields.status.name"] == 'Cancelled':
  releaseVariables['dnsRemove_proceed'] = False
  releaseVariables['dnsRemove_cancelled'] = True

if releaseVariables["updateRecord"] or releaseVariables["deleteRecord"]:
  releaseVariables['dnsRemove_proceed'] = True
else:
  releaseVariables['dnsRemove_proceed'] = False