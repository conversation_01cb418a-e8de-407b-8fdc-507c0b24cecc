print "${auth_release_id}"
import json

auth_release_vars = releaseApi.getVariables(releaseVariables["auth_release_id"])
#print auth_release_vars

for r in auth_release_vars:
    if r._delegate.key == "ebx_auth_success":
        print r._delegate.value #  r._delegate.key +"="+
        releaseVariables['ebx_auth_ok'] = r._delegate.value
    if r._delegate.key == "ebx_auth_response":
        print r._delegate.value #  r._delegate.key +"="+
        releaseVariables['ebx_auth_headers'] = r._delegate.value
        releaseVariables["Authorization"] =  releaseVariables['ebx_auth_headers']['Authorization']
        releaseVariables["ebx_access_token"] =  releaseVariables['ebx_auth_headers']['ebx_access_token']
        releaseVariables["tokenType"] =  releaseVariables['ebx_auth_headers']['tokenType']
    else:
        pass


dnsAddAPIResponse = {
    "success":  "false",
    "status":  "CRITICAL",
    "message":  "A Record for server1.dev.mud.internal.co.za pointing to ******* already exists",
    "data":  {
                 "RecordName":  "server1",
                 "IPAddress":  "",
                 "HostName":  "",
                 "ZoneName":  "dev.mud.internal.co.za",
                 "RecordType":  "A",
                 "DNSServer":  "SRV010699.dev.mud.internal.co.za",
                 "Reference":  "EUP-85",
                 "Requester":  "svcDevSlmDcsRelease"
             },
    "date":  "2025-03-18 13:49:06"
}


import json
dnsAddAPIResponse_vars = releaseApi.getVariables(releaseVariables["dnsAddAPIResponse"])

#dnsAddAPIResponse_vars = releaseApi.getVariables(releaseVariables["dnsAddAPIResponse"])
# 
#dnsAddAPIResponse_vars = json.loads(dnsAddAPIResponse)  # Remove this line

for r in dnsAddAPIResponse_vars:
    releaseVariables['success'] = r._delegate.value
    releaseVariables['status'] = r._delegate.value
    releaseVariables['message'] = r._delegate.value
    releaseVariables['data'] = r._delegate.value
    releaseVariables['date'] = r._delegate.value
    print r._delegate.key +"="+ r._delegate.value

# Now store the dictionary in the releaseVariables
releaseVariables = {}
releaseVariables["dnsAddAPIResponse"] = dnsAddAPIResponse
releaseVariables["success"] = dnsAddAPIResponse['success']
releaseVariables["status"] = dnsAddAPIResponse['status']
releaseVariables["message"] = dnsAddAPIResponse['message']
releaseVariables["date"] = dnsAddAPIResponse['date']
releaseVariables["data"] = dnsAddAPIResponse['data']

# Store both raw and JSON string versions
releaseVariables['dnsAddRequestData'] = dnsAddAPIResponse
# Convert to string if needed for raw representation
releaseVariables['dnsAddRequestDataRaw'] = json.dumps(dnsAddAPIResponse)

print "Success:", releaseVariables["success"]
print "Status:", releaseVariables["status"]

releaseVariables['dnsRequestDataRaw'] = datamap

dataobj = json.loads(datamap)
releaseVariables['dnsRequestData'] = dataobj