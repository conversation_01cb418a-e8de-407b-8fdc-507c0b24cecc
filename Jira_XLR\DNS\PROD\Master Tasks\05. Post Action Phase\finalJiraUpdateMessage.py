if releaseVariables["record_type"] == "C-Record" and releaseVariables["category"] == "Update Record":
    finalUpdateMessage = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'From: ${host_alias} - ${hostname}\n'
        'To: ${new_host_alias} - ${new_hostname}\n'
        'Success: ${success}\n'
        'Request has been completed successfully'
    )
    releaseVariables['action_proceed'] = True
elif releaseVariables["record_type"] == "C-Record" and releaseVariables["category"] == "Remove Record":
    finalUpdateMessage = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'Record: ${host_alias} pointing to ${hostname}\n'
        'Success: ${success}\n'
        'Request has been completed successfully'
    )
    releaseVariables['action_proceed'] = True
elif releaseVariables["record_type"] == "C-Record" and releaseVariables["category"] == "Add Record":
    finalUpdateMessage = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'Record: ${host_alias} pointing to ${hostname}\n'
        'Success: ${success}\n'
        'Request has been completed successfully'
    )
    releaseVariables['action_proceed'] = True
elif releaseVariables["record_type"] == "A-Record" and releaseVariables["category"] == "Update Record":
    finalUpdateMessage = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'From: ${hostname} - ${ip_address}\n'
        'To: ${new_hostname} - ${new_ip_address}\n'
        'Success: ${success}\n'
        'Request has been completed successfully'
    )
    releaseVariables['action_proceed'] = True
elif releaseVariables["record_type"] == "A-Record" and releaseVariables["category"] == "Remove Record":
    finalUpdateMessage = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'Record: ${hostname} with an IP of ${ip_address}\n'
        'Success: ${success}\n'
        'Request has been completed successfully'
    )
    releaseVariables['action_proceed'] = True
elif releaseVariables["record_type"] == "A-Record" and releaseVariables["category"] == "Add Record":
    finalUpdateMessage = (
        'Action: ${category}\n'
        'Record Type: ${record_type}\n'
        'Record: ${hostname} with an IP of ${ip_address}\n'
        'Success: ${success}\n'
        'Request has been completed successfully'
    )
    releaseVariables['action_proceed'] = True
else:
    finalUpdateMessage = 'Something went wrong with this request; please log a BMC request with the DNS team.'
    releaseVariables["finalUpdateMessage"] = finalUpdateMessage
    releaseVariables['action_proceed'] = False

releaseVariables["finalUpdateMessage"] = finalUpdateMessage