module.exports = (sequelize, DataTypes) => {
  const Chapter = sequelize.define('Chapter', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    mangaId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Mangas',
        key: 'id'
      }
    },
    number: {
      type: DataTypes.FLOAT,
      allowNull: false
    },
    title: {
      type: DataTypes.STRING,
      allowNull: true
    },
    filePath: {
      type: DataTypes.STRING,
      allowNull: true
    },
    totalPages: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    releaseDate: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    timestamps: true
  });

  Chapter.associate = (models) => {
    Chapter.belongsTo(models.Manga, {
      foreignKey: 'mangaId',
      as: 'manga'
    });
    Chapter.hasMany(models.Page, {
      foreignKey: 'chapterId',
      as: 'pages'
    });
  };

  return Chapter;
};