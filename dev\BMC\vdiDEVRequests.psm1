$Global:baseURL = "https://sanlam-dev-restapi.onbmc.com"
$Global:loginURL = "$baseURL/api/jwt/login"
$Global:logoutURL = "$baseURL/api/jwt/logout"
$Global:apiURL = "$baseURL/api/arsys/v1"
$Global:helixFormName = "NIT:VDI:v_ src_srs_vdi_request"
$Global:costCenter = "B24051"
$Global:queryURL = "https://sanlam-dev-restapi.onbmc.com/api/arsys/v1/entry/NIT:VDI:v_ src_srs_vdi_request"

#$credPath = "\\srv009484\D$\AutoDeploy\RequiredFiles\Credentials"
#$creds = Import-Clixml "$credPath\prdDomainCreds.xml"
$loginpath = "/api/jwt/login"
$prefix = "AR-JWT"
$v1path = "/api/arsys/v1"
$logoutpath = "/api/jwt/logout"
$HelixFormName = "NIT:VDI:v_ src_srs_vdi_request"

$creds = Get-Credential
$password = $creds.GetNetworkCredential().Password
$user = $creds.UserName

function get-jwt {
    Param(
        [string]$URL,
        [string]$Username,
        [string]$Password
    )
    $headers = New-Object "System.Collections.Generic.Dictionary[[String],[String]]"
    $headers.Add("Content-Type", "application/x-www-form-urlencoded")
    $body = @{
        "username"   = $Username;
        "password"   = $Password
    }
    try {
        $Global:jwt = Invoke-RestMethod $URL -Method 'POST' -Headers $headers -Body $body -ErrorVariable rest_error
        #$Global:jwt = $response | ConvertTo-Json
    }
    catch {
        $err = $_
        try {
            $temp = $rest_error.Message | ConvertFrom-Json
            if ($temp.message) {
                $err = $temp.message
            }
        }
        catch {}
        Write-Error $err
        exit
    }
    return $Global:jwt
}

function get-vdiRequestsQuery {
    Param(
        [string]$token
    )
    $headers = New-Object "System.Collections.Generic.Dictionary[[String],[String]]"
    $headers.Add("Authorization", "AR-JWT $token")
    $headers.Add("Content-Type", "application/json")
    try {
        #$response = Invoke-RestMethod 'https://sanlam-dev-restapi.onbmc.com/api/arsys/v1/entry/NIT:VDI:v_ src_srs_vdi_request?q=''BuiltFlag'' = 0 AND ''Cost_Center'' != "B24051"' -Method 'GET' -Headers $headers -ErrorVariable rest_error
        $response = Invoke-RestMethod = '{0}/{1}?q=''BuiltFlag'' = 0 AND ''Cost_Center'' != "{2}"' -f $Global:apiURL,$HelixFormName,$costCenter
        $Global:vdiRequests = $response | ConvertTo-Json
    }
    catch {
        $err = $_
        try {
            $temp = $rest_error.Message | ConvertFrom-Json
            if ($temp.message) {
                $err = $temp.message
            }
        }
        catch {}
        Write-Error $err
        exit
    }
    return $Global:vdiRequests
}

function get-vdiRequest {
    param (
        [string]$token,
        [string]$entryID
    )
    $headers = New-Object "System.Collections.Generic.Dictionary[[String],[String]]"
    $headers.Add("Authorization", "AR-JWT $token")
    $headers.Add("Content-Type", "application/json")
    try {
        $response = Invoke-RestMethod "$apiURL/entry/$helixFormName/$entryID" -Method GET -Headers $headers -ErrorVariable rest_error
        $Global:request = $response | ConvertTo-Json
    }
    catch {
        $err = $_
        try {
            $temp = $rest_error.Message | ConvertFrom-Json
            if ($temp.message) {
                $err = $temp.message
            }
        }
        catch {}
        Write-Error $err
        exit
    }
    return $Global:request
}

function update-vdiRequest {
    param (
        [string]$token,
        [string]$entryID
    )

    $headers = New-Object "System.Collections.Generic.Dictionary[[String],[String]]"
    $headers.Add("Authorization", "AR-JWT $token")
    $headers.Add("Content-Type", "application/json")
    $body = @"
{
    `"values`":{
        `"BuiltFlag`": 0,
        `"BuildCompletionDateTime`": `"2024-01-30T09:55:50`"
    }
}
"@
    try {
        $response = Invoke-RestMethod 'https://sanlam-dev-restapi.onbmc.com:443/api/arsys/v1/entry/NIT:VDI:v_ src_srs_vdi_request/000000000000806' -Method 'PUT' -Headers $headers -Body $body
        $response | ConvertTo-Json
    }
    catch {
        <#Do this if a terminating exception happens#>
    }
}

$vdiRequestsJson = $vdiRequests | ConvertFrom-Json

# Create empty hash table
$vdiRequestsHash = @{}

# Loop through each entry
foreach ($entry in $vdiRequestsJson.entries) {
    # Convert the values string to object (removing @ symbol and converting to proper object)
    $valuesObj = $entry.values -replace '^@{' -replace '}$' | ConvertFrom-StringData
    
    # Use ARS_Entry_ID as key and store full values object as value
    $vdiRequestsHash[$valuesObj.ARS_Entry_ID] = $valuesObj
}



Write-Host "Split complete. Files saved in $outputDir"

$vdi1 = $data.entries[0].values.Normalize()
$vdi1 = $data.entries[0].values.Normalize()

$vdiRequestsJson.entries[0].values.Normalize()

$vdiRequestsJson = Get-Content -path ".\BMC\Helix\test.json" | ConvertFrom-Json
$vdiRequestsJson

foreach ($entry in $vdiRequestsJson) {
    $valuesObj = $entry -replace '^@{' -replace '}$'
}

$vdiRequestsJsonEdited = $vdiRequestsJson.values -replace '^@{' -replace '}$'
$vdiRequestsJsonEdited | Out-File .\BMC\Helix\out.json