services:
  bookstack:
    image: linuxserver/bookstack:latest
    container_name: bookstack
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Africa/Johannesburg
      - APP_URL=http://srv041374.mud.internal.co.za
      - APP_KEY=tcuqUm5eJP+IkroLNOK0P2N376nCoJvY8Lk1XYe3zu4=
      - DB_HOST=srv009485.mud.internal.co.za
      - DB_PORT=3306
      - DB_USERNAME=bookstacksvc
      - DB_PASSWORD=HjYbTv*Jl3&bjpD1
      - DB_DATABASE=bookstackdb
      #- QUEUE_CONNECTION= #optional
    volumes:
      - /data/docker_configs/bookstack/config:/config
    ports:
      - 6875:6875
    restart: unless-stopped