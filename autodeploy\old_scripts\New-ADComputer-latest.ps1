<#
    .SYNOPSIS
    Creates an AD Computer Account from the AutoDeploy service.

    .DESC<PERSON><PERSON><PERSON><PERSON>
    Creates an AD Computer Account from the AutoDeploy service, for Windows, Linux and VDI virtual machines.

    .INPUTS
    $adObjectData = Get-Job -JobID "" -JobStatus "PENDING" -JobStage "AD_OBJECT" -HostingPlatform ""

    .EXAMPLE
    This script is dependent on the CredentialManager, ActiveDirectory and AutoDeploy modules.
    
    The stored credentials need to be assigned based on the domain that is parsed from the API call.
    New-StoredCredential -Target "example.com" -UserName "user" -Password "pass" -Comment "testing credentials" -Persist LocalMachine
    $adCreds = Get-StoredCredential -Target $domain

    The AutoDeploy module require the following parameters to function.
    Get-Job -JobID "" -JobStatus "PENDING" -JobStage "AD_OBJECT" -HostingPlatform ""
    Update-Job -JobID $JobID -JobStage $jobStage -JobStatus $jobStatus -LogType $logtype -LogMessage $updateComment -SuccessStatus $success -IssueRef $issueRef -IssueType $issueType


    .NOTES
    File Name: New-ADComputer.ps1
    Author: <PERSON><PERSON>
    Version: 1.1 - 09/06/2022

    .NOTES
    Version 1.1     - Re-wrote Using AutoDeploy module
    Version 1.0.2   - Added issue_ref and issue_type to the API update.
    Version 1.0.1   - Added skipping AD object creation if this is a linux server.
    Version 1.0     - Base Script
#>
#requires -Version 5
#requires -Modules CredentialManager, ActiveDirectory, Autodeploy

try {
    #Modules
    Import-Module -Name ActiveDirectory
    Import-Module -Name CredentialManager
    Import-Module -Name AutoDeploy

    #Get-RestData
    $adObjectData = Get-Job -JobID "" -JobStatus "PENDING" -JobStage "AD_OBJECT" -HostingPlatform ""
    if ($adObjectData.success) {
        $jobstatus = "IN_PROGRESS"
        $jobComment = "Job $jobID Received; creating AD object"
        $success = $true
    }
    elseif (-not $adObjectData.success){
        $jobstatus = "PENDING"
        $jobComment = "No data or job received!"
        Write-Host $jobComment -ForegroundColor Blue
        Exit
    }

    #Job Variables
    $vmName, $vmDescription, $domain, $ouPath, $adobject, $JobID, $vmOS, $adFqdn, $jobStage, $jobStatus, $jobComment, $appType, $logtype, $updateComment  = $null
    $jobID = $adObjectData.request_jobid
    $vmName = $adObjectData.vm_name
    $vmDescription = $adObjectData.vm_description
    $vmOS = $adObjectData.os_version
    $domain = $adObjectData.ad_suffix
    $ouPath = $adObjectData.ad_orgunit
    $adFqdn = $vmName + "." + $domain
    $jobStage = $adObjectData.job_stage
    $jobStatus = $adObjectData.job_status
    $jobComment = $adObjectData.job_comment
    $appType = $adObjectData.app_type
    $logtype = "INFO"
    $issueRef = " "
    $issueType = "INFO"
    #$adDescription = $vmDescription+" - "+$jobID

    #Skip AD object Creation for Linux servers.
    if ($vmOS -ne "Linux") {
        #Specify the AD account which will be used to create the object
        if ($appType -like "VDI") {
            $adCreds = Get-StoredCredential -Target "VDI"
        }
        else {
            $adCreds = Get-StoredCredential -Target $domain
        }

        #Validate whether computer account exists before continuing
        try {
            $adObjectInit = Get-ADComputer -Identity $vmName -Server $domain
        }
        catch [Microsoft.ActiveDirectory.Management.ADServerDownException] {
            $errorMessage = "Unable to reach a domain controller for $domain"
            $updateComment = $errorMessage
            $success = $false
            $logtype = "ISSUE"
            $issueRef = " "
            $issueType = "CRITICAL"
            Write-Host $errorMessage -ForegroundColor "Red"
            break
        }
        catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
            $accountExist = $false
            Write-Host "Computer name $vmName not found in the $domain domain; proceeding with object creation." -ForegroundColor "DarkGreen"
        }
        if ($adObjectInit) {
            $errorMessage = "$vmName already exists in the $domain domain; skipping the creation step."
            $updateComment = $errorMessage
            $success = $true
            $accountExist = $true
            $issueRef = " "
            $issueType = "WARNING"
            Write-Host $errorMessage -ForegroundColor "Blue"
        }
        #Create the new AD object
        if ($accountExist -ne $true) {
            Write-Host "Creating $vmName in $domain"
            try {
                New-ADComputer -Name $vmName -SAMAccountName $vmName -Path $ouPath -Description $vmDescription -Enabled $true -Credential $adCreds -Server $domain -DNSHostName $adFqdn -OtherAttributes @{'comment' = $jobID }
            }
            catch {
                $errorMessage = ($Error[0].exception).Message
                $updateComment = $errorMessage
                $success = $false
                $logtype = "ISSUE"
                $issueRef = " "
                $issueType = "CRITICAL"
                Write-Host $errorMessage -ForegroundColor "Red"
                throw $errorMessage
            }
        }
        else {
            $success = $true
        }
        
        Start-Sleep 10

        #Test if created AD account exists
        try {
            $adobject = Get-ADComputer -Identity $vmName -Server $domain
        }
        catch [Microsoft.ActiveDirectory.Management.ADServerDownException] {
            $errorMessage = ($Error[0].exception).Message
            $updateComment = $errorMessage
            $success = $false
            $logtype = "ISSUE"
            $issueRef = " "
            $issueType = "CRITICAL"
            Write-Host "Unable to reach a domain controller for $domain" -ForegroundColor "Red"
        }
        catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
            $errorMessage = ($Error[0].exception).Message
            $updateComment = $errorMessage
            $success = $false
            $logtype = "ISSUE"
            $issueRef = " "
            $issueType = "CRITICAL"
            Write-Host "Unable to find the computer account in the $domain domain" -ForegroundColor "Red"
        }
        catch {
            $errorMessage = ($Error[0].exception).Message
            $updateComment = $errorMessage
            $success = $false
            $logtype = "ISSUE"
            $issueRef = " "
            $issueType = "CRITICAL"
            Write-Host "AD oject not created: $errorMessage" -ForegroundColor "Red"
            throw $errorMessage
        }
        if ($adobject) {
            $updateComment = "AD Object Created in $domain"
            $success = $true
            $jobStatus = "COMPLETED"
            Write-Host "Computer account created successfully in the $domain domain" -ForegroundColor "DarkGreen"
        }
    }
    else {
        Write-Host "This is a Linux server...skipping account creation" -ForegroundColor "DarkGreen"
        $updateComment = "Linux based server, no AD Object created."
        $success = $true
    }
}
catch {
    $errorMessage = ($Error[0].exception).Message
    $updateComment = $errorMessage
    $success = $false
    $logtype = "ISSUE"
    $issueRef = " "
    $issueType = "CRITICAL"
    Write-Host $errorMessage -ForegroundColor "Red"
    Update-Job -JobID $JobID -JobStage $jobStage -JobStatus $jobStatus -LogType $logtype -LogMessage $updateComment -SuccessStatus $success -IssueRef $issueRef -IssueType $issueType
}
#Update the job
$jobResponse = Update-Job -JobID $JobID -JobStage $jobStage -JobStatus $jobStatus -LogType $logtype -LogMessage $updateComment -SuccessStatus $success -IssueRef $issueRef -IssueType $issueType
exit