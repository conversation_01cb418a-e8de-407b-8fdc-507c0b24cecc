# Manga Manager

A modern and responsive web application for managing and downloading manga.

## Features

### Frontend
- Modern design built with HTML5 and Bootstrap
- Light and dark mode support
- Google authentication
- Multi-factor authentication
- Search feature with accuracy rating
- Download functionality
- Responsive sidebar for easy navigation

### Middleware
- Category management
- Bookmark and page tracking system
- Customizable dashboard
- Support for various manga formats (.CBR, .CBZ, etc.)
- Blur feature for explicit content
- Upload functionality with automatic organization
- Standardized format for manga files

### Backend
- PostgreSQL database for storing all data
- Server-side processing for optimal performance
- RESTful API for mobile app integration

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Backend**: Node.js, Express.js
- **Database**: PostgreSQL
- **Authentication**: Passport.js, JWT, Google OAuth
- **Image Processing**: Sharp
- **Archive Handling**: AdmZip, unrar
- **Containerization**: Docker, Docker Compose

## Installation

### Prerequisites

- Docker and Docker Compose
- Node.js (for development)
- PostgreSQL (for development without Docker)

### Using Docker (Recommended)

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/manga-manager.git
   cd manga-manager
   ```

2. Configure environment variables:
   - Edit the `.env` file or use the environment variables in `docker-compose.yml`
   - Set up your Google OAuth credentials if you want to use Google authentication

3. Build and start the containers:
   ```bash
   docker-compose up -d
   ```

4. Access the application at http://localhost:8080

### Manual Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/manga-manager.git
   cd manga-manager
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure environment variables:
   - Create a `.env` file based on the `.env.example` template
   - Set up your database connection details
   - Configure Google OAuth credentials if needed

4. Start the application:
   ```bash
   npm start
   ```

5. Access the application at http://localhost:8080

## Usage

### User Registration and Login

1. Register for an account using email or Google authentication
2. Set up multi-factor authentication for enhanced security
3. Login with your credentials

### Managing Manga

1. **Search**: Use the search feature to find manga with accuracy ratings
2. **Download**: Download manga directly from the search results
3. **Upload**: Upload your own manga files in various formats
4. **Organize**: Categorize your manga collection
5. **Read**: Use the built-in reader to read your manga
6. **Bookmark**: Save your reading progress with bookmarks

### Customization

1. **Theme**: Switch between light and dark mode
2. **Dashboard**: Customize your dashboard to show preferred categories
3. **Settings**: Manage your account settings and preferences

## Mobile App

A separate mobile app is available that connects to this web application via API calls. The mobile app provides a native experience while leveraging the server-side processing of the main application.

## Development

### Project Structure

```
manga-manager/
├── backend/
│   ├── api/           # API routes
│   ├── controllers/   # Request handlers
│   ├── models/        # Database models
│   ├── services/      # Business logic
│   └── utils/         # Utility functions
├── frontend/
│   ├── css/           # Stylesheets
│   ├── js/            # JavaScript files
│   └── images/        # Static images
├── middleware/
│   ├── auth/          # Authentication middleware
│   ├── file-processing/ # File processing middleware
│   └── tracking/      # Reading tracking middleware
├── database/
│   ├── migrations/    # Database migrations
│   └── seeds/         # Seed data
├── docker/            # Docker configuration
├── uploads/           # Uploaded manga files
├── .env               # Environment variables
├── docker-compose.yml # Docker Compose configuration
├── Dockerfile         # Docker configuration
├── package.json       # Node.js dependencies
└── server.js          # Main application entry point
```

### API Documentation

The API documentation is available at `/api/docs` when running the application in development mode.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Bootstrap](https://getbootstrap.com/) - Frontend framework
- [Express.js](https://expressjs.com/) - Web framework
- [Sequelize](https://sequelize.org/) - ORM
- [Passport.js](http://www.passportjs.org/) - Authentication middleware
- [Sharp](https://sharp.pixelplumbing.com/) - Image processing