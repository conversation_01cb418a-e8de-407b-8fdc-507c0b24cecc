from datetime import datetime, date, time
from fastapi.encoders import jsonable_encoder
import json
from app import logging

from app.core.helpers import key_exists, dictval2list, dct2df, recast_json
#import MariaDB connection
from app.core.database import dbQuery, df2sql

# Convert Dict to Dataframe
""" def dct2df(dctIn):
    dct = {k:[v] for k,v in dctIn.items()}
    df = pd.DataFrame(dct)
    # df = df.append(dctIn, ignore_index=True)
    # print(df)
    return df """

def tagASO(x):
    if "SGT" in x.upper():
        return "SLM"
    elif "SANTAM" in x.upper():
        return "STM"
    elif "RETAIL" in x.upper():
        return "SKY"
    else:
        return "UNKNOWN"

def tagOS(ebx):
    if "poc" in ebx['server_name'].lower():
        return "POC"
    elif "windows" in ebx['operating_system_name'].lower():
        return "Windows"
    elif "red" in ebx['operating_system_name'].lower():
        return "Linux RHEL"
    elif "suse" in ebx['operating_system_name'].lower():
        return "Linux SuSe"
    elif "aix" in ebx['operating_system_name'].lower():
        return "AIX"
    else:
        return "UNKNOWN"
  

# Query DB for UI Options
def UiInit():
    try:
        # Query db for initial UI options
        qrytxt = f"select * from ui_init;"
        logging.info(f"QUERY: {qrytxt}")
        dfQry = dbQuery(qrytxt)
        dctRes = dfQry.to_dict(orient='records')
    except:
        # Return error
        message = {"messages" : "Issue with UI Init data in table", "data":""}
        logging.warning(message["messages"])
        return message
    return dictval2list(dctRes[0],",")

# Process Filters
def ProcessFilters(dct):
    logging.info(f"PARAMETERS => {dct}")
    if not bool(dct):
        logging.warning(f"PARAMETERS issue...")
        success = False
        data = ""
        message = f"Verify UI selections..."
        status = "WARN"
        logging.warning(message)
    else:
        respdata = UiOptions(dct)
        # dctClient = getClient(params)
        # dctEnviron = getEnvirons(params)
        # respdata = {**dctClient, **dctEnviron}
        # print(json.dumps(respdata, indent=2),"\n")   
        if respdata['data'] == "":
            success = False
            data = ""
            message = respdata["messages"] #f"Invalid selection, please retry."
            status = "FAILED"
            logging.error(message)
        else:
            success =True
            message = f"Result(s) returned."
            data = respdata['data']
            status = "NORMAL"
            logging.info(message)
    result = {
        'data' : data,
        'status': status, 
        'success' : success,
        "messages" : message
    }

    return result

# Query DB for UI Options

def getClient(dct):
    logging.info("Executing Clients query...")
    logging.warning(f"QUERY SPECS: {dct}")
    qrytxt = f"select * from clients where lower(client) = lower('{dct['client']}') ;"
    logging.info(f"QUERY: {qrytxt}")
    dfQry = dbQuery(qrytxt)
    if not dfQry.empty:
        print(f"Client Results:\n{dfQry}\n")
        dfOut = recast_json(dfQry)
        dctRes = dfOut.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes[0]}
    else:
        return {"success": False, "messages": "Invalid parameters", "data":{}}

def getDeployment(dct):
    logging.info("Executing Deployments query...")
    # print(dct)
    qrytxt = f"""select * from ui_deployments where lower(deployment) = lower('{dct['deployment']}')
        and upper(aso_tag) = upper('{dct['aso_tag']}')
        and lower(app_types) like lower('%{dct['app_type']}%');"""
    dfQry = dbQuery(qrytxt)
    if not dfQry.empty:
        print(f"Deployment Results:\n{dfQry}\n")
        dctRes = dfQry.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes[0]}
    else:
        return {"success": False, "messages": "Invalid parameters", "data":{}}

def getOStypes(dct):
    logging.info("Executing OS Types query...")
    qrytxt = \
        f"select * from ui_ostypes where os_type = '{dct['os_type']}' and app_type = '{dct['app_type']}' ;"
    dfQry = dbQuery(qrytxt)
    if not dfQry.empty:
        print(f"OS Types Results:\n{dfQry}\n")
        dctRes = dfQry.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes[0]}
    else:
        return {"success": False, "messages": "Invalid parameters", "data":{}}

# Query DB for UI Options
def getADConfigs(dct):
    logging.info("Executing AD Configs query...")
    print(dct)
    qrytxt = f"""select * from adconfigs
        where upper(client_tag) = upper('{dct['client_tag']}')
            and upper(app_type) like upper('%{dct['app_type']}%')
            and upper(os_version) = upper('{dct['os_version']}')
            and upper(env_type) = upper('{dct['env_type']}')
        ;"""
    dfQry = dbQuery(qrytxt)
    if not dfQry.empty:
        print(f"AD Config Results:\n{dfQry}\n")
        dctRes = dfQry.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes[0]}
    else:
        return {"success": False, "messages": "Invalid parameters", "data":{}}


# Query DB for UI Options
def getIpConfigs(dct):
    logging.info("Executing IPAM Configs query...")
    print(dct)
    # where = BuildAndQuery(dct)
    # qrytxt = f"select * from vw_uioptions3 where {where} ;"
    qrytxt = f"""select * from ipconfigs
        where upper(aso_tag) = upper('{dct['aso_tag']}')
            and upper(app_type) = upper('{dct['app_type']}')
            and upper(os_type) = upper('{dct['os_type']}')
            and upper(sla_type) = upper('{dct['sla_type']}')
        ;"""
    dfQry = dbQuery(qrytxt)
    if not dfQry.empty:
        print(f"IP Config Results:\n{dfQry}\n")
        dctRes = dfQry.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes[0]}
    else:
        return {"success": False, "messages": "Invalid parameters", "data":{}}

# Query DB for UI Options
def getHostingConfigs(dct):
    logging.info("Executing Hosting Configs query...")
    # print(dct)
    qrytxt = f"""select * from vcconfigs
        where upper(aso_tag) = upper('{dct['aso_tag']}')
            and upper(app_types) like upper('%{dct['app_type']}%')
            and upper(os_type) = upper('{dct['os_type']}')
            and upper(sla_type) = upper('{dct['sla_type']}')
        ;"""
    dfQry = dbQuery(qrytxt)
    if not dfQry.empty:
        print(f"Hosting Results:\n{dfQry}\n")
        dfTmp = dfQry.transpose()
        print(dfTmp,"\n")
        dctRes = dfTmp.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes}
    else:
        return {"success": False, "messages": "Invalid parameters", "data":[]}

# Query DB for UI Options
def getVcConfigs(dct):
    logging.info("Executing Hosting Configs query...")
    # print(dct)
    qrytxt = f"""select * from vcconfigs
        where upper(aso_tag) = upper('{dct['aso_tag']}')
            and upper(app_types) like upper('%{dct['app_type']}%')
            and upper(os_type) = upper('{dct['os_type']}')
            and upper(sla_type) = upper('{dct['sla_type']}')
        ;"""
    dfQry = dbQuery(qrytxt)
    if not dfQry.empty:
        print(f"Hosting Results:\n{dfQry}\n")
        dctRes = dfQry.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes}
    else:
        return {"success": False, "messages": "Invalid parameters", "data":[]}

# Query DB for UI Options
def getResources(dct):
    logging.info("Executing Resources query...")
    # print(dct)
    qrytxt = f"""select * from ui_resources
        where aso_tag = '{dct['aso_tag']}'
            and app_type = '{dct['app_type']}'
            and os_type = '{dct['os_type']}'
            and sla_type = '{dct['sla_type']}'
        ;"""
    dfQry = dbQuery(qrytxt)
    if not dfQry.empty:
        print(f"Resources Results:\n{dfQry}\n")
        dctRes = dfQry.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes[0]}
    else:
        return {"success": False, "messages": "Invalid parameters", "data":{}}

# Get quoting rates
def getDcsRates(dctQry):
    logging.info("Executing Rates query...")
    # logging.info(f"{dctQry}")
    qry = f" SELECT aso_tag, fy_period, os_type, cpu as vcpus, ram as vram, srm as vmware_srm,\
        stg_tier_2 as gold_disks, stg_tier_3 as bronze_disks, stg_back_up as backups,\
        (hardware_maintenance+sgt_cost+shared_tech+bcx_service_cost+software_maintenance+ha_licence+facilities+fc_port) as support\
        FROM dcsrates WHERE aso_tag = '{dctQry['aso_tag'].strip().upper()}' AND os_type like '%{dctQry['os_type'].strip()}%' AND fy_period = '{dctQry['fy_period']}' ;"
    logging.warning(f"QUERY\n{qry}\n")
    dfQry = dbQuery(qry)
    if not dfQry.empty:
        print(f"DCS Rates Results:\n{dfQry}\n")
        dfQry.fillna(0, inplace=True)
        dfQry['vmware_srm'] = dfQry['vmware_srm'].astype(int)
        dctRes = dfQry.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes[0]}
    else:
        return {"success": False, "messages": "Rates not found or Invalid parameters", "data":{}}


# Def get VM Compute Resources
def getVCompute(dct):
    logging.info("Executing VM Compute query...")
    qrytxt = \
        f"select * from vinfo_latest where vm = '{dct['vm'].lower()}';"
    dfQry = dbQuery(qrytxt)
    if not dfQry.empty:
        print(f"Results: {dfQry}\n")
        dctRes = dfQry.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes[0]}
    else:
        logging.error(f"{dct['vm'].lower()} compute query failed")
        return {"success": False, "messages": f"{dct['vm'].lower()} compute query failed", "data": {} }


# Def get VM Storage Resources
def getVStorage(dct):
    logging.info("Executing VM Storage query...")
    qrytxt = \
        f"select * from vdisk_latest where vm = '{dct['vm'].lower()}';"
    dfQry = dbQuery(qrytxt)
    if not dfQry.empty:
        print(f"Results: {dfQry}\n")
        dctRes = dfQry.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes}
    else:
        logging.error(f"{dct['vm'].lower()} disks query failed")
        return {"success": False, "messages": f"{dct['vm'].lower()} disks query failed", "data": [] }


# Def get VM Annotations
def getAnnotations(dct):
    logging.info("Executing VM Annotations query...")
    qrytxt = \
        f"select * from ebxservers where server_name = '{dct['vm'].lower()}';"
    dfQry = dbQuery(qrytxt)
    if not dfQry.empty:
        print(f"Results: {dfQry}\n")
        dctRes = dfQry.to_dict(orient='records')
        return {"success": True, "messages" : "", "data":dctRes[0]}
    else:
        return {"success": False, "messages": f"{dct['vm'].lower()} Annotations not found, bad spelling, or Invalid parameters", "data":{}}


# Def get VM Compute Resources
def getVSpecs(dct):
    logging.info(f"Executing {dct['vm'].lower()} VM Specs query...")
    messages = []
    dctSpecs = {}
    # dctSpecs.update({"data": "", "messages": messages, "success": False})

    annotations_response = getAnnotations(dct)

    if annotations_response['success']:
        # Add annotations data to response
        dctSpecs['data'] = { 'annotations': annotations_response['data'] }
        # dctSpecs['data'] = { 'annotations': annotations_response['data'] }
        if annotations_response['data']['decommissioned_ind.key'] == "Yes":
            msg = f"{dct['vm'].lower()} is decommissioned"
            logging.error(msg)
            messages.append(msg)       #(f"{dct['vm'].lower()} is decommissioned")
            dctSpecs.update({ 'success': False })
            dctSpecs['data'].update({ 'compute' : {'message': msg},'disks' : [{'message': msg}] , 'rates':{ 'message': msg } })
        elif ("-" not in annotations_response['data']['serial_number']) and ( annotations_response['data']['serial_number'] != "None"):
            msg = f"{dct['vm'].lower()} is a physical"
            logging.error(msg)
            messages.append(msg)        #(f"{dct['vm'].lower()} is a physical")
            dctSpecs.update({ 'success': False })
            dctSpecs['data'].update({ 'compute' : {'message': msg},'disks' : [{'message': msg}] , 'rates':{ 'message': msg } })
        else:
            now = datetime.now()
            fy_period = now.year
            os_type = tagOS(annotations_response['data'])
            aso_tag = tagASO(annotations_response['data']['asset_owner_name.key'])
            dct.update({ "aso_tag": aso_tag, "fy_period": fy_period, "os_type": os_type })
            rates_response = getDcsRates(dct)
            if rates_response['success']:
                dct_rates = rates_response['data']
                dctSpecs['data'].update({ 'rates': dct_rates })
                dctSpecs.update({ 'success': True })
            else:
                messages.append(rates_response["messages"])
                dctSpecs['data'].update({ "rates": {} })
            dctCompute = getVCompute(dct)
            if not dctCompute["success"]:
                messages.append(dctCompute["messages"])
                dctSpecs.update({ 'success': False })

            dctStorage = getVStorage(dct)
            if not dctStorage["success"]:
                messages.append(dctStorage["messages"])
                dctSpecs.update({ 'success': False })

            dctSpecs['data'].update({ 'compute' : dctCompute['data'],'disks' : dctStorage['data'] })
            # Mark as successful if you made it this far!
            dctSpecs.update({ 'success': True })

        dctSpecs.update({ 'messages': messages })
        # print("Print Server Details:")
        # print(json.dumps(jsonable_encoder(dctSpecs), indent=2))
        return dctSpecs
    else:
        messages.append(annotations_response["messages"])
        dctSpecs['data'].update({ "rates": {} })
        dctSpecs.update({ 'success': False })

    dctSpecs.update({ 'messages': messages })
    # print("Print Server Details:")
    # print(json.dumps(jsonable_encoder(dctSpecs), indent=2))
    return dctSpecs


# Query DB for UI Options
def UiOptions(options):
    print(f"UI Filters:\n{options}")
    dctRes = {}

    if key_exists('client',options):
        try:
            dctClient = getClient(options)[0]
            # dctRes.update({'data':dctClient})
        except:
            return {"messages" : "Invalid client selected", "data":""}
    else:
        return {"messages" : "No client specified", "data":""}

    deployment = {**options, **dctClient}    # merge Client query result with options
    print("UI DEPLOYMENT\n",deployment)

    if key_exists('deployment',options):
        try:
            dctDeployTypes = getDeployment(deployment) #[0] #getEnvirons(options) #
            # dctRes.append({'data':dctEnvs})
        except:
            return {"messages" : "Invalid deployment or app/os type selection", "data":""}
    else:
        return {"messages" : "No deployment specified", "data":""}
    
    if key_exists('app_type',options):
        try:
            dctOStypes = getOStypes(options)
        except:
            return {"messages" : "Invalid OS or App Type", "data":""}
    else:
        return {"messages" : "No OS or App Type specified", "data":""}
    
    dctRes['data'] = {**dctClient, **dctDeployTypes, **dctOStypes} #, **dctEnvs} #}
    # Get IP Options
    try:
        dctIpConfigs = getIpConfigs(dctRes['data']) #[0] #getEnvirons(options) #
        # dctRes.append({'data':dctEnvs})
    except:
        return {"messages" : "Invalid selections or No IP configs defined!", "data":""}
    # Get Resource Options
    try:
        dctResources = getResources(dctRes['data']) #[0] #getEnvirons(options) #
        # dctRes.append({'data':dctEnvs})
    except:
        return {"messages" : "Invalid selections or No server resources defined!", "data":""}

    dctOptions = {**dctClient, **dctDeployTypes, **dctOStypes, **dctResources, **dctIpConfigs}
    dctRes['data'] = dictval2list(dctOptions,",") #{**dctClient, **dctDeployTypes, **dctOStypes, **dctResources}
    # print(f"Results: {len(dctRes)}\n")
    return dctRes
