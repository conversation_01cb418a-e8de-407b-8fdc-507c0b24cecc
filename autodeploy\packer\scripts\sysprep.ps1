# Sysprep Script
# This script prepares the Windows system for imaging by running sysprep

Write-Host "Preparing system for imaging with Sysprep..."

# Create sysprep answer file for generalization
$sysprepAnswerFile = @"
<?xml version="1.0" encoding="utf-8"?>
<unattend xmlns="urn:schemas-microsoft-com:unattend">
    <settings pass="generalize">
        <component name="Microsoft-Windows-Security-SPP" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <SkipRearm>1</SkipRearm>
        </component>
        <component name="Microsoft-Windows-PnpSysprep" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <PersistAllDeviceInstalls>false</PersistAllDeviceInstalls>
            <DoNotCleanUpNonPresentDevices>false</DoNotCleanUpNonPresentDevices>
        </component>
    </settings>
    <settings pass="oobeSystem">
        <component name="Microsoft-Windows-Shell-Setup" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <OOBE>
                <HideEULAPage>true</HideEULAPage>
                <HideLocalAccountScreen>true</HideLocalAccountScreen>
                <HideOEMRegistrationScreen>true</HideOEMRegistrationScreen>
                <HideOnlineAccountScreens>true</HideOnlineAccountScreens>
                <HideWirelessSetupInOOBE>true</HideWirelessSetupInOOBE>
                <NetworkLocation>Work</NetworkLocation>
                <ProtectYourPC>1</ProtectYourPC>
                <SkipUserOOBE>true</SkipUserOOBE>
                <SkipMachineOOBE>true</SkipMachineOOBE>
            </OOBE>
            <UserAccounts>
                <AdministratorPassword>
                    <Value>UABAAHMAcwB3ADAAcgBkADEAMgAzACEAUABhAHMAcwB3AG8AcgBkAA==</Value>
                    <PlainText>false</PlainText>
                </AdministratorPassword>
            </UserAccounts>
            <TimeZone>UTC</TimeZone>
            <RegisteredOwner>Organization</RegisteredOwner>
            <RegisteredOrganization>Organization</RegisteredOrganization>
        </component>
        <component name="Microsoft-Windows-International-Core" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <InputLocale>en-US</InputLocale>
            <SystemLocale>en-US</SystemLocale>
            <UILanguage>en-US</UILanguage>
            <UserLocale>en-US</UserLocale>
        </component>
    </settings>
    <settings pass="specialize">
        <component name="Microsoft-Windows-Shell-Setup" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <ComputerName>*</ComputerName>
            <TimeZone>UTC</TimeZone>
        </component>
        <component name="Microsoft-Windows-Deployment" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <RunSynchronous>
                <RunSynchronousCommand wcm:action="add">
                    <Order>1</Order>
                    <Path>powershell.exe -ExecutionPolicy Bypass -Command "Set-ExecutionPolicy RemoteSigned -Force"</Path>
                    <Description>Set PowerShell Execution Policy</Description>
                </RunSynchronousCommand>
            </RunSynchronous>
        </component>
    </settings>
</unattend>
"@

# Save the sysprep answer file
$sysprepAnswerPath = "C:\Windows\System32\Sysprep\unattend.xml"
try {
    $sysprepAnswerFile | Out-File -FilePath $sysprepAnswerPath -Encoding UTF8
    Write-Host "Sysprep answer file created at $sysprepAnswerPath"
} catch {
    Write-Host "Failed to create sysprep answer file: $($_.Exception.Message)"
    exit 1
}

# Stop and disable services that might interfere with sysprep
Write-Host "Stopping services before sysprep..."
$servicesToStop = @(
    "wuauserv",      # Windows Update
    "BITS",          # Background Intelligent Transfer Service
    "Themes",        # Themes service
    "WSearch"        # Windows Search
)

foreach ($service in $servicesToStop) {
    try {
        $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
        if ($svc -and $svc.Status -eq "Running") {
            Stop-Service -Name $service -Force
            Write-Host "Stopped service: $service"
        }
    } catch {
        Write-Host "Could not stop service $service : $($_.Exception.Message)"
    }
}

# Clear any pending Windows updates
Write-Host "Clearing pending Windows updates..."
try {
    dism /online /cleanup-image /startcomponentcleanup /resetbase
    Write-Host "Component cleanup completed."
} catch {
    Write-Host "Component cleanup failed: $($_.Exception.Message)"
}

# Remove any pending reboot flags
Write-Host "Clearing reboot flags..."
try {
    Remove-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\RebootRequired" -Name "*" -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\PostRebootReporting" -Force -ErrorAction SilentlyContinue
    Remove-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager" -Name "PendingFileRenameOperations" -Force -ErrorAction SilentlyContinue
    Write-Host "Reboot flags cleared."
} catch {
    Write-Host "Failed to clear reboot flags: $($_.Exception.Message)"
}

# Final registry cleanup
Write-Host "Performing final registry cleanup..."
try {
    # Remove machine-specific information
    Remove-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion" -Name "InstallDate" -Force -ErrorAction SilentlyContinue
    Remove-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion" -Name "DigitalProductId" -Force -ErrorAction SilentlyContinue
    
    # Clear network adapter information
    Remove-Item -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4D36E972-E325-11CE-BFC1-08002BE10318}\*" -Include "????*" -Recurse -Force -ErrorAction SilentlyContinue
    
    Write-Host "Registry cleanup completed."
} catch {
    Write-Host "Registry cleanup failed: $($_.Exception.Message)"
}

# Verify sysprep can run
Write-Host "Verifying sysprep readiness..."
$sysprepPath = "C:\Windows\System32\Sysprep\sysprep.exe"

if (-not (Test-Path $sysprepPath)) {
    Write-Host "ERROR: Sysprep executable not found at $sysprepPath"
    exit 1
}

# Check if system is already sysprepped
$sysprepState = Get-ItemProperty -Path "HKLM:\SYSTEM\Setup" -Name "SystemSetupInProgress" -ErrorAction SilentlyContinue
if ($sysprepState -and $sysprepState.SystemSetupInProgress -eq 1) {
    Write-Host "WARNING: System appears to be in setup mode already"
}

# Run sysprep
Write-Host "Running sysprep..."
Write-Host "This will generalize the system and shut it down..."

try {
    # For VMware templates, we want to generalize and shutdown
    # For AWS AMIs, Packer will handle the shutdown
    $sysprepArgs = "/generalize /oobe /shutdown /unattend:`"$sysprepAnswerPath`""
    
    Write-Host "Executing: $sysprepPath $sysprepArgs"
    Start-Process -FilePath $sysprepPath -ArgumentList $sysprepArgs -Wait
    
    Write-Host "Sysprep completed successfully."
} catch {
    Write-Host "ERROR: Sysprep failed: $($_.Exception.Message)"
    exit 1
}

# This point should not be reached if sysprep shuts down the system
Write-Host "Sysprep script completed."
