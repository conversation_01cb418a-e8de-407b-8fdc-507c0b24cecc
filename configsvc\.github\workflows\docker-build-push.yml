name: <PERSON><PERSON> and Push Docker Image (Dev)

on:
  push:
    branches: [ "dev", "ppe", "main" ]

jobs:
  build_and_push:
    name: Build and Push to DockerHub
    runs-on: ubuntu-latest
    permissions:
      contents: read
    
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'
      
      - name: Extract version from pyproject.toml
        id: get_version
        run: |
          VERSION=$(grep "version" pyproject.toml | head -n 1 | awk -F '"' '{print $2}')
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "version=$VERSION" >> $GITHUB_OUTPUT
      
      - name: Log in to Docker Hub
        uses: docker/login-action@f4ef78c080cd8ba55a85445d5b36e214a81df20a
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      
      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
        with:
          images: ${{ secrets.DOCKER_USERNAME }}/configsvc
          tags: |
            type=raw,value=${{ github.ref_name }}
            type=raw,value=${{ env.VERSION }}
            type=raw,value=${{ github.ref_name }}-${{ env.VERSION }}
            type=raw,value=prd,enable={{is_default_branch}}
            type=sha,format=short
      
      - name: Build and push Docker image
        uses: docker/build-push-action@3b5e8027fcad23fda98b2e3ac259d8d67585f671
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            VERSION=${{ env.VERSION }}
