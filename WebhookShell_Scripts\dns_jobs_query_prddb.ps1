param(
  [Parameter(Mandatory=$true)] $reference,
  [Parameter(Mandatory=$true)] $requested_by,
  [Parameter(Mandatory=$true)] $requested_on,
  [Parameter(Mandatory=$true)] $catagory,
  [Parameter(Mandatory=$true)] $record_type,
  [Parameter(Mandatory=$true)] $act_date,
  [Parameter(Mandatory=$true)] $ptr,
  [Parameter(Mandatory=$true)] $hostname,
  [Parameter(Mandatory=$true)] $ip_address,
  [Parameter(Mandatory=$true)] $host_alias,
  [Parameter(Mandatory=$true)] $new_hostname,
  [Parameter(Mandatory=$true)] $new_ip_address,
  [Parameter(Mandatory=$true)] $new_host_alias,
  [Parameter(Mandatory=$true)] $status,
  [Parameter(Mandatory=$true)] $completed_datetime,
  [Parameter(Mandatory=$true)] $jobTask,
  [Parameter(Mandatory=$true)] $success
)

$DB = "MariaDB"
$DBtableTemplate = [ordered]@{Server="";Database="";Port="";User="";Password="";Credential="";Driver="";DLL="";Option=""}
$dbTable = @{}
$dbObj = New-Object psobject -Property $DBtableTemplate
$dbobj.Database = "dcsPRDautomation"
$dbobj.Server = "srv009403.mud.internal.co.za"
$dbobj.Credential = Import-Clixml -Path "D:\AutoDeploy\RequiredFiles\Credentials\prd_db_creds.xml"
$dbObj.Driver = "{MariaDB ODBC 3.1 Driver}"
$dbobj.Option = 67108867
$dbTable.Add("MariaDB",$dbobj)
$dbConnection = $NULL

function connectdb {
    if ($null -eq $script:DBconnection) {
        $script:DBconnection = New-Object System.Data.Odbc.OdbcConnection
        $script:DBconnection.ConnectionString = "DRIVER={0};Server = {1};Database = {2};UID = {3};PWD = ***;Option = {5}" -f $dbTable[$db].Driver,$dbTable[$db].Server,$dbTable[$db].Database,$dbTable[$db].Credential.UserName,$dbTable[$db].Credential.GetNetworkCredential().Password,$dbTable[$db].Option
    }
}
function opendb {
    $success = $false
    if ($null -eq $DBconnection) {
      connectdb
    }
    if ($DBconnection.State -eq "Closed") {
      try {
        $DBconnection.open()
        $success = $true
      }
      catch {
        "Unable to sign in to database"
      }
    }
    return $success
}

function dbQuery ([string]$query, [hashtable]$parameters = @{}) {
    try {
      $dbCommand = $script:DBconnection.CreateCommand()
      $dbCommand.CommandText = $query
      
      # Add parameters
      foreach ($key in $parameters.Keys) {
        $param = $dbCommand.CreateParameter()
        $param.ParameterName = $key
        $param.Value = $parameters[$key]
        [void]$dbCommand.Parameters.Add($param)
      }
      
      [void]$dbCommand.ExecuteNonQuery()
      $dbCommand.Dispose()
      
      $xlrSuccess = $true
      $xlrStatus = "NORMAL"
      $xlrMessage = "Query into database successful"    
    }
    catch {  
      $xlrSuccess = $false
      $xlrStatus = "REMEDIATION"
      $xlrMessage = "Failed to query into the database: $($_.Exception.Message)"
    }

  $xlrResponse = "" | Select-Object success, status, message, data
  $xlrResponse.success = $xlrSuccess
  $xlrResponse.status = $xlrStatus
  $xlrResponse.message = $xlrMessage
  $xlrResponse.data = $query

  return $xlrResponse
}

if($null -eq $requested_on){
    $requested_on_final = ""
}else{
    $requested_on_final = (Get-Date -Format 'yyyy-MM-ddTHH:mm:00')
}

if($null -eq $completed_datetime){
    $completed_datetime_final = ""
}else{
    $completed_datetime_final = (Get-Date -Format 'yyyy-MM-ddTHH:mm:00')
}

if($ptr -match "false"){
  $ptr = 0
}else{
    $ptr = 1
}

$info = [pscustomobject] @{
  "reference"= $reference
  "requested_by"= $requested_by
  "requested_on"= $requested_on_final
  "catagory"= $catagory
  "record_type"= $record_type
  "act_date"= $act_date
  "ptr"= $ptr
  "hostname"= $hostname
  "ip_address"= $ip_address
  "host_alias"= $host_alias
  "new_hostname"= $new_hostname
  "new_ip_address"= $new_ip_address
  "new_host_alias"= $new_host_alias
  "dns_status"= $status
  "completed_datetime"= $completed_datetime
  "success"= $success
}

if ((opendb) -eq $false) {
  $xlrSuccess = $false
  $xlrStatus = "REMEDIATION"
  $xlrMessage = "Failed to Connect to database"
}else{
  if($jobTask -eq "ADD_JOB"){
    $queryStuff = dbQuery ('INSERT INTO dnsrequests (reference, requested_by, requested_on, catagory, record_type, act_date, ptr, hostname, ip_address, host_alias, new_hostname, new_ip_address, new_host_alias, dns_status) VALUES ("{0}", "{1}", "{2}", "{3}", "***", "{5}", "{6}", "{7}", "{8}", "{9}", "{10}", "{11}", "{12}", "{13}")' -f $info.reference, $info.requested_by, $info.requested_on, $info.catagory, $info.record_type, $info.act_date, $info.ptr, $info.hostname, $info.ip_address, $info.host_alias, $info.new_hostname, $info.new_ip_address, $info.new_host_alias, $info.status)
  }elseif($jobTask -eq "UPDATE_JOB"){
    $queryStuff = dbQuery ('UPDATE dnsrequests SET dns_status = "{0}", success = "{1}", completed_datetime = "{2}" WHERE reference = "{3}"' -f $info.dns_status, $info.success, $info.completed_datetime, $info.reference)
  }

  $xlrSuccess = $queryStuff.success
  $xlrStatus = $queryStuff.status
  $xlrMessage = $queryStuff.message
  $xlrData = $queryStuff.query

}
$xlrResponse = "" | Select-Object success, status, message, data
$xlrResponse.success = $xlrSuccess
$xlrResponse.status = $xlrStatus
$xlrResponse.message = $xlrMessage
$xlrResponse.data = $xlrData
return $($xlrResponse | ConvertTo-Json)
