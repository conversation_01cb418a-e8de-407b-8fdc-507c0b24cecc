from datetime import datetime, date, time
import json
from app import logging

from app.core.helpers import key_exists, dictval2list, dct2df, recast_json
#import MariaDB connection
from app.core.database import dbQuery


# Query DB for UI Options
def UiInit():
    try:
        # Query db for initial UI options
        qrytxt = f"select * from ui_init;"
        logging.info(f"QUERY: {qrytxt}")
        dfQry = dbQuery(qrytxt)
        dfQry['updated'] = dfQry['updated'].astype(str)
        dfInit = recast_json(dfQry)
        dctRes = dfInit.to_dict(orient='records')
    except:
        # Return error
        message = {"message" : "Issue with UI Init data in table", "data":""}
        logging.warning(message['message'])
        return message
    return dictval2list(dctRes[0],",")

# Process Filters
def ProcessSpecs(dct):
    logging.warning(f"PARAMETERS => {dct}")
    if not bool(dct):
        logging.warning(f"PARAMETERS issue...")
        success = False
        data = ""
        message = f"Verify UI selections..."
        status = "WARN"
        logging.warning(message)
    else:
        respdata = UiSpecs(dct)
        # dctClient = getClient(params)
        # dctEnviron = getEnvirons(params)
        # respdata = {**dctClient, **dctEnviron}
        # print(json.dumps(respdata, indent=2),"\n")   
        if respdata['data'] == "":
            success = False
            data = ""
            message = respdata['message'] #f"Invalid selection, please retry."
            status = "FAILED"
            logging.error(message)
        else:
            success =True
            message = f"Result(s) returned."
            data = respdata['data']
            status = "NORMAL"
            logging.info(message)
    result = {
        'data' : data,
        'status': status, 
        'success' : success,
        'messages' : message
    }

    return result

# Query DB for UI Options

def getClient(dct):
    qrytxt = f"select * from clients where lower(client_tag) = lower('{dct['client_tag']}');"
    logging.info(f"QUERY - getClient: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    return dctRes[0]

def getDeployment(dct):
    # print(dct)
    qrytxt = f"select * from ui_deployments where lower(deployment) = lower('{dct['deployment']}')\
        and upper(aso_tag) = upper('{dct['aso_tag']}')\
        and lower(app_types) like lower('%{dct['app_types']}%');"
    logging.info(f"QUERY - getDeployment: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    return dctRes[0]

def getOStypes(dct):
    qrytxt = \
        f"select * from ui_ostypes where os_type = '{dct['os_types']}' and app_type = '{dct['app_types']}' ;"
    logging.info(f"QUERY - getOStypes: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    # print(f"Results: {len(dctRes)}\n")
    # print(f"Values: {dctRes}\n")
    return dctRes[0]

# Query DB for UI Options
def getIpConfigs(dct):
    qrytxt = f"select * from ipconfigs\
        where aso_tag = '{dct['aso_tag']}'\
            and app_type = '{dct['app_type']}'\
            and os_type = '{dct['os_type']}'\
            and sla_type = '{dct['sla_type']}'\
        ;"
    logging.info(f"QUERY - getIpConfigs: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    # print(f"Results: {len(dctRes)}\n")
    # print(f"Values: {dctRes}\n")
    return dctRes[0]

# Query DB for UI Options
def getResources(dct):
    # print(dct)
    qrytxt = f"select * from ui_resources\
        where aso_tag = '{dct['aso_tag']}'\
            and app_type = '{dct['app_type']}'\
            and os_type = '{dct['os_type']}'\
            and sla_type = '{dct['sla_type']}'\
        ;"
    logging.info(f"QUERY - getResources: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    # print(f"Results: {len(dctRes)}\n")
    # print(f"Values: {dctRes}\n")
    return dctRes[0]

# Query DB for UI Options
def getHosting(dct):
    # print(dct)
    qrytxt = f"select * from vcconfigs\
        where aso_tag = '{dct['aso_tag']}'\
            and lower(app_types) like lower('%{dct['app_type']}%')\
            and os_type = '{dct['os_type']}'\
            and sla_type = '{dct['sla_type']}'\
        ;"
    logging.info(f"QUERY - getHosting: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dfQry.drop(columns=['updated'], inplace=True)
    dctRes = dfQry.to_dict(orient='records')
    # print(f"Results: {len(dctRes)}\n")
    # print(f"Values: {dctRes}\n")
    return dctRes

# Query AD Configs
def getADConfigs(dct):
    # print(dct)
    qrytxt = f"select * from adconfigs\
        where client_tag = '{dct['client_tag']}'\
            and lower(app_type) like lower('%{dct['app_type']}%')\
            and os_version = '{dct['os_version']}'\
            and env_type = '{dct['env_type']}'\
        ;"
    logging.info(f"QUERY - getResources: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    # print(f"Results: {len(dctRes)}\n")
    # print(f"Values: {dctRes}\n")
    return dctRes[0]

# Query DB for UI Options
def getOSConfigs(dct):
    # print(dct)
    qrytxt = f"select * from osconfigs\
        where lower(os_version) = lower('{dct['os_version']}')\
        ;"
    # Add to OS query as needed
    # and lower(app_type) like lower('%{dct['app_type']}%')\
    # and os_version = '{dct['os_version']}'\
    # and env_type = '{dct['env_type']}'\
            
    logging.info(f"QUERY - getResources: {qrytxt}")
    dfQry = recast_json(dbQuery(qrytxt))
    print(f"Results: {dfQry}\n")
    dctRes = dfQry.to_dict(orient='records')
    # print(f"Results: {len(dctRes)}\n")
    # print(f"Values: {dctRes}\n")
    return dctRes[0]


# Query DB for UI Options
def UiSpecs(options):
    print(f"\nSPECS:\n{options}")
    dctRes, data = {}, {}
    data.update({'specs':options})
    # Get Hosting Options
    try:
        dctHosting = getHosting(options) #[0] #getEnvirons(options) #
        # dctRes['data'] = dctHosting
        data.update({'hosting':dctHosting})
    except Exception as e:
        logging.warning(f"Exception: {e}")
        return {'message' : "Invalid selections or No hosting defined!", "data":""}
    
    # Get Client Config
    if key_exists('client_tag',options):
        try:
            dctClient = getClient(options)
            data.update({'client':dctClient})
            # dctRes['data'].update({'client':dctClient})
        except:
            # return {'message' : "Invalid client selected", "data":""}
            message = "Invalid client selected"
            logging.error(f"Exception: {e}")
            logging.warning(f"Issue: {message}")
            return {'message' : message, "data":""}
    else:
        return {'message' : "No client_tag specified", "data":""}
    
    # Get IP Config
    try:
        dctIpConfigs = getIpConfigs(options)
        data.update({'ipconfig':dctIpConfigs})
    except Exception as e:
        message = "Invalid selections or No IP configs defined!"
        logging.error(f"Exception: {e}")
        logging.warning(f"Issue: {message}")
        return {'message' : message, "data":""}
    
    # Get AD Config
    try:
        dctADConfigs = getADConfigs(options)
        data.update({'adconfig':dctADConfigs})
    except Exception as e:
        
        message = "Invalid selections or No AD configs defined!"
        logging.error(f"Exception: {e}")
        logging.warning(f"Issue: {message}")
        return {'message' : message, "data":""}

    # Get OS Config
    try:
        dctOSConfigs = getOSConfigs(options)
        data.update({'osconfig':dctOSConfigs})
    except Exception as e:
        logging.warning(f"Exception: {e}")
        return {'message' : "Invalid selections or No OS configs defined!", "data":""}
   

    dctRes['data'] = data

    # print(f"Results: {len(dctRes)}\n")
    return dctRes
