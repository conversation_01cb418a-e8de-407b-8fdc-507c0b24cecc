from fastapi import BackgroundTasks
import pandas as pd
import numpy as np
from datetime import datetime
from app.core.config import config
import ftplib

class Logging:
    def __init__(self, background_task: BackgroundTasks):
        background_task.add_task(self._send_log)

    async def _send_log(self):
        pass

# Check if Key exists in Dictionary
def key_exists(key,dict_obj):
    if key in dict_obj:
        return True
    else:
        return False
# Recast data types for json
def recast_json(df):
    for column, dt in zip(df.columns, df.dtypes):
        if dt.type not in [np.int64, np.float64, np.bool_,np.datetime64]:
            df.loc[:, column] = df[column].astype(str)
    return df
    
# Convert string with delimiter-separated items to list, else return string if only one item.
def str2list(string,dlm):
    if dlm in string:
        strlist = string.split(dlm)
        strlist = [x.strip() for x in strlist]
        return strlist
    else:
        return string

# Convert list with delimiter-separated items to string 
def list2str(lst):
    string = str(lst).strip("[|]")
    string = '('+string+')'
    print(string)
    return string

# Convert Value to Int, else return String
def convert_int(x):
    try:
        return int(float(x))
    except ValueError:
        return x
    
# Convert string to integer else return string
def str2int(string):
    try:
        return int(string)
    except:
        return string

# Convert dictionary key values to values as list if delimited
def dictval2list(dctIn,dlm):
    dctOut = {}
    for key, value in dctIn.items():
        # if isinstance(value, datetime):
        #     dctOut.update({key : str(value)})
        if not isinstance(value, (int, float)):
            dctOut.update({key : str2list(value,dlm)})
        else:
            dctOut.update({key : value})
    return dctOut

# Convert Dict to Dataframe
def dct2df(dctIn):
    dct = {k:[v] for k,v in dctIn.items()}
    df = pd.DataFrame(dct)
    # df = df.append(dctIn, ignore_index=True)
    # print(df)
    return df

# FTP Downloads
def ftp_get(filename,ftpfolder,destination):
    print(f"FTP Download: {filename} from {ftpfolder} to {destination}")
    # Connect FTP Server and Upload XLSX data file
    ftpServer = ftplib.FTP(config.FTP_HOST, config.FTP_USER, config.FTP_PWRD)
    # force UTF-8 encoding
    ftpServer.encoding = "utf-8"
    # Reset destination path on FTP Server to root
    ftpServer.cwd("/")    
    # Change destination path on FTP Server
    ftpServer.cwd(ftpfolder)
    # Read file in binary mode
    with open(destination, "wb") as file:
        # Command for Uploading the file "STOR filename"
        try:
            ftpServer.retrbinary(f"RETR {filename}", file.write)
            ftp_ok = True
            msg = (f"ftp2path -> FTP Downloaded {ftpfolder}{filename} to {destination}")
            print(msg)
        except Exception as e:
            msg = f"ftp2path -> Error with FTP source {filename}: {e}"
            # print(f"FTP Error with {filename}: {e}")
            ftp_ok = False
            # Close the Connection
            ftpServer.quit()
            return ftp_ok, msg
    ftpServer.quit()
    return ftp_ok, msg

def ftp_post(source, filename,ftpfolder):
    print(f"FTP Upload: from {source} as {filename} to {ftpfolder}")
    # Connect FTP Server and Upload XLSX data file
    ftpServer = ftplib.FTP(config.FTP_HOST, config.FTP_USER, config.FTP_PWRD)
    # force UTF-8 encoding
    ftpServer.encoding = "utf-8"
    # Reset destination path on FTP Server to root
    ftpServer.cwd("/")    
    # Change destination path on FTP Server
    ftpServer.cwd(ftpfolder)
    # Read file in binary mode
    with open(source, "rb") as file:
        try:
            # Command for Uploading the file "STOR filename"
            ftpServer.storbinary(f"STOR {filename}", file)
            ftp_ok = True
            msg = (f"FTP Uploaded {filename} to {ftpfolder}/{filename}")
            print(msg)
        except Exception as e:
            msg = f"FTP Upload Failed: Error with FTP source {filename}: {e}"
            # print(f"FTP Error with {filename}: {e}")
            ftp_ok = False
            # Close the Connection
            ftpServer.quit()
            return ftp_ok, msg
    # Close the Connection
    ftpServer.quit()
    return ftp_ok, msg


def element_exists(element,lst):
  # Try to get the index of the element in the list
  try:
    lst.index(element)
  # If the element is found, return True
    print("Exists: ",True)
    return True
  # If a ValueError is raised, the element is not in the list
  except ValueError:
  # Return False in this case
    print("Exists: ",False)
    return False
  
