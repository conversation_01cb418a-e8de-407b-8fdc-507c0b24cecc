<#
    .SYNOPSIS
    Sends messages to Teams channels

    .DESCRIPTION
    Sends curated messages to Teams channels

    .EXAMPLE
    SendTeams.ps1

    .NOTES
    File Name: SendTeams.ps1
    Author: <PERSON><PERSON>
    Version 1.0 - 23/05/2025
#>
#requires -Version 5
#requires -Modules PSTeams

$TeamsLinkChannel = 'https://teams.microsoft.com/l/channel/19%3Ab6b53047ec904600ab15cf019d1d1c6f%40thread.skype/CPS%20Infra%20DevOps?groupId=df110042-736f-4313-9fad-5a05b71ec2af&tenantId=32acc968-ee0c-4a4b-a2a2-f578609a3785'

# Teams webhook URL
$TeamsWebhookUrl = 'https://bcx.webhook.office.com/webhookb2/df110042-736f-4313-9fad-5a05b71ec2af@32acc968-ee0c-4a4b-a2a2-f578609a3785/IncomingWebhook/bbda6d429ddd48139f10cf3cff3d4f14/f7d1b729-259a-48af-a44d-0c79cc862e41'

function Get-SvcAccLogon {
[CmdletBinding()]
    param (
        [Parameter()]
        [string]
        $ServerName,
        [Parameter()]
        [string]
        $ServiceAccount
    )
    $user = qwinsta $ServiceAccount /server:$ServerName
    if ($user) {
        Write-Host "$ServiceAccount is Logged On $ServerName"
    }
    else {
        Write-Host "Couldn't Query Server or Account Is Not Logged On!"
    }
return
}

Function checkIPAM {
    $phpSite = Test-Connection ipam.sanlam.co.za -quiet -Count 1
    if($phpSite){
        $apiURL = "https://ipam.sanlam.co.za/ipam/api"
        $apiAppID = "automon"
        $apiKey = "2MFYKYtHs3BxGenwqVDeIvbO98g1U8PH"
        New-PhpIpamSession -UseStaticAppKeyAuth -PhpIpamApiUrl $apiURL -AppID $apiAppID -AppKey $apiKey | Out-Null
        $ipamSubnet = Get-PhpIpamSubnet -CIDR *********/22
        if ($ipamSubnet) {
            $timeDiff =  New-TimeSpan -Start $ipamSubnet.lastScan -End $timeStamp
            if ($timeDiff.Days -gt 0 | Out-Null) {
                $phpSiteCheck = "The last scan date was $($timeDiff.Days) days, $($timeDiff.Hours) hours, $($timeDiff.Minutes) minutes ago - Failed"
            }
            else {
                $phpSiteCheck = "The last scan date was $($timeDiff.Days) days, $($timeDiff.Hours) hours, $($timeDiff.Minutes) minutes ago - Passed"
            }
        }
        else {
            $phpSiteCheck = "There was an issue getting the subnet detail - Failed"
        }
    }
    $phpSiteCheck
}

Function checkAutoDeploy {
    $allTasks = @()
    $svcAccount = "svcscvmmadmin"
    $serverName = "srv009484"
    $AutoDeploySchedulers = Get-ScheduledTask -CimSession SRV009484 -TaskPath "\" | Where-Object {$_.TaskName -match "AutoDeploy" -and $_.State -notmatch "disabled"} | Get-ScheduledTaskInfo

    foreach($AutoDeployJob in $AutoDeploySchedulers){

        $lastRunTime = Get-Date $AutoDeployJob.LastRunTime -Format dd/MM/yyyy   
        $today =   Get-Date -Format dd/MM/yyyy 
        if($lastRunTime -eq $today){
            $todayRan = $true
        }else{
            $todayRan = $false
        }

        $taskListDetails = [PSCustomObject]@{
            TaskName = $AutoDeployJob.TaskName
            LastRun = $AutoDeployJob.LastRunTime
            RunToday = $todayRan
        }
        $allTasks += $taskListDetails

    }
    
    $AutoDeployDetails = $allTasks | Where-Object {$RunToday -eq "false"}
    if($AutoDeployDetails){
        $AutoDeployResult = $AutoDeployDetails.TaskName -join "," + " has not run yet today - failed"
    }else{
        $AutoDeployResult = "All Schedulers Running as scheduled - passed"
    }

    $svcAccountAutodeploy = Get-SvcAccLogon -ServerName $serverName -ServiceAccount $svcAccount
    if($svcAccountAutodeploy){
        $autodeployAccount = "Service Account is Logged On - passed"
    }else{
        $autodeployAccount = "Service Account is not Logged On - failed"
    }

    $finalResult = $AutoDeployResult + "`r`n" + $autodeployAccount
    $finalResult
}
function Send-SystemStatusReport {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$PHPIPAMStatus,
        
        [Parameter(Mandatory = $true)]
        [string]$AccountLogonStatus,
        
        [Parameter(Mandatory = $true)]
        [string]$AutoDeployStatus,
        
        [Parameter(Mandatory = $false)]
        [string]$AdditionalInfo = ""
    )
    
    # Get current date for report
    $CurrentDate = Get-Date -Format "dd-MM-yyyy HH:mm"

    # Determine overall status
    $overallStatus = "Healthy"
    $statusColor = "00FF00" # Green
    
    if ($PHPIPAMStatus -match "Failed" -or $AccountLogonStatus -match "Failed" -or $AutoDeployStatus -match "Failed") {
        $overallStatus = "Issues Detected"
        $statusColor = "FF0000" # Red
    }
    
    # Create message content
    $messageContent = @"
**PHPIPAM Status:**
$PHPIPAMStatus

**Account Logon Status:**
$AccountLogonStatus

**Auto Deploy Status:**
$AutoDeployStatus
"@

    if ($AdditionalInfo) {
        $messageContent += @"

**Additional Information:**
$AdditionalInfo
"@
    }
    
    # Send Teams message
    Send-TeamsMessage -URI $TeamsWebhookUrl -MessageTitle "System Status Report" -MessageText "Daily Health Check - $CurrentDate" -Color $statusColor {
        New-TeamsSection {
            New-TeamsActivityTitle -Title "**System Status Report**"
            New-TeamsActivitySubtitle -Subtitle "Status: $overallStatus"
            New-TeamsActivityText -Text $messageContent
            New-TeamsButton -Name 'View Monitoring Dashboard' -Link "https://srv009485:3000"
        }
    }
    
    Write-Host "System Status Report sent to Teams at $CurrentDate" -ForegroundColor Green
}




# Example usage:
# $phpStatus = "PHPIPAM last scan was 0 days, 2 hours, 15 minutes ago - Passed"
# $accountStatus = "Service Account is Logged On - Passed"
# $deployStatus = "All Schedulers Running as scheduled - Passed"
# Send-SystemStatusReport -PHPIPAMStatus $phpStatus -AccountLogonStatus $accountStatus -AutoDeployStatus $deployStatus
