import sys
import subprocess
import argparse
from enum import Enum
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel, Field

# Create an instance of the FastAPI application
api = FastAPI()

# Refactored: Use Enums for allowed commands for each tool.
class TerraformCommand(str, Enum):
    apply = "apply"
    plan = "plan"
    destroy = "destroy"
    init = "init"
    validate = "validate"
    version = "version"

class PackerCommand(str, Enum):
    build = "build"
    init = "init"
    validate = "validate"
    version = "version"
    fix = "fix"

# Generic payload to accept any command (for API extensibility and modularity.)
# TODO: Implement handling and validation for aws-cli, azure-cli, pwsh commands
class ToolCommandPayload(BaseModel):
    container: str = Field(..., min_length=1)
    command: list[str]
    env_vars: dict[str, str] = {}

# Refactored: A single, more flexible endpoint
@api.post("/api/run/{tool}")
def run_command(tool: str, payload: ToolCommandPayload):
    """
    Executes a command for a specified tool inside a Docker container.
    Supported tools: terraform, packer, powershell.
    """
    allowed_commands = {
        "terraform": [cmd.value for cmd in TerraformCommand],
        "packer": [cmd.value for cmd in PackerCommand],
        "pwsh": ["-Command", "-File"]  # Simple check for PowerShell
    }

    if tool not in allowed_commands:
        raise HTTPException(status_code=400, detail=f"Tool '{tool}' not supported. Supported tools are: {', '.join(allowed_commands.keys())}")

    if not payload.command:
        raise HTTPException(status_code=400, detail="Command cannot be empty.")
        
    # Validation for specific tools
    if tool in ["terraform", "packer"] and payload.command[0] not in allowed_commands[tool]:
         raise HTTPException(status_code=400, detail=f"Invalid or unsupported command for tool '{tool}'.")

    try:
        # Check if the container is running
        check_cmd = ["docker", "ps", "-q", "--filter", f"name={payload.container}"]
        container_check = subprocess.run(check_cmd, capture_output=True, text=True, check=True)
        if not container_check.stdout.strip():
            raise HTTPException(status_code=404, detail=f"Container '{payload.container}' not found or not running.")
        
        # Build the full docker command with environment variables and the tool-specific command
        docker_cmd = ["docker", "exec"]
        for key, value in payload.env_vars.items():
            docker_cmd.extend(["-e", f"{key}={value}"])
        
        docker_cmd.append(payload.container)
        
        if tool == "pwsh":
            docker_cmd.append("pwsh")
            docker_cmd.extend(payload.command)
        else:
            docker_cmd.append(tool)
            docker_cmd.extend(payload.command)

        # Execute the command inside the container
        process = subprocess.run(docker_cmd, capture_output=True, text=True, check=True, timeout=1800)
        return {
            "status": "success",
            "stdout": process.stdout,
            "stderr": process.stderr,
        }

    except subprocess.CalledProcessError as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Command execution failed",
                "message": e.stderr.strip(),
                "returncode": e.returncode,
            },
        )
    except subprocess.TimeoutExpired as e:
        raise HTTPException(
            status_code=504,
            detail={
                "error": "Command timed out",
                "message": f"Command execution took longer than {e.timeout} seconds.",
            },
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={"error": "An unexpected error occurred", "message": str(e)},
        )