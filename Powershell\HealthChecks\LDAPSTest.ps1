# Basic LDAPS connectivity test script

# Explicitly set TLS version
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12 -bor [System.Net.SecurityProtocolType]::Tls13

# Parameters
$LdapServer = "ad-ldap.sanlam.co.za"
$Port = 636
$Domain = "dc=mud,dc=internal,dc=co,dc=za"
$Username = "mud\SvcIPAMsolutionAcc"

# Test 1: Basic TCP connectivity to LDAPS port
Write-Host "Testing TCP connectivity to $LdapServer on port $Port..." -ForegroundColor Yellow
$tcpTest = Test-NetConnection -ComputerName $LdapServer -Port $Port
if ($tcpTest.TcpTestSucceeded) {
    Write-Host "✓ TCP connection successful" -ForegroundColor Green
} else {
    Write-Host "✗ TCP connection failed" -ForegroundColor Red
    exit 1
}

# Test 2: TLS/SSL Certificate and Protocol verification
Write-Host "`nTesting TLS/SSL connection and certificate..." -ForegroundColor Yellow
try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient($LdapServer, $Port)
    $sslStream = New-Object System.Net.Security.SslStream($tcpClient.GetStream())
    $sslStream.AuthenticateAsClient($LdapServer)
    
    Write-Host "✓ TLS/SSL connection established" -ForegroundColor Green
    Write-Host "  Protocol: $($sslStream.SslProtocol)" -ForegroundColor Cyan
    Write-Host "  Cipher Algorithm: $($sslStream.CipherAlgorithm)" -ForegroundColor Cyan
    Write-Host "  Hash Algorithm: $($sslStream.HashAlgorithm)" -ForegroundColor Cyan
    Write-Host "  Key Exchange: $($sslStream.KeyExchangeAlgorithm)" -ForegroundColor Cyan
    
    # Certificate details
    $cert = $sslStream.RemoteCertificate
    if ($cert) {
        $cert2 = New-Object System.Security.Cryptography.X509Certificates.X509Certificate2($cert)
        Write-Host "  Certificate Subject: $($cert2.Subject)" -ForegroundColor Cyan
        Write-Host "  Certificate Issuer: $($cert2.Issuer)" -ForegroundColor Cyan
        Write-Host "  Certificate Valid From: $($cert2.NotBefore)" -ForegroundColor Cyan
        Write-Host "  Certificate Valid To: $($cert2.NotAfter)" -ForegroundColor Cyan
        Write-Host "  Certificate Thumbprint: $($cert2.Thumbprint)" -ForegroundColor Cyan
        
        # Check if certificate is still valid
        $now = Get-Date
        if ($now -gt $cert2.NotAfter) {
            Write-Host "  ⚠ Certificate has expired!" -ForegroundColor Red
        } elseif ($now.AddDays(30) -gt $cert2.NotAfter) {
            Write-Host "  ⚠ Certificate expires within 30 days!" -ForegroundColor Yellow
        } else {
            Write-Host "  ✓ Certificate is valid" -ForegroundColor Green
        }
    }
    
    $sslStream.Close()
    $tcpClient.Close()
} catch {
    Write-Host "✗ TLS/SSL connection failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: LDAPS authentication test
Write-Host "`nTesting LDAPS authentication..." -ForegroundColor Yellow
try {
    # Create LDAP connection string
    $ldapPath = "LDAPS://$LdapServer/$Domain"
    
    # Prompt for password securely
    $credential = Get-Credential -UserName $Username -Message "Enter LDAP credentials"
    
    # Create DirectoryEntry object
    $directoryEntry = New-Object System.DirectoryServices.DirectoryEntry($ldapPath, $credential.UserName, $credential.GetNetworkCredential().Password)
    
    # Test the connection by accessing a property
    $name = $directoryEntry.Name
    Write-Host "✓ LDAPS authentication successful" -ForegroundColor Green
    Write-Host "Connected to: $name" -ForegroundColor Cyan
    
    # Optional: Simple search test
    $searcher = New-Object System.DirectoryServices.DirectorySearcher($directoryEntry)
    $searcher.Filter = "(objectClass=user)"
    $searcher.PageSize = 1
    $result = $searcher.FindOne()
    
    if ($result) {
        Write-Host "✓ LDAPS search test successful" -ForegroundColor Green
    }
    
} catch {
    Write-Host "✗ LDAPS authentication failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nLDAPS test completed." -ForegroundColor Cyan