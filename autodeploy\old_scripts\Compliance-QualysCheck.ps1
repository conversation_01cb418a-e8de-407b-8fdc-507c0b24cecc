#Requires -module CredentialManager
param (
  [int]$runtimes = 1,
  [switch]$Getjob = $true,
  [switch]$log = $true
)
begin {
  $stateObj = [ordered]@{JobID="";State="";QScanJob="";QComplianceJob="";QScanFile="";QComplianceFile="";QScanStatus=0;QComplianceStatus=0;startDate="";endDate="";lastupdated="";RetryCount=0;ResultState=1; Data=""}
  $DebugPreference = [System.Management.Automation.ActionPreference]::Continue
  $DebugPreference |Write-Host
  $DB = "MariaDB"
  $DBtableTemplate = [ordered]@{Server="";Database="";Port="";User="";Password="";Credential="";Driver="";DLL="";Option=""}
  $dbTable = @{}
  $dbObj = New-Object psobject -Property $DBtableTemplate
  $dbobj.Database = (get-location).Path + "\qualys.db"
  $dbobj.DLL = ".\dlls\sqlite\System.Data.SQLite.dll"
  $dbTable.Add("SQLite",$dbobj)
  $dbObj = New-Object psobject -Property $DBtableTemplate
  $dbobj.Database = "vulnscanprd"
  #$dbobj.Server = "srv009485.mud.internal.co.za"
  $dbobj.Server = "***********"
  $dbobj.Credential = Get-StoredCredential -Target Mariadb
  $dbObj.Driver = "{MariaDB ODBC 3.1 Driver}"
  $dbobj.Option = 67108867
  $dbTable.Add("MariaDB",$dbobj)
  $config = New-Object psobject -Property @{smtp_server = "mail.sanlam.co.za";smtp_from = "<EMAIL>";smtp_to="<EMAIL>"} # critical defaults

  $ActiveJobs = @{}
  $LogState = @{}
  $resultState = @{}
  $dbConnection = $NULL
  $QReportLocation = "\\SRV005879\Reports\Vulnerabilities\Nessus_Vulnerabilities"
  $DateTimeFormat = "yyyy-MM-dd HH:mm:ss"
  Import-Module CredentialManager
  $QualysURIs = @{}
  $QualysURIs.auth = "https://autoscan.sanlam.co.za/users/authenticate"
  $QualysURIs.jobs = "https://autoscan.sanlam.co.za/scan/getjobs"
  $QualysURIs.scanip = "https://autoscan.sanlam.co.za/scan/scanip"
  $QualysCreds = Get-StoredCredential -Target qualys-bcxwintel
  $QualysHeader = @{'Content-Type'='application/json';"Accept"='application/json'}
  $email = '<EMAIL>'
  $PolicyDoc = [ordered]@{emailaddress=$email;IPToScan="";Scanner="";ScanPolicy="";ScanBaseline="";ScanType=""}
  $AutoAPI = @{baseURI="http://autodeployprd.sanlam.co.za:5000";Headers=@{"Content-Type"="application/json";Accept="application/json"};Method="POST"} #
  [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12
  function connectdb {
    if ($null -eq $script:DBconnection) {
      switch ($db) {
        "SQLite" {
          try {
            [reflection.assembly]::LoadFrom((get-item $dbTable[$db].dll).FullName) |Out-Null
          }
          catch {
            [reflection.assembly]::UnsafeLoadFrom((get-item $dbTable[$db].dll).FullName) |Out-Null
          }
          $script:DBconnection = New-Object System.Data.SQLite.SQLiteConnection
          $script:DBconnection.ConnectionString = ("data source={0}" -f $dbTable[$db].Database)
        }
        "MariadB" {
          $script:DBconnection = New-Object System.Data.Odbc.OdbcConnection
          $script:DBconnection.ConnectionString = "DRIVER={0};Server = {1};Database = {2};UID = {3};PWD = ***;Option = {5}" -f $dbTable[$db].Driver,$dbTable[$db].Server,$dbTable[$db].Database,$dbTable[$db].Credential.UserName,$dbTable[$db].Credential.GetNetworkCredential().Password,$dbTable[$db].Option
        }
      }
    }
  }
  function opendb {
    $success = $false
    if ($null -eq $DBconnection) {
      connectdb
    }
    if ($DBconnection.State -eq "Closed") {
      try {
        $DBconnection.open()
        $success = $true
      }
      catch {
        Log -jobID "OpenDB" -LogState $logstate["Emergency"] -message "Unable to sign in to database"
      }
    }
    return $success
  }
  function closedb {
    if ($DBconnection.State -ne "Closed") {
      $DBconnection.close()
    }
  }
  function getConfig {
    $cfg = New-Object psobject
    dbquery "SELECT * FROM Config" |ForEach-Object {$cfg |Add-Member -MemberType NoteProperty -name $_.key -Value $_.value}
    return $cfg
  }
  function createTables {
    $data = dbquery "SELECT * from Config"
    if ([System.DBNull]::Value.Equals($data)) {
      switch ($DB) {
        "SQLite" { $dbQuery= @'
CREATE TABLE "Config" ("key" TEXT UNIQUE,"value" TEXT, PRIMARY KEY("key"));
CREATE TABLE States ("ID" INTEGER UNIQUE, "State" TEXT, PRIMARY KEY("ID" AUTOINCREMENT));
CREATE TABLE ScanStates ("ID" INTEGER UNIQUE, ScanState TEXT, PRIMARY KEY("ID" AUTOINCREMENT));
CREATE TABLE LogState ("ID" INTEGER UNIQUE, LogState TEXT, PRIMARY KEY("ID" AUTOINCREMENT));
CREATE TABLE ResultStates ("ID" INTEGER UNIQUE, ResultState TEXT, PRIMARY KEY("ID" AUTOINCREMENT));
CREATE TABLE Log ("ID" INTEGER UNIQUE, JobID TEXT, LogTime TEXT, LogState INTEGER, JobState INTEGER, Message TEXT, PRIMARY KEY("ID" AUTOINCREMENT));
CREATE TABLE "Jobs" ("JobID" TEXT UNIQUE, "StartDate" TEXT, "EndDate" TEXT, "State" INTEGER, "QScanJob" TEXT, "QComplianceJob" TEXT, "QScanFile" TEXT, "QComplianceFile" TEXT, "QScanStatus" INTEGER, "QComplianceStatus" INTEGER,RetryCount INTEGER,ResultState INTEGER, "JobData" TEXT, PRIMARY KEY("JobID"));
INSERT INTO States (ID,State) VALUES (1,"CheckActiveScan"),(2,"CheckFolder"),(3,"SubmitScan"),(4,"DetectResult"),(5,"RetrieveResult"),(6,"LogResults"),(7,"CheckFixes"),(8,"UpdateJob"),(9,"Complete");
INSERT INTO Config (key,value) VALUES ("version","0.1.0"),("datetimeformat","yyyy/MM/dd HH:mm:ss),("reportpath","\\\\SRV005879\\Reports\\Vulnerabilities\\Nessus_Vulnerabilities");
INSERT INTO ResultStates (Resultstate) VALUES (`Complete`),(`FailQFiles`),(`FailQID`);
INSERT INTO LogState (LogState) VALUES ('Emergency'),('Alert'),('Critical'),('Error'),('Warning'),('Notice'),('Informational'),('Debug');
INSERT INTO ScanState (ScanState) VALUES ('Already Logged'),('Logged After Request'),('Submitted'),('In Progress'),('Completed');
CREATE VIEW v_jobs AS SELECT jobs.JobID, jobs.StartDate, jobs.EndDate, States.State, jobs.QScanJob, jobs.QComplianceJob, jobs.QScanFile, jobs.QComplianceFile, jobs.QScanStatus, jobs.QComplianceStatus, jobs.RetryCount, ResultStates.ResultState FROM Jobs INNER JOIN States ON Jobs.State = States.ID INNER JOIN Jobs.ResultState = ResultStates.ID;
'@
        }
        "MariaDB" { $dbQuery= @'
CREATE TABLE Config (`key` varchar(20) unique primary key, `value` varchar(255));
CREATE TABLE States (ID tinyint unsigned primary key, State VARCHAR(25));
CREATE TABLE ScanStates (ID tinyint unsigned primary key, ScanState VARCHAR(25));
CREATE TABLE ResultStates (ID tinyint unsigned primary key ,ResultState varchar(50));
CREATE TABLE LogState (ID smallint(5) unsigned primary key, LogState varchar (25));
CREATE TABLE Log (ID bigint(20) unsigned auto_increment primary key, JobID varchar(20), LogTime datetime, LogState smallint(5), JobState smallint(5), Message varchar(100));
CREATE TABLE Jobs (JobID varchar(20) unique primary key, StartDate datetime, EndDate datetime, LastUpdated datetime, State tinyint unsigned, QScanJob varchar(50), QComplianceJob varchar(50), QScanFile varchar(100), QComplianceFile varchar(100), QScanStatus tinyint(3) unsigned, QComplianceStatus tinyint(3) unsigned, RetryCount int(10) unsigned, ResultState tinyint(3) unsigned, JobData varchar(8192), QScanStart datetime, QScanEnd datetime, QComplianceStart datetime, QComplianceEnd datetime, QScanJobID int(10) unsigned, QComplianceJobID int(10) unsigned);
INSERT INTO States (ID,State) VALUES (0,"Undefined"),(1,"CheckActiveScan"),(2,"CheckFolder"),(3,"SubmitScan"),(4,"DetectResult"),(5,"RetrieveResult"),(6,"LogResults"),(7,"CheckFixes"),(8,"UpdateJob"),(9,"Complete");
INSERT INTO Config (`key`,`value`) VALUES ("version","0.1.0"),("datetimeformat","yyyy/MM/dd HH:mm:ss"),("reportpath","\\\\SRV005879\\Reports\\Vulnerabilities\\Nessus_Vulnerabilities"),("smtp_server","mail.sanlam.co.za"),("smtp_from","<EMAIL>"),("smtp_recipient","<EMAIL>");
INSERT INTO LogState (ID,LogState) VALUES (1,'Emergency'),(2,'Alert'),(3,'Critical'),(4,'Error'),(5,'Warning'),(6,'Notice'),(7,'Informational'),(8,'Debug');
INSERT INTO ScanStates (ID,ScanState) VALUES (0,'Initial'),(1,'Already Logged'),(2,'Logged After Request'),(3,'Submitted'),(4,'In Progress'),(5,'Completed');
INSERT INTO ResultStates (ID,`Resultstate`) VALUES (1,"Complete"),(2,"FailQFiles"),(3,"FailQID");
ALTER TABLE Jobs ADD CONSTRAINT `FK_Jobs_ResultState` FOREIGN KEY (`ResultState`) REFERENCES ResultStates (`ID`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE Jobs ADD CONSTRAINT `FK_Jobs_QScanStatus` FOREIGN KEY (`QScanStatus`) REFERENCES ScanStates (`ID`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE Jobs ADD CONSTRAINT `FK_Jobs_QComplianceStatus` FOREIGN KEY (`QComplianceStatus`) REFERENCES ScanStates(`ID`) ON DELETE RESTRICT ON UPDATE RESTRICT;
CREATE VIEW v_jobs AS SELECT Jobs.JobID, Jobs.StartDate, Jobs.EndDate, States.State, Jobs.QScanJob, Jobs.QComplianceJob, Jobs.QScanFile,Jobs.QComplianceFile, Jobs.QScanStatus, Jobs.QComplianceStatus, Jobs.RetryCount, ResultStates.ResultState FROM Jobs INNER JOIN States on Jobs.State = States.ID INNER JOIN ResultStates on Jobs.ResultState = ResultStates.ID;
CREATE VIEW v_log AS SELECT Log.ID, Log.JobID, Log.LogTime, LogState.LogState, States.State, Log.Message FROM Log INNER JOIN LogState ON Log.LogState = LogState.ID INNER JOIN States ON Log.JobState = States.ID ORDER BY ID;
'@
        }
      }
      dbinsert $dbQuery
    }
  }
  function dbquery ([string]$query) {
    $data = New-Object System.Data.DataSet
    $dbCommand = $script:DBconnection.CreateCommand()
    $dbCommand.CommandType= [System.Data.CommandType]::Text
    $dbCommand.CommandText=$query
    try {
      switch ($db) {
        "SQLite" {
          $dbAdapter = New-Object -TypeName System.Data.SQLite.SQLiteDataAdapter $dbCommand
        }
        "MariaDB" {
          $dbAdapter = New-Object -TypeName System.Data.Odbc.OdbcDataAdapter($dbCommand)
        }
      }
     [void]$dbAdapter.Fill($data)
     return $data.Tables[0].rows
    }
    catch {
      return [System.DBNull]::Value
    }
    finally {
      $dbAdapter.Dispose()
      $dbCommand.Dispose()
      $data.Dispose()
    }
  }

  function dbInsert ([string]$query) {
    $dbCommand = $script:DBconnection.CreateCommand()
    $dbCommand.CommandText = $query
    [void]$dbCommand.ExecuteNonQuery()
    $dbCommand.Dispose()
  }
  function apiCreateUser { #Use once
    $creds = Get-StoredCredential -Target apiPortal
    $body = [ordered]@{username=$creds.UserName;password=$creds.GetNetworkCredential().Password;email="<EMAIL>";fullname="Andrew Smith";description="Devops Consultant"}
    $addURI = $AutoAPI.baseURI+"/user/add"
    try {
      $result = Invoke-RestMethod -Uri $addURI -Method $AutoAPI.Method -Body ($body |ConvertTo-Json)
    }
    catch {
      $error[0].Exception.Message
    }
  }
  function GetAPIToken {
    $uri = $AutoAPI.baseURI+"/token"
    $creds = Get-StoredCredential -Target apiPortal
    $body = [ordered]@{username=$creds.UserName;password=$creds.GetNetworkCredential().password}
    try {
      $tokendata = Invoke-RestMethod -uri $uri -Method POST -Headers $AutoAPI.Headers -body ($body |convertto-json)
      $AutoAPI["Headers"].Add("Authorization",("Bearer "+$tokendata.token))
      return $true
    }
    catch {
      Log -jobID "GetAPIToken" -LogState $logstate["Critical"] -message "Unable to retrieve Qualys API Token"
      return $false
    }
  }
  function GetNextJob {
    if ($AutoAPI["Headers"].keys -contains "Authorization") { #Authenticated
      $uri = $AutoAPI.baseURI+'/getnextjob'
      $body = [ordered]@{"request_jobid"="";"job_stage"="COMPLIANCE";"job_status"="PENDING";"hosting_platform"=""}
      try {
        if ($getjob) {
          $qResult = Invoke-RestMethod -uri $uri -Method POST -Header $AutoAPI.Headers -Body ($body |ConvertTo-Json) -TimeoutSec 10
          if ($qResult.Success -eq $false) {
            "No new jobs" |Write-Debug
            Log -jobID "GetNextJob" -LogState $logstate["Informational"] -message "No new jobs"
            $qResult = $null
          }
        } else {
          $qResult = $null #Job retrieval not enabled
        }
      }
      catch {
        Log -jobID "GetNextJob" -LogState $logstate["Critical"] -message "Unable to retrieve next job"
        "Unable to retrieve next job" |Write-Debug
        $qResult = $null
      }
      While ($null -ne $qResult) {
        "Got new job {0}" -f $qResult.request_jobid |Write-Debug
        $job = New-Object psobject -Property $stateObj
        $job.jobid = $qResult.request_jobid
        $job.State = 1
        $job.StartDate = (Get-Date).ToString($DateTimeFormat)
        $job.LastUpdated = $job.StartDate
        $job.QScanStatus = 0
        $job.QComplianceStatus = 0
        $job.RetryCount = 0
        $job.ResultState = 1 #assume success - failures will change
        if(![string]::IsNullOrEmpty($qResult.t2_datastore)) {
          $qResult.t2_datastore = $qResult.t2_datastore.replace("\","\\")
        }
        if(![string]::IsNullOrEmpty($qResult.t3_datastore)) {
          $qResult.t3_datastore = $qResult.t3_datastore.replace("\","\\")
        }
        $job.Data = ($qResult |ConvertTo-Json).Replace('"','""') #Escape quotes
        #Add Job to DB
        try {
          dbInsert ('INSERT INTO Jobs (JobID,State,StartDate,LastUpdated,QScanStatus,QComplianceStatus,RetryCount,ResultState,JobData) VALUES ("{0}","{1}","{2}","{3}","***","{5}","{6}","{7}","{8}")' -f $job.jobid,$job.state,$job.startDate,$job.startDate,$job.QScanStatus,$job.QComplianceStatus,$job.RetryCount,$job.ResultState,$job.data)
        }
        catch {
          If ($Error[0].exception.message -like "*Duplicate Entry*for key 'PRIMARY'*") {
            log -jobID $job.jobid -LogState 4 -JobState 0 -message "Unable to add record to ActiveJobs - Duplicate record"
          }
        }
        try {
          $qResult = Invoke-RestMethod -uri $uri -Method POST -Header $AutoAPI.Headers -Body ($body |ConvertTo-Json)
          if ($qResult.Success -eq $false) {
            "No more new jobs" |Write-Debug
            $qResult = $null
          }
          #$qResult = $null #API Query (Loads more jobs)
        }
        catch {
          $qResult = $null
        }
      }
    } else {
      log -jobID "GetNextJob" -LogState $logstate["Error"] -message "No API Token available"
    }
  }
  function submitFiles ([string]$jobid, [string]$qscan, [string]$qcompliance, [int]$jobstate) {
    $success = $false
    if ($AutoAPI["Headers"].keys -contains "Authorization") {
      $uri = $AutoAPI.baseURI+'/updatecompliance'
      $body = [ordered]@{"request_jobid"=$jobid;"scan_os_vulnerability"=$qscan;"scan_os_baseline"=$qcompliance}
      try {
        $result = Invoke-RestMethod -Uri $uri -Method POST -Headers $AutoAPI["Headers"] -Body ($body |ConvertTo-Json)
        #$result = dbInsert ('UPDATE deploydevdb.buildjobs SET scan_os_baseline = "{0}", scan_os_vulnerability = "{1}" WHERE request_jobid = "{2}"' -f $qcompliance,$qscan,$jobid) #bypass
        if ($result.update_ok) {
          $success = $true
        } elseif (![System.DBNull]::Value.Equals($result) -or $null -ne $result) { #bypass
          $success = $true
        }
      }
      catch {
        log -jobID $jobid -LogState $logstate["Critical"] -JobState $jobstate -message $error[0].Exception.InnerException
        "Unable to submit job results for $jobid" |Write-Debug
        success = $false
      }
    }
    return $success
  }
  function submitClose ([string]$jobID,[int]$jobstate,[int]$resultstate) {
    $closeSuccess = $false
    switch ($resultstate) {
      2 {
        $job_stage = "COMPLIANCE"
        $job_status = "COMPLETED"
        $log_type = "ISSUE"
        $job_comment = "Qualys files failed to arrive"
        $issue_type = "Failue"
        $issue_ref = "QFF"
        $success = $false
      }
      4 {
        $job_stage = "COMPLIANCE"
        $job_status = "COMPLETED"
        $log_type = "ISSUE"
        $job_comment = "No Qualys ID"
        $issue_type = "Failue"
        $issue_ref = "QFI"
        $success = $false
      }
      Default {
        $job_stage = "COMPLIANCE"
        $job_status = "COMPLETED"
        $log_type = "INFO"
        $job_comment = "Vulnerability scan completed"
        $issue_type = ""
        $issue_ref = ""
        $success = $true
      }
    }
    if ($AutoAPI["Headers"].keys -contains "Authorization") {
      $uri = $AutoAPI.baseURI+'/updatejob'
      $body = [ordered]@{"request_jobid"=$jobid;requested_by="autodeploysvc";"job_stage"=$job_stage;"job_status"=$job_status;"job_comment"=$job_comment;"log_type"=$log_type;"issue_ref"=$issue_ref;"issue_type"=$issue_type;success=$success}
      try {
        $result = Invoke-RestMethod -Uri $uri -Method POST -Headers $AutoAPI["Headers"] -Body ($body |convertto-json)
        if ($result.update_result -eq "COMPLIANCE Updated to QUALITY!") {
          $closeSuccess = $true
        }
      }
      catch {
        log -jobID $jobid -LogState $logstate["Critical"] -JobState $jobstate -message $error[0].Exception.InnerException
        "Unable to submit job signoff for $jobid" |Write-Debug
        $closeSuccess = $false
      }
    }
    return $closeSuccess
   
  }
  function GetActiveJobs {
    $joblist = dbquery 'SELECT * FROM Jobs WHERE State < (SELECT MAX(ID) FROM States)'
    foreach ($job in $joblist) {
      $script:ActiveJobs.Add($job.jobid,$job)
    }
  }
  function getQtoken {
    $body = @{
      username = $QualysCreds.UserName
      password = $QualysCreds.GetNetworkCredential().Password
    }
    try {
      $result = Invoke-RestMethod -Uri $QualysURIs.auth -Headers $QualysHeader -Method Post -Body ($body |convertto-json)
      if (![string]::IsNullOrEmpty($result.token)) {
        $QualysHeader.Add('Authorization', ('Bearer ' + $result.token))
        Return $true
      } else {
        return $false
      }
    }
    Catch {
      log -jobID "QualisAuth" -LogState $logstate["Critical"] -message $error[0].Exception.InnerException.Message
      "Unable to get Qualis Auth Token" |Write-Debug
      $success = $false
    }
  }
  function getQjobs {
    try {
      $result = Invoke-RestMethod -Uri $QualysURIs.jobs -Headers $QualysHeader -Method GET 
      return $result
    }
    catch {
      log -jobID "getQualisJob" -LogState $logstate["Error"] -message $error[0].Exception.InnerException.Message
      "Unable to get Qualis Jobs" |Write-Debug
      return $null
    }
  }
  function submitQJob ([psobject]$jobObj) {
    $body = ($jobObj |ConvertTo-Json).Replace("`r`n","").replace(" ","")
    try {
      $result = Invoke-RestMethod -Uri $QualysURIs.scanip -Method POST -Headers $QualysHeader -Body $body
      return $result
    }
    catch {
      log -jobID "submitQualisJob" -LogState $logstate["Error"] -message $error[0].Exception.InnerException.Message
      "Unable to submit Qualis Jobs" |Write-Debug
      return $null
    }
  }
  function changestate ([string]$JobID, [int]$state) {
    $ActiveJobs[$JobID].state = $state
    $result = dbInsert ('UPDATE Jobs SET State = {0}, LastUpdated = "{1}", RetryCount = {2} where JobID = "{3}"' -f $state, (Get-Date).ToString($DateTimeFormat), $ActiveJobs[$JobID].RetryCount, $JobID)
  }
  function changeRetry ([string]$JobID,[switch]$reset) {
    if ($reset) {
      $ActiveJobs[$JobID].RetryCount = 0
    } else {
      $ActiveJobs[$JobID].RetryCount++
    }
    dbinsert ('UPDATE Jobs SET RetryCount = {0}, LastUpdated = "{1}" WHERE JobID =  {2}' -f $ActiveJobs[$JobID].RetryCount,(Get-Date).ToString($DateTimeFormat). $JobID)
  }
  function updateResultState ([string]$jobID,[int]$ResultState) {
    $ActiveJobs[$jobID].ResultState = $ResultState
    dbinsert ('UPDATE Jobs SET ResultState = {0}, LastUpdated = "{1}" WHERE JobID =  {2}' -f $ActiveJobs[$JobID].ResultState,(Get-Date).ToString($DateTimeFormat). $JobID)
  }
  function updateQjobs {
    param (
      [parameter(mandatory = $true)][string]$JobID, 
      [string]$QScan, 
      [string]$QCompliance
    )
    if ([string]::IsNullOrEmpty($QScan)) { #preserve job name if not supplied
      $QScan = $ActiveJobs[$jobID].QScanJob
    } else {
      $ActiveJobs[$jobID].QScanJob = $QScan
    }
    if ([string]::IsNullOrEmpty($QComplianceStatus)) { #preserve job name if not supplied
      $QComplianceStatus = $ActiveJobs[$jobID].QComplianceJob
    } else {
      $ActiveJobs[$jobID].QComplianceJob = $QCompliance
    }
    dbInsert ('UPDATE Jobs SET QScanJob = "{0}", QComplianceJob = "{1}", LastUpdated = "{2}" WHERE JobID = "{3}"' -f $QScan,$QCompliance,(Get-Date).ToString($DateTimeFormat),$jobID)
  }
  function updateQstate {
    param (
      [parameter(mandatory = $true)][string]$JobID, 
      [int]$QScanStatus, 
      [int]$QComplianceStatus
    )
    if ($null -eq $QScanStatus) { #Preserve status if not supplied
      $QScanStatus = $ActiveJobs[$jobID].QScanStatus
    } else {
      $ActiveJobs[$jobID].QScanStatus = $QScanStatus
    }
    if ($null -eq $QComplianceStatus) { #Preserve status if not supplied
      $QComplianceStatus = $ActiveJobs[$jobID].QComplianceStatus
    } else {
      $ActiveJobs[$jobID].QComplianceStatus = $QComplianceStatus
    }
    dbInsert ('UPDATE Jobs SET QScanStatus = "{0}", QComplianceStatus = "{1}", LastUpdated = "{2}" WHERE JobID = "{3}"' -f $QScanStatus,$QComplianceStatus,(Get-Date).ToString($DateTimeFormat),$jobID)
  }
  function updateQtimes ([string]$jobID,[datetime]$qScanStart,[datetime]$qScanEnd,[datetime]$qComplianceStart,[datetime]$qComplianceEnd) {
    try {
      if ($null -eq $qComplianceStart) { #no compliance job - linux
        dbInsert ('UPDATE Jobs SET QScanStart = "{0}", QScanEnd = "{1}" WHERE JobID = "{2}"' -f $qScanStart.tostring($DateTimeFormat),$qScanEnd.tostring($DateTimeFormat),$JOBID)
      } elseif ($null -eq $qScanStart) { #no Scan job (unlikely)
        dbInsert ('UPDATE Jobs SET QComplianceStart = "{0}", QComplianceEnd = "{1}" WHERE JobID = "{2}"' -f $qComplianceStart.tostring($DateTimeFormat),$qComplianceEnd.tostring($DateTimeFormat),$JOBID)
      } else {
        dbInsert ('UPDATE Jobs SET QScanStart = "{0}", QScanEnd = "{1}", QComplianceStart = "{2}", QComplianceEnd = "{3}" WHERE JobID = "***"' -f $qScanStart.tostring($DateTimeFormat),$qScanEnd.tostring($DateTimeFormat),$qComplianceStart.tostring($DateTimeFormat),$qComplianceEnd.tostring($DateTimeFormat),$JOBID)
      }
    }
    catch {
      log -jobID $jobID -LogState $LogState["Error"] -message "Failed to update Scan times"
      "Failed to update Scan times" |Write-Debug
    }
  }
  function updateQfiles ([string]$JobID, [string]$QScan, [string]$QCompliance) {
    if ([string]::IsNullOrEmpty($QScan)) { #Preserve file if not specified
      $QScan = $ActiveJobs[$jobID].QScanFile
    } else {
      $ActiveJobs[$jobID].QScanFile = $QScan
    }
    if ([string]::IsNullOrEmpty($QCompliance)) { #Preserve file if not specified
      $QCompliance = $ActiveJobs[$jobID].QComplianceFile
    } else {
      $ActiveJobs[$jobID].QComplianceFile = $QCompliance
    }
    $data = $ActiveJobs[$jobID].JobData |ConvertFrom-Json
    if(![string]::IsNullOrEmpty($data.t2_datastore)) {
      $data.t2_datastore = $data.t2_datastore.replace("\","\\")
    }
    if(![string]::IsNullOrEmpty($data.t3_datastore)) {
      $data.t3_datastore = $data.t3_datastore.replace("\","\\")
    }
    $data.scan_os_baseline = $QCompliance
    $data.scan_os_vulnerability = $QScan
    $json = $data |ConvertTo-Json
    $ActiveJobs[$jobID].JobData = $json
    $json = $json.replace('"','""') #escape quotes for dbupdate
    dbInsert ('UPDATE Jobs SET QScanFile = "{0}", QComplianceFile = "{1}", JobData = "{2}", LastUpdated = "{3}" WHERE JobID = "***"' -f $QScan,$QCompliance,$json, (Get-Date).ToString($DateTimeFormat),$jobID)
  }
  function qEndJob ([string]$jobid) {
    $dt = Get-Date
    $ActiveJobs[$jobid].endDate = $dt.ToString($DateTimeFormat)
    dbInsert ('UPDATE Jobs SET endDate = "{0}", LastUpdated = "{1}" WHERE JobID = "{2}"' -f $dt.ToString($DateTimeFormat),$dt.ToString($DateTimeFormat),$jobID)
  }
  function updateSubmitTime ([string]$jobID) {
    $dt = Get-Date
    $ActiveJobs[$jobid].SubmitDate = $dt.ToString($DateTimeFormat)
    dbInsert ('UPDATE Jobs SET SubmitDate = "{0}", LastUpdated = "{1}" WHERE JobID = "{2}"' -f $dt.ToString($DateTimeFormat),$dt.ToString($DateTimeFormat),$jobID)
  }
  function setupStates {
    dbquery "SELECT LogState,ID from LogState" |ForEach-Object {$Script:LogState.Add($_.LogState,$_.ID)}
    dbquery "SELECT ResultState,ID from ResultStates" |ForEach-Object {$Script:ResultState.Add($_.ResultState,$_.ID)}
  }
  function log ([string]$jobID,[int]$LogState = 7,[int]$JobState,[String]$message) {
    if ($Logstate -lt 3) {
      Send-MailMessage -SmtpServer $script:config.smtp_server -From $script:config.smtp_from -To $script:config.smtp_recipient -Subject "Unrecoverable Error in Decomission script" -body "$message"
    }
    if ($Logstate -eq 1) {
      "{0} - {1}" -f $Logstate,$message |write-host
      Exit 1
    }
    if ($log) {
      try {
        dbInsert ('INSERT INTO Log (JobID,LogTime,LogState,JobState,Message) VALUES ("{0}","{1}",{2},{3},"***")' -f $jobID,(Get-Date).ToString($DateTimeFormat),$LogState,$JobState,$message)
      }
      catch {
        Write-debug "Logging failed"
      }
    }
  }
  "Connecting to DB" |Write-Debug
  connectdb
  if ((opendb) -eq $false) {Exit 9}
  createTables
  $config = getConfig
  setupStates
  Log -jobID "Main" -LogState $logstate["Informational"] -message "Starting run"
  #apiCreateUser
  if (!(GetAPIToken)) { exit 2 }
  "Getting Job" |Write-Debug
  GetNextJob
  "Loading Engine Data" |Write-Debug
  GetActiveJobs
  if (getQtoken) {
    if (($ActiveJobs.keys |Measure-Object).count -eq 0) {
      "No jobs to process" |Write-Debug
      Log -jobID "Main" -LogState $logstate["Informational"] -message "No jobs to process"
    }
    for ($cnt = 0; $cnt -lt $runtimes; $cnt++) {
      foreach ($job in $ActiveJobs.keys) {
        $jobspec = $ActiveJobs[$job].JobData |ConvertFrom-Json
        $jobStartTime = get-date $ActiveJobs[$job].StartDate
        $ipAddress = $jobspec.ip_address
        $QScanStatus = $ActiveJobs[$job].QScanStatus
        $QComplianceStatus = $ActiveJobs[$job].QComplianceStatus
        $Qjobs = getQjobs |Where-Object {$_.iptoscan -like $ipAddress}
        #ScanStates = 1 Already logged, 2 Logged after Request, 3 Logged by Script. 4 In progress, 5 Completed
        "Count:Job:State:OS - {0}:{1}:{2}:{3}" -f $ActiveJobs.Count,$job,$ActiveJobs[$job].state,$jobspec.os_type |Write-Debug
        switch ($ActiveJobs[$job].state) {
          1 { #Check if there is an active scan in progress
            switch ($jobspec.os_type) {
              "Windows" {
                if ($null -ne $qjobs) { #There are jobs
                  $QScan = $Qjobs |Where-Object {$_.jobid -like '*scan*' } |sort -Property JobID -Descending |select -first 1 #select latest
                  $QCompliance = $Qjobs |Where-Object {$_.jobid -like '*compliance*' } |sort -Property JobID -Descending |select -first 1 #select latest
                  if ($null -ne $QScan) { #Vulnerability scan is logged
                    if ($QScanStatus -lt 1) {$QScanStatus = 1}
                    $scantime = get-date ($QScan.requestDate+' '+$qscan.requestTime)
                    if ($scantime -ge $jobStartTime) { #Vulnerability scan initiated after job submitted
                      if ($QScanStatus -lt 2) {$QScanStatus = 2}
                      if ($QScan.state -eq "Completed") { #Vulnerability scan completed
                        $QScanStatus = 5
                      }
                    }
                }
                  if ($null -ne $QCompliance) { #Compliance scan logged
                    if ($QComplianceStatus -lt 1) {$QComplianceStatus = 1}
                    $complianceTime = get-date ($QCompliance.requestDate+' '+$QCompliance.requestTime)
                    if ($complianceTime -ge $jobStartTime) {# Compliance scan initiated after job submitted
                      if ($QComplianceStatus -lt 2) {$QComplianceStatus = 2}
                      if ($QCompliance.State -eq "Completed") { #Compliance scan completed"
                        $QComplianceStatus = 5
                      }
                    }
                  }
                  if ($QScanStatus -ne $ActiveJobs[$job].QScanStatus -or $QComplianceStatus -ne $ActiveJobs[$job].QComplianceStatus) { #Statuses have changed
                    updateQjobs -JobID $job -QScan $QScan.JobID -QCompliance $QCompliance.jobID
                    updateQState -JobID $job -QScanStatus $QScanStatus -QComplianceStatus $QComplianceStatus
                  }
                  if ($QScanStatus -gt 4 -and $QComplianceStatus -gt 4) { #Both scans have completed
                    $qss = get-date ($qScan.requestDate + ' ' + $qscan.requestTime)
                    $qse = get-date ($qScan.requestCompletionDate + ' ' + $qScan.requestCompletionTime)
                    $qcs = get-date ($qCompliance.requestDate + " " + $qCompliance.requestTime)
                    $qce = get-date ($qCompliance.requestCompletionDate + ' ' + $qCompliance.requestCompletionTime)
                    updateQtimes -JobID $job -qScanStart $qss -qScanEnd $qse -qComplianceStart $qcs -qComplianceEnd $qce
                    changestate $job 2
                  } elseif ($QScanStatus -lt 2 -or $QComplianceStatus -lt 2 ) { # one or more scans are not valid or logged
                    changestate $job 3
                  } else { #scans still in progress.
                  }
                } else {
                  changestate $job 3 # No jobs - Log new ones
                }
              }
              "Linux" {
                if ($null -ne $qjobs) { #There are jobs
                  $QScan = $Qjobs |Where-Object {$_.jobid -like '*scan*' } |sort -Property JobID -Descending |select -first 1 #select latest
                  if ($null -ne $QScan) { #Vulnerability scan is logged
                    if ($QScanStatus -lt 1) {$QScanStatus = 1}
                    $scantime = get-date ($QScan.requestDate+' '+$qscan.requestTime)
                    if ($scantime -ge $jobStartTime) { #Vulnerability scan initiated after job submitted
                      if ($QScanStatus -lt 2) {$QScanStatus = 2}
                      if ($QScan.state -eq "Completed") { #Vulnerability scan completed
                        $QScanStatus = 5
                      }
                    }
                  }
                  if ($QScanStatus -ne $ActiveJobs[$job].QScanStatus ) { #Status has changed
                     updateQjobs -JobID $job -QScan $QScan.JobID
                     $qss = get-date ($qScan.requestDate + ' ' + $qscan.requestTime)
                     $qse = get-date ($qScan.requestCompletionDate + ' ' + $qScan.requestCompletionTime)
                     updateQtimes -JobID $job -qScanStart $qss -qScanEnd $qse
                     updateQState -JobID $job -QScanStatus $QScanStatus
                  }
                  if ($QScanStatus -gt 4) { #Both scans have completed
                    changestate $job 2
                  } elseif ($QScanStatus -lt 2) { # one or more scans are not valid or logged
                    changestate $job 3
                  } else { #scans still in progress.
                  }
                } else {
                  changestate $job 3 # No jobs - Log new ones
                }
              }
            }
          }
          2 { #Check folder for report files
            if ($ActiveJobs[$job].RetryCount -ge 2) {  #Remediate
              changeRetry $job -reset
              updateResultState $job $resultState["FailQFiles"]
              changestate $job 8
            }
            Switch ($jobspec.os_type) {
              "Windows" {
                $files = Get-ChildItem ($QReportLocation+'\'+$jobspec.ip_address+'*')
                if (($files |Measure-Object).count -gt 0) { #There are files
                  $qScanJob = $ActiveJobs[$job].QScanJob.substring(0,$ActiveJobs[$job].QScanJob.Indexof("$"))
                  $qComplianceJob = $ActiveJobs[$job].QComplianceJob.substring(0,$ActiveJobs[$job].QComplianceJob.Indexof("$"))
                  $QScan = $ipAddress + '-' + $qScanJob + "-qualys.csv"
                  $QCompliance = $ipAddress + '-' + $qComplianceJob + "-compliance-qualys.csv"
                  $QScanFile = $files |Where-Object {$_.name -eq $QScan}
                  $QComplianceFile = $files |Where-Object {$_.name -eq $QCompliance}
                  $QScanOK = ((New-TimeSpan -End $QScanFile.LastWriteTime -Start $jobStartTime) -gt 0) #Report older than Job submition
                  $QComplianceOK = ((New-TimeSpan -End $QComplianceFile.LastWriteTime -Start $jobStartTime) -gt 0) #Report older than Job submition
                  if ($QScanOK -and $QComplianceOK) { #Both reports ok
                    updateQfiles -JobID $job -QScan $QScan -QCompliance $QCompliance #Save report names
                    changestate $job 6
                  } elseif ((New-TimeSpan -Start $ActiveJobs[$job].LastUpdate -End (Get-Date)).Totalminutes -ge 10){ #Stuck in this state for more than 10 minutes and report file has not been delivered yet
                    updateQstate $job 1 1
                    changeRetry $job
                    changestate $job 3
                  }
                } else { #There are no files or old files
                  changestate $job 3
                }
              }
              "Linux" {
                $files = Get-ChildItem ($QReportLocation+'\'+$jobspec.ip_address+'*')
                if (($files |Measure-Object).count -gt 0) { #There are files
                  $qScanJob = $ActiveJobs[$job].QScanJob.substring(0,$ActiveJobs[$job].QScanJob.Indexof("$"))
                  $QScan = $ipAddress + '-' + $qScanJob + "-qualys.csv"
                  $QScanFile = $files |Where-Object {$_.name -eq $QScan}
                  $QScanOK = ((New-TimeSpan -End $QScanFile.LastWriteTime -Start $jobStartTime) -gt 0) #Report older than Job submition
                  if ($QScanOK) { #Both reports ok
                    updateQfiles -JobID $job -QScan $QScan #Save report names
                    changestate $job 6
                  } elseif ((New-TimeSpan -Start $ActiveJobs[$job].LastUpdate -End (Get-Date)).Totalminutes -ge 10){ #Stuck in this state for more than 10 minutes and report file has not been delivered yet
                    updateQstate $job 1 1
                    changeRetry $job
                    changestate $job 3
                  }
                } else { #There are no files or old files
                  changestate $job 3
                }
              }
            }
          }
          3 { #Submit check to Qualys
            $policyDoc.IPToScan = $ipAddress
            $PolicyDoc.Scanner = switch -Wildcard ($jobspec.datacenter) {
              "BDC*" { "Sanlam_BDC_Scanner" }
              "CDC*" { "Sanlam_CDC_Scanner" }
              "ALDC*" { "Sanlam_ALDC_Scanner" }
              Default { "Sanlam_CDC_Scanner" }
            }
            Switch ($jobspec.os_type) {
              "Windows" {
                $PolicyDoc.ScanPolicy = "Sanlam_Main_Scan"
                $PolicyDoc.ScanBaseline = switch ($jobspec.os_version) {
                  "Windows Server 2016" { "Sanlam_Windows_2016_Member_Server_AUTO" }
                  "Windows Server 2019" { "Sanlam_Windows_2019_Member_Server_AUTO" }
				          "Windows Server 2022" { "Sanlam_Windows_2022_Member_Server_AUTO" }
                }
                if ($QScanStatus -lt 2) {
                  $PolicyDoc.ScanType= "Vulnerability"
                  $Scan_vulnerabilities = SubmitQJob $PolicyDoc
                  if ($Scan_vulnerabilities -eq "Job created") {
                    $QScanStatus = 3
                  } else {
                    log $job $LogState["Error"] $ActiveJobs[$job].state "Error Submitting Vulnerability Scan"
                  }
                }
                if ($QComplianceStatus -lt 2) {
                  $PolicyDoc.ScanType= "Compliance"
                  $Scan_vulnerabilities = SubmitQJob $PolicyDoc
                  if ($Scan_vulnerabilities -eq "Job created") {
                    $QComplianceStatus = 3
                  } else {
                    log $job $LogState["Error"] $ActiveJobs[$job].state "Error Submitting Compliance Scan"
                  }
                }
              }
              "Linux" {
                $PolicyDoc.ScanPolicy = "Sanlam_Linux_Scan"
                if ($QScanStatus -lt 2) {
                  $PolicyDoc.ScanType= "Vulnerability"
                  $Scan_vulnerabilities = SubmitQJob $PolicyDoc
                  if ($Scan_vulnerabilities -eq "Job created") {
                    $QScanStatus = 3
                  } else {
                    log $job $LogState["Error"] $ActiveJobs[$job].state "Error Submitting Vulnerability Scan"
                  }
                }
              }
            }
            if ($QScanStatus -ne $ActiveJobs[$job].QScanStatus -or $QComplianceStatus -ne $ActiveJobs[$job].QComplianceStatus) { #Statuses have changed
              updateQState -JobID $job -QScanStatus $QScanStatus -QComplianceStatus $QComplianceStatus
            }
            switch ($jobspec.os_type) {
              "Windows" { # Windows has 2 jobs
                if ($QScanStatus -eq 3 -and $QComplianceStatus -eq 3) {
                  changestate $job 4
                }
              }
              "Linux" { #Linux has 1 job
                if ($QScanStatus -eq 3) {
                  changestate $job 4
                }
              }
            }
          }
          4 { #Validate Jobs
            if ($ActiveJobs[$job].RetryCount -ge 2) {  #Remediate
              changeRetry $job -reset
              updateResultState $job $resultState["FailQID"]
              changestate $job 8
            }
            if ($null -ne $qjobs) { #There are jobs
              $QScan = $Qjobs |Where-Object {$_.jobid -like '*scan*' } |sort -Property JobID -Descending |select -first 1 #select latest
              $QCompliance = $Qjobs |Where-Object {$_.jobid -like '*compliance*' } |sort -Property JobID -Descending |select -first 1 #select latest
            }
            switch ($jobspec.os_type) {
              "Windows" {
                if (![string]::IsNullOrEmpty($QScan.jobID)) {$QScanStatus = 4} #Submitted Job has JobId
                if (![string]::IsNullOrEmpty($QCompliance.jobID)) {$QComplianceStatus = 4} #Submitted Job has JobId
                if ($QScanStatus -ne $ActiveJobs[$job].QScanStatus -or $QComplianceStatus -ne $ActiveJobs[$job].QComplianceStatus) { #Statuses have changed
                  updateQState -JobID $job -QScanStatus $QScanStatus -QComplianceStatus $QComplianceStatus
                }
                if ($QScanStatus -eq 4 -and $QComplianceStatus -eq 4) {
                  updateQjobs -JobID $job -QScan $QScan.JobID -QCompliance $QCompliance.jobID
                  changestate $job 1
                } elseif ((New-TimeSpan -Start $ActiveJobs[$job].LastUpdated -End (Get-Date)).Totalminutes -ge 10){ #Stuck in this state for more than 10 minutes and no job id
                  updateQstate $job 1 1
                  changeRetry $job
                  changestate $job 3
                }
              }
              "Linux" {
                if (![string]::IsNullOrEmpty($QScan.jobID)) {$QScanStatus = 4} #Submitted Job has JobId
                if ($QScanStatus -ne $ActiveJobs[$job].QScanStatus ) { #Status has changed
                  updateQState -JobID $job -QScanStatus $QScanStatus
                }
                if ($QScanStatus -eq 4) {
                  changestate $job 1
                } elseif ((New-TimeSpan -Start $ActiveJobs[$job].LastUpdated -End (Get-Date)).Totalminutes -ge 10){ #Stuck in this state for more than 10 minutes and no job id
                  updateQstate $job 1 1
                  changeRetry $job
                  changestate $job 3
                }
              }
            }
          }
          5 { #Retrieve Result
          }
          6 { #Log Result
            #Log all fails in scan files if required and jump to 7
            $submitresult = submitfiles -jobid $job -qScan $activejobs[$job].QScanFile -qcompliance $activejobs[$job].QComplianceFile -jobstate $ActiveJobs[$job].state
            if ($submitresult) {
              changestate $job 8 #If no failure reporting
            }
          }
          7 { #Check Fixes
            changestate $job 8
          }
          8 { #Update Job
            #mark job as complete in API
            $submitResult = submitClose $job $ActiveJobs[$job].state $ActiveJobs[$job].resultstate
            qEndJob $job
            if ($submitresult) {
              changestate $job 9
            }
          }
          9 { #Complete
          }
        }
      }
    }
  } else {
    #Log failure to connect to Qualys
  }
 closedb
 $DebugPreference = [System.Management.Automation.ActionPreference]::SilentlyContinue
}