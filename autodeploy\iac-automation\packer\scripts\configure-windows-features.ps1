# Configure Windows Features and Roles
# This script configures common Windows features based on your existing deployment patterns

Write-Host "Configuring Windows Features and Roles..."

# Enable common Windows features that are typically needed
$featuresToEnable = @(
    "IIS-WebServerRole",
    "IIS-WebServer",
    "IIS-CommonHttpFeatures",
    "IIS-HttpErrors",
    "IIS-HttpLogging",
    "IIS-RequestMonitor",
    "IIS-StaticContent",
    "IIS-DefaultDocument",
    "IIS-DirectoryBrowsing",
    "IIS-ASPNET45",
    "IIS-NetFxExtensibility45",
    "IIS-ISAPIExtensions",
    "IIS-ISAPIFilter",
    "IIS-ManagementConsole",
    "NetFx4Extended-ASPNET45",
    "WAS-WindowsActivationService",
    "WAS-ProcessModel",
    "WAS-NetFxEnvironment",
    "WAS-ConfigurationAPI"
)

# Install .NET Framework 4.8 (if not already installed)
Write-Host "Checking .NET Framework version..."
$dotNetVersion = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue

if ($dotNetVersion -and $dotNetVersion.Release -ge 528040) {
    Write-Host ".NET Framework 4.8 or later is already installed."
} else {
    Write-Host "Installing .NET Framework 4.8..."
    try {
        $dotNetUrl = "https://download.microsoft.com/download/2/d/1/2d1c5e1c-72d3-4b4c-b47d-3f2f3c644d84/ndp48-web.exe"
        $dotNetPath = "$env:TEMP\ndp48-web.exe"
        
        Invoke-WebRequest -Uri $dotNetUrl -OutFile $dotNetPath
        Start-Process -FilePath $dotNetPath -ArgumentList "/quiet" -Wait
        Remove-Item $dotNetPath -Force
        
        Write-Host ".NET Framework 4.8 installation completed."
    } catch {
        Write-Host "Failed to install .NET Framework 4.8: $($_.Exception.Message)"
    }
}

# Enable Windows features
Write-Host "Enabling Windows features..."
foreach ($feature in $featuresToEnable) {
    try {
        $result = Enable-WindowsOptionalFeature -Online -FeatureName $feature -All -NoRestart
        if ($result.RestartNeeded) {
            Write-Host "Feature $feature enabled (restart required)"
        } else {
            Write-Host "Feature $feature enabled"
        }
    } catch {
        Write-Host "Failed to enable feature $feature : $($_.Exception.Message)"
    }
}

# Install common server roles using PowerShell
Write-Host "Installing server roles..."

$rolesToInstall = @(
    "Web-Server",
    "Web-Common-Http",
    "Web-Static-Content",
    "Web-Default-Doc",
    "Web-Dir-Browsing",
    "Web-Http-Errors",
    "Web-Http-Logging",
    "Web-Request-Monitor",
    "Web-Asp-Net45",
    "Web-Net-Ext45",
    "Web-ISAPI-Ext",
    "Web-ISAPI-Filter",
    "Web-Mgmt-Console",
    "RSAT-AD-PowerShell",
    "RSAT-DNS-Server"
)

foreach ($role in $rolesToInstall) {
    try {
        $result = Install-WindowsFeature -Name $role -IncludeManagementTools
        if ($result.Success) {
            Write-Host "Role $role installed successfully"
        } else {
            Write-Host "Failed to install role $role"
        }
    } catch {
        Write-Host "Error installing role $role : $($_.Exception.Message)"
    }
}

# Configure PowerShell execution policy
Write-Host "Configuring PowerShell execution policy..."
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine -Force

# Enable PowerShell remoting (if not already enabled)
Write-Host "Ensuring PowerShell remoting is enabled..."
try {
    Enable-PSRemoting -Force -SkipNetworkProfileCheck
    Write-Host "PowerShell remoting enabled."
} catch {
    Write-Host "PowerShell remoting configuration failed: $($_.Exception.Message)"
}

# Configure Windows Remote Management
Write-Host "Configuring Windows Remote Management..."
try {
    winrm set winrm/config/service '@{AllowUnencrypted="false"}'
    winrm set winrm/config/service/auth '@{Basic="true"}'
    winrm set winrm/config/service/auth '@{Windows="true"}'
    winrm set winrm/config/winrs '@{MaxMemoryPerShellMB="1024"}'
    Write-Host "WinRM configured successfully."
} catch {
    Write-Host "WinRM configuration failed: $($_.Exception.Message)"
}

# Configure Windows Firewall for common services
Write-Host "Configuring Windows Firewall..."
try {
    # Enable RDP
    Enable-NetFirewallRule -DisplayGroup "Remote Desktop"
    
    # Enable WinRM
    Enable-NetFirewallRule -DisplayName "Windows Remote Management (HTTP-In)"
    Enable-NetFirewallRule -DisplayName "Windows Remote Management (HTTPS-In)"
    
    # Enable File and Printer Sharing (for domain environments)
    Enable-NetFirewallRule -DisplayGroup "File and Printer Sharing"
    
    Write-Host "Firewall rules configured."
} catch {
    Write-Host "Firewall configuration failed: $($_.Exception.Message)"
}

# Set timezone to UTC (can be changed later via deployment scripts)
Write-Host "Setting timezone to UTC..."
try {
    Set-TimeZone -Id "UTC"
    Write-Host "Timezone set to UTC."
} catch {
    Write-Host "Failed to set timezone: $($_.Exception.Message)"
}

Write-Host "Windows Features and Roles configuration completed."
