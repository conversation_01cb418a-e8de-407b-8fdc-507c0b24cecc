# EC2 Deployment Scripts

This folder contains PowerShell scripts for deploying and managing EC2 instances using the custom AMIs created by ImageBuilder.

## Scripts Overview

### `generate-user-data.ps1`
**Purpose**: Generates user data scripts for business-specific domain joining

**Usage**:
```powershell
.\generate-user-data.ps1 -BusinessConfigPath "path\to\config.json" -ServerRole "WebServer-2022" -DomainUser "domain\user" -DomainPassword "password"
```

**Features**:
- Creates customized user data for specific business configurations
- Supports Parameter Store integration for secure credential storage
- Generates domain join scripts based on server role
- Outputs ready-to-use PowerShell user data

**Parameters**:
- `BusinessConfigPath`: Path to business configuration JSON file
- `ServerRole`: Target server role (e.g., "WebServer-2022", "SQLServer-2022")
- `DomainUser`: Domain administrator username
- `DomainPassword`: Domain administrator password
- `OutputPath`: Output file path (default: "user-data.ps1")
- `UseParameterStore`: Use AWS Parameter Store for credentials
- `ParameterPrefix`: Parameter Store prefix (default: "/ec2/domain")

### `launch-business-instance.ps1`
**Purpose**: Launches EC2 instances with business-specific configurations and automatic domain joining

**Usage**:
```powershell
.\launch-business-instance.ps1 -BusinessConfigPath "configs\SPF_PRD.json" -ServerRole "MSSQL-2022" -AMIId "ami-xxxxxxxxx" -InstanceType "t3.medium" -KeyName "my-key" -SecurityGroupId "sg-xxxxxxxxx" -SubnetId "subnet-xxxxxxxxx"
```

**Features**:
- Launches instances with proper business tagging
- Automatically generates and applies user data
- Configures instance for domain joining
- Sets up business-specific environment variables
- Applies appropriate security groups and networking

**Parameters**:
- `BusinessConfigPath`: Path to business configuration JSON
- `ServerRole`: Server role for the instance
- `AMIId`: Custom AMI ID from ImageBuilder
- `InstanceType`: EC2 instance type
- `KeyName`: EC2 Key Pair name
- `SecurityGroupId`: Security group ID
- `SubnetId`: Subnet ID for instance placement
- `DomainUser`: Domain administrator (optional)
- `DomainPassword`: Domain password (optional)

### `schedule-auto-updates.ps1`
**Purpose**: Sets up automatic AMI updates using Windows Task Scheduler (runs on deployed instances)

**Usage**:
```powershell
.\schedule-auto-updates.ps1 -ScheduleType "Weekly" -ScheduleTime "02:00" -ScheduleDay "Sunday"
```

**Features**:
- Creates Windows scheduled tasks for AMI updates
- Configures automatic ImageBuilder pipeline execution
- Sets up logging and monitoring
- Supports multiple schedule types (Daily, Weekly, Monthly)
- Integrates with AWS CLI for pipeline management

**Parameters**:
- `ScheduleType`: Schedule frequency (Weekly, Monthly, Daily)
- `ScheduleTime`: Execution time in 24-hour format
- `ScheduleDay`: Day of week for weekly schedules
- `Region`: AWS region (default: "af-south-1")
- `LogPath`: Log file location
- `PipelineName`: ImageBuilder pipeline name

## Relationship to ImageBuilder

These scripts work with AMIs created by the ImageBuilder pipeline:

1. **ImageBuilder** creates the base AMI with .NET 4.8, registry tweaks, and firewall rules
2. **These scripts** deploy instances from that AMI with business-specific configurations
3. **User data** applies final business customizations during instance launch

## Workflow

### 1. Prepare Business Configuration
```powershell
# Ensure business config files are available
# Example: configs/SPF_PRD.json, configs/STM_PRD.json
```

### 2. Generate User Data (Optional)
```powershell
# Generate custom user data if needed
.\generate-user-data.ps1 -BusinessConfigPath "configs\SPF_PRD.json" -ServerRole "MSSQL-2022" -DomainUser "mud\administrator" -DomainPassword "SecurePassword123"
```

### 3. Launch Instance
```powershell
# Launch instance with business configuration
.\launch-business-instance.ps1 -BusinessConfigPath "configs\SPF_PRD.json" -ServerRole "MSSQL-2022" -AMIId "ami-0123456789abcdef0" -InstanceType "t3.large" -KeyName "my-keypair" -SecurityGroupId "sg-0123456789abcdef0" -SubnetId "subnet-0123456789abcdef0"
```

### 4. Setup Auto-Updates (On Instance)
```powershell
# Run on deployed instance to setup automatic updates
.\schedule-auto-updates.ps1 -ScheduleType "Weekly" -ScheduleDay "Sunday" -ScheduleTime "02:00"
```

## Security Considerations

### Credential Management
- **Avoid hardcoded passwords** in scripts
- **Use Parameter Store** for sensitive credentials
- **Implement IAM roles** instead of access keys where possible
- **Rotate credentials** regularly

### Network Security
- **Use private subnets** for database servers
- **Implement security groups** with least privilege
- **Enable VPC Flow Logs** for network monitoring
- **Use NAT Gateway** for outbound internet access

### Instance Security
- **Enable CloudTrail** for API logging
- **Configure CloudWatch** monitoring
- **Implement backup strategies**
- **Apply security patches** regularly

## Prerequisites

### AWS CLI Configuration
```powershell
# Configure AWS CLI with appropriate credentials
aws configure
```

### Required IAM Permissions
- EC2 instance management (launch, terminate, describe)
- Parameter Store access (if using)
- CloudWatch Logs access
- VPC and networking permissions

### PowerShell Modules
```powershell
# Install required modules
Install-Module -Name AWSPowerShell.NetCore -Force
```

## Troubleshooting

### Common Issues

1. **Instance Launch Failures**:
   - Check AMI availability in target region
   - Verify security group and subnet configuration
   - Ensure IAM permissions are correct

2. **Domain Join Failures**:
   - Verify domain credentials
   - Check network connectivity to domain controllers
   - Ensure DNS resolution is working

3. **User Data Execution Issues**:
   - Check instance logs in EC2 console
   - Verify PowerShell execution policy
   - Review user data syntax

### Log Locations
- **Instance Logs**: EC2 Console > Instance > Actions > Monitor and troubleshoot > Get system log
- **User Data Logs**: `C:\Scripts\user-data.log` (on instance)
- **Domain Join Logs**: Windows Event Logs > System
- **Scheduled Task Logs**: Windows Event Logs > Task Scheduler

## Integration with ImageBuilder

These scripts complement the ImageBuilder workflow:

```
ImageBuilder Pipeline → Custom AMI → EC2 Deployment Scripts → Business Instance
```

1. **ImageBuilder** creates optimized base AMI
2. **EC2 Deployment Scripts** launch instances with business configs
3. **User Data** applies final customizations
4. **Scheduled Tasks** maintain the environment

For ImageBuilder-specific operations, see the scripts in `../ImageBuilder/deployment/`.

## Best Practices

1. **Test in Development**: Always test deployments in non-production environments first
2. **Version Control**: Keep scripts in version control with proper change management
3. **Documentation**: Document any customizations or business-specific modifications
4. **Monitoring**: Implement comprehensive monitoring and alerting
5. **Backup Strategy**: Ensure proper backup and disaster recovery procedures
