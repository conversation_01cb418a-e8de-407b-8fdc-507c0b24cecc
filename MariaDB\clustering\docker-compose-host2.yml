version: '3.8'

services:
  mariadb-node2:
    image: mariadb:11.8
    container_name: mariadb-galera-node2
    hostname: mariadb-node2
    restart: unless-stopped
    
    environment:
      MYSQL_ROOT_PASSWORD: "${MYSQL_ROOT_PASSWORD}"
      MYSQL_DATABASE: "${MYSQL_DATABASE}"
      MYSQL_USER: "${MYSQL_USER}"
      MYSQL_PASSWORD: "${MYSQL_PASSWORD}"
      
    ports:
      - "3306:3306"    # MySQL port
      - "4567:4567"    # Galera replication port
      - "4568:4568"    # Incremental State Transfer port
      - "4444:4444"    # State Snapshot Transfer port
      
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./galera-node2.cnf:/etc/mysql/conf.d/galera.cnf:ro
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
      - mariadb_logs:/var/log/mysql
      
    networks:
      galera_network:
        ipv4_address: "${NODE2_IP}"
        
    command: >
      --wsrep-on=ON
      --wsrep-provider=/usr/lib/galera/libgalera_smm.so
      --wsrep-cluster-name="${CLUSTER_NAME}"
      --wsrep-cluster-address=gcomm://${HOST1_IP}:4567,${HOST2_IP}:4567
      --wsrep-node-name=node2
      --wsrep-node-address=${HOST2_IP}
      --wsrep-sst-method=rsync
      --wsrep-sst-auth="${SST_USER}:${SST_PASSWORD}"
      --binlog-format=ROW
      --default-storage-engine=InnoDB
      --innodb-autoinc-lock-mode=2
      --bind-address=0.0.0.0
      
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  mariadb_data:
    driver: local
  mariadb_logs:
    driver: local

networks:
  galera_network:
    driver: bridge
    ipam:
      config:
        - subnet: "${GALERA_SUBNET}"
          gateway: "${GALERA_GATEWAY}"
