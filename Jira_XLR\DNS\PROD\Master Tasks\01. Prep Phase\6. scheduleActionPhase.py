from datetime import datetime, timedelta
from java.util import Calendar, Date

actdate = releaseVariables['act_date']
print("PHASE START: ", type(actdate), actdate)

release.title = releaseVariables['jira_ref']
releaseApi.updateRelease(release)

phases = []
for p in release.phases:
    print("ID: ", p.id)
    phases.append(p.id)
ActionPhase = phaseApi.getPhase(phases[1])
print ActionPhase
# Set Action date and time to the scheduled date and time.
ActionPhase.setStartDate(actdate) # setScheduledStartDate
ActionPhase.setScheduledStartDate(actdate)
ActionPhase.setTitle("COOLDOWN until " + str(actdate))
ActionPhase.setFlagComment("COOLDOWN")
# Apply updated properties
phaseApi.updatePhase(ActionPhase)

cal = Calendar.getInstance()
#print cal1  # This will print something like: java.util.GregorianCalendar[time=1620123456789,areFieldsSet=true,areAllFieldsSet=true,lenient=true,zone=America/New_York,firstDayOfWeek=1,minimalDaysInFirstWeek=1,ERA=1,YEAR=2023,MONTH=4,WEEK_OF_YEAR=18,WEEK_OF_MONTH=1,DAY_OF_MONTH=10,DAY_OF_YEAR=130,DAY_OF_WEEK=3,DAY_OF_WEEK_IN_MONTH=2,AM_PM=0,HOUR=10,HOUR_OF_DAY=10,MINUTE=30,SECOND=45,MILLISECOND=789,ZONE_OFFSET=-18000000,DST_OFFSET=3600000]
cal.setTime(actdate)

taskslist = []
for item in ActionPhase.tasks:
    if item.title == "Check to Proceed":
        t = taskApi.getTask(item.id)
        t.setScheduledStartDate(cal.getTime()) 
        t.setStartDate(cal.getTime()) 
        t.setFlagComment("COOLDOWN")
        t.setDescription("Scheduled for " + str(actdate))
        taskApi.updateTask(t)