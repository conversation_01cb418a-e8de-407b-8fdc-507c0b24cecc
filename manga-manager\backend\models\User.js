module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    username: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    password: {
      type: DataTypes.STRING,
      allowNull: true // Null for OAuth users
    },
    googleId: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true
    },
    mfaEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    mfaSecret: {
      type: DataTypes.STRING,
      allowNull: true
    },
    prefersDarkMode: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    lastLogin: {
      type: DataTypes.DATE,
      allowNull: true
    },
    profilePicture: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    timestamps: true
  });

  User.associate = (models) => {
    User.hasMany(models.Bookmark, {
      foreignKey: 'userId',
      as: 'bookmarks'
    });
    User.hasMany(models.ReadingHistory, {
      foreignKey: 'userId',
      as: 'readingHistory'
    });
    User.belongsToMany(models.Category, {
      through: 'UserCategories',
      foreignKey: 'userId',
      as: 'preferredCategories'
    });
  };

  return User;
};