import json
infraview_resp = json.loads(releaseVariables['infraview_response'])

infraview_json = {}
for key, value in infraview_resp.items():
    key1 = str(key).split("  ")[0].lower().replace(" ","_")
    infraview_json.update({key1 : value})
print(infraview_json)
releaseVariables['infraview_json'] = infraview_json

releaseVariables['os_version'] = releaseVariables['infraview_json']['operating_system']
releaseVariables['competency'] = releaseVariables['infraview_json']['competency']
releaseVariables['location'] = releaseVariables['infraview_json']['location']
releaseVariables['hosting_platform'] = releaseVariables['infraview_json']['vendor']
releaseVariables['ip'] = releaseVariables['infraview_json']['ipv4address']
releaseVariables['server_description'] = releaseVariables['infraview_json']['server_description']

if not releaseVariables['competency'] or not releaseVariables['infraview_json']['primary_techowner'] or not releaseVariables['infraview_json']['secondary_techowner'] or not releaseVariables['infraview_json']['app_owner']:
    releaseVariables["is_missing_annotations"] = True
    releaseVariables['decomm_proceed'] = False