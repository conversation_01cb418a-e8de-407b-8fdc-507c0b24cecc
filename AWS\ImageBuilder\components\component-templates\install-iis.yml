# AWS Image Builder Component: Install and Configure IIS
# This component installs Internet Information Services (IIS) with common features

name: win-server-iis
description: Install and configure Internet Information Services (IIS) on Windows Server
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: CheckExistingIIS
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
            commands:
              - |
                Write-Host "Checking for existing IIS installation..."

                $iisFeature = Get-WindowsFeature -Name "IIS-WebServerRole" -ErrorAction SilentlyContinue
                if ($iisFeature -and $iisFeature.InstallState -eq "Installed") {
                    Write-Host "IIS is already installed"
                    Write-Host "Current IIS features:"
                    Get-WindowsFeature -Name "IIS-*" | Where-Object {$_.InstallState -eq "Installed"} | 
                        Select-Object Name, DisplayName | Format-Table -AutoSize
                    exit 0
                } else {
                    Write-Host "IIS is not installed. Proceeding with installation..."
                }

      - name: InstallIISCore
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Installing IIS Web Server Role..."

                # Install IIS Web Server Role and Management Tools
                $features = @(
                    "IIS-WebServerRole",
                    "IIS-WebServer",
                    "IIS-CommonHttpFeatures",
                    "IIS-HttpErrors",
                    "IIS-HttpRedirect",
                    "IIS-ApplicationDevelopment",
                    "IIS-NetFxExtensibility45",
                    "IIS-HealthAndDiagnostics",
                    "IIS-HttpLogging",
                    "IIS-Security",
                    "IIS-RequestFiltering",
                    "IIS-Performance",
                    "IIS-WebServerManagementTools",
                    "IIS-ManagementConsole",
                    "IIS-IIS6ManagementCompatibility",
                    "IIS-Metabase"
                )

                foreach ($feature in $features) {
                    Write-Host "Installing feature: $feature"
                    try {
                        Enable-WindowsOptionalFeature -Online -FeatureName $feature -All -NoRestart
                        Write-Host "Successfully installed: $feature"
                    } catch {
                        Write-Warning "Failed to install $feature : $($_.Exception.Message)"
                    }
                }

      - name: InstallASPNETFeatures
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Installing ASP.NET features..."

                $aspNetFeatures = @(
                    "IIS-NetFxExtensibility45",
                    "IIS-ASPNET45",
                    "IIS-ISAPIExtensions",
                    "IIS-ISAPIFilter",
                    "IIS-NetFxExtensibility",
                    "IIS-ASPNET"
                )

                foreach ($feature in $aspNetFeatures) {
                    Write-Host "Installing ASP.NET feature: $feature"
                    try {
                        Enable-WindowsOptionalFeature -Online -FeatureName $feature -All -NoRestart
                        Write-Host "Successfully installed: $feature"
                    } catch {
                        Write-Warning "Failed to install $feature : $($_.Exception.Message)"
                    }
                }

      - name: ConfigureIIS
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring IIS settings..."

                # Import WebAdministration module
                Import-Module WebAdministration -ErrorAction SilentlyContinue

                # Configure default application pool
                Write-Host "Configuring Default Application Pool..."
                Set-ItemProperty -Path "IIS:\AppPools\DefaultAppPool" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
                Set-ItemProperty -Path "IIS:\AppPools\DefaultAppPool" -Name "recycling.periodicRestart.time" -Value "00:00:00"
                Set-ItemProperty -Path "IIS:\AppPools\DefaultAppPool" -Name "processModel.idleTimeout" -Value "00:00:00"

                # Configure default website
                Write-Host "Configuring Default Web Site..."
                Set-ItemProperty -Path "IIS:\Sites\Default Web Site" -Name "serverAutoStart" -Value $true

                # Enable detailed error messages for development (can be disabled for production)
                Write-Host "Configuring error pages..."
                Set-WebConfigurationProperty -Filter "system.webServer/httpErrors" -Name "errorMode" -Value "Detailed" -PSPath "IIS:\" -Location "Default Web Site"

                # Configure logging
                Write-Host "Configuring IIS logging..."
                Set-WebConfigurationProperty -Filter "system.webServer/httpLogging" -Name "enabled" -Value $true -PSPath "IIS:\"
                Set-WebConfigurationProperty -Filter "system.webServer/httpLogging" -Name "logExtFileFlags" -Value "Date,Time,ClientIP,UserName,SiteName,ComputerName,ServerIP,Method,UriStem,UriQuery,HttpStatus,Win32Status,BytesSent,BytesRecv,TimeTaken,ServerPort,UserAgent,Cookie,Referer,ProtocolVersion,Host,HttpSubStatus" -PSPath "IIS:\"

      - name: InstallAdditionalFeatures
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Installing additional IIS features..."

                $additionalFeatures = @(
                    "IIS-HttpCompressionStatic",
                    "IIS-HttpCompressionDynamic",
                    "IIS-DirectoryBrowsing",
                    "IIS-DefaultDocument",
                    "IIS-StaticContent",
                    "IIS-CustomLogging",
                    "IIS-LoggingLibraries",
                    "IIS-RequestMonitor",
                    "IIS-HttpTracing",
                    "IIS-BasicAuthentication",
                    "IIS-WindowsAuthentication",
                    "IIS-DigestAuthentication",
                    "IIS-ClientCertificateMappingAuthentication",
                    "IIS-IISCertificateMappingAuthentication"
                )

                foreach ($feature in $additionalFeatures) {
                    Write-Host "Installing feature: $feature"
                    try {
                        Enable-WindowsOptionalFeature -Online -FeatureName $feature -All -NoRestart
                        Write-Host "Successfully installed: $feature"
                    } catch {
                        Write-Warning "Failed to install $feature : $($_.Exception.Message)"
                    }
                }

      - name: CreateTestPage
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Creating test page..."

                # Create test page content
                $serverName = hostname
                $currentDate = Get-Date
                $iisVersion = (Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\InetStp\" -Name "VersionString" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty VersionString)

                $htmlContent = @()
                $htmlContent += '<!DOCTYPE html>'
                $htmlContent += '<html>'
                $htmlContent += '<head>'
                $htmlContent += '    <title>IIS Test Page</title>'
                $htmlContent += '    <style>'
                $htmlContent += '        body { font-family: Arial, sans-serif; margin: 40px; }'
                $htmlContent += '        .header { color: #0066cc; }'
                $htmlContent += '        .info { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }'
                $htmlContent += '    </style>'
                $htmlContent += '</head>'
                $htmlContent += '<body>'
                $htmlContent += '    <h1 class="header">IIS Installation Successful</h1>'
                $htmlContent += '    <div class="info">'
                $htmlContent += '        <h2>Server Information</h2>'
                $htmlContent += "        <p><strong>Server:</strong> $serverName</p>"
                $htmlContent += "        <p><strong>Date:</strong> $currentDate</p>"
                $htmlContent += "        <p><strong>IIS Version:</strong> $iisVersion</p>"
                $htmlContent += '    </div>'
                $htmlContent += '    <h2>Features Installed</h2>'
                $htmlContent += '    <ul>'
                $htmlContent += '        <li>IIS Web Server Role</li>'
                $htmlContent += '        <li>ASP.NET Framework Support</li>'
                $htmlContent += '        <li>Static and Dynamic Compression</li>'
                $htmlContent += '        <li>Authentication Modules</li>'
                $htmlContent += '        <li>Management Console</li>'
                $htmlContent += '    </ul>'
                $htmlContent += '</body>'
                $htmlContent += '</html>'

                $testPageContent = $htmlContent -join "`n"

                $testPagePath = "C:\inetpub\wwwroot\test.html"
                Set-Content -Path $testPagePath -Value $testPageContent -Encoding UTF8
                Write-Host "Test page created at: $testPagePath"

  - name: validate
    steps:
      - name: ValidateIISInstallation
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating IIS installation..."

                # Check if IIS service is running
                $iisService = Get-Service -Name "W3SVC" -ErrorAction SilentlyContinue
                if ($iisService -and $iisService.Status -eq "Running") {
                    Write-Host "SUCCESS: IIS service (W3SVC) is running"
                } else {
                    Write-Error "FAILED: IIS service is not running"
                    exit 1
                }

                # Check if default website is running
                Import-Module WebAdministration -ErrorAction SilentlyContinue
                $defaultSite = Get-Website -Name "Default Web Site" -ErrorAction SilentlyContinue
                if ($defaultSite -and $defaultSite.State -eq "Started") {
                    Write-Host "SUCCESS: Default Web Site is running"
                } else {
                    Write-Error "FAILED: Default Web Site is not running"
                    exit 1
                }

                # Test HTTP response
                try {
                    $response = Invoke-WebRequest -Uri "http://localhost" -UseBasicParsing -TimeoutSec 30
                    if ($response.StatusCode -eq 200) {
                        Write-Host "SUCCESS: HTTP response test passed"
                    } else {
                        Write-Warning "HTTP response returned status code: $($response.StatusCode)"
                    }
                } catch {
                    Write-Warning "HTTP response test failed: $($_.Exception.Message)"
                }

                # Display installed IIS features
                Write-Host "`nInstalled IIS Features:"
                Get-WindowsFeature -Name "IIS-*" | Where-Object {$_.InstallState -eq "Installed"} | 
                    Select-Object Name, DisplayName | Format-Table -AutoSize

                Write-Host "VALIDATION SUCCESS: IIS is properly installed and configured"
