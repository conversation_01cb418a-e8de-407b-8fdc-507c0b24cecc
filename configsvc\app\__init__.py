# print("\nInitializing API service...")
import logging.handlers
import os
from datetime import datetime
from pathlib import Path
from functools import lru_cache

from uvicorn.config import LOGGING_CONFIG
# from app.core import config settings
from .core.config import config

from fastapi import Depends, FastAPI, Request
from fastapi.responses import J<PERSON><PERSON>esponse, FileResponse
from starlette.middleware import Middleware
# from starlette.middleware.cors import CORSMiddleware
from fastapi.middleware.cors import CORSMiddleware
from .core.exceptions import CustomException
from .core.helpers import Logging
from .api.v1.endpoints import router as endpoints_router
from .api.v1.cpsops import router as cpsops_router
from .api.v1.apisvc import router as apisvc_router
from .api.v1.webapp import router as webapp_router
from .api.v1.dcsquotes import router as dcsquotes_router
from .api.v1.namessvc import router as namesapi_router

from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles

def init_cors(app_: FastAPI) -> None:
    origins = ["http://linuxdev.local:5085"]
    app_.add_middleware(
        CORSMiddleware,
        allow_origins=origins, #["http://linuxdev.local:5085/"],
        expose_headers=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    logging.info(f"Initialised CORS")

def init_routers(app_: FastAPI) -> None:
    app_.include_router(apisvc_router)
    app_.include_router(webapp_router)
    app_.include_router(dcsquotes_router)
    app_.include_router(endpoints_router)
    app_.include_router(cpsops_router)
    app_.include_router(namesapi_router)
    logging.info(f"Initialized Routers...")

def init_listeners(app_: FastAPI) -> None:
    # Exception handler
    @app_.exception_handler(CustomException)
    async def custom_exception_handler(request: Request, exc: CustomException):
        return JSONResponse(
            status_code=exc.code,
            content={"error_code": exc.error_code, "message": exc.message},
        )
    @app_.get('/favicon.ico', include_in_schema=False)
    async def favicon():
        return FileResponse(favicon_path)

def create_app() -> FastAPI:
    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=['*'],
            allow_headers=['*']
        )
    ]
    app_ = FastAPI(
        middleware=middleware,
        title="DCS Configs Service",
        description="API Endpoints for UI Options and Deployment Configurations",
        version="0.1.0",
        docs_url="/docs", # None if config.ENV == "production" else 
        redoc_url="/redoc", # None if config.ENV == "production" else 
        dependencies=[Depends(Logging)],
        
    )
    # init_cors(app_=app_)
    init_routers(app_=app_)
    init_listeners(app_=app_)
    # logging.info(f"API is starting up on {config.UVICORN_HOST}:{config.UVICORN_PORT}...")
    # Mount the static files directory (CSS, JavaScript, images)
    app_.mount("/static", StaticFiles(directory="static"), name="static")

    
    return app_

# Set up file logging.
log_dir = Path(f"{__name__}/logs")
log_file = log_dir / f"apisvc.log"
# log_file = log_dir / f"{datetime.now().strftime('%Y-%m-%d')}.log"
os.makedirs(log_dir, exist_ok=True)

favicon_path = Path(f"{__name__}/favicon.ico")


# File handler rotates logs every 5 MB.
file_handler = logging.handlers.RotatingFileHandler(
    log_file, maxBytes=5 * (2 ** 20), backupCount=10, encoding="utf-8",
)
file_handler.setLevel(logging.DEBUG)

# Console handler prints to terminal.
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG if config.debug else logging.INFO)

# Format configuration.
fmt = "%(asctime)s - apisvc %(levelname)s: %(message)s"
datefmt = "%Y-%m-%d %H:%M:%S" #"%H:%M:%S"

# Add colors for logging if available.
try:
    from colorlog import ColoredFormatter

    console_handler.setFormatter(
        ColoredFormatter(fmt=f"%(log_color)s{fmt}", datefmt=datefmt))
except ModuleNotFoundError:
    pass

# Remove old loggers, if any.
root = logging.getLogger()
if root.handlers:
    for handler in root.handlers:
        root.removeHandler(handler)

# Silence irrelevant loggers.
logging.getLogger("asyncio").setLevel(logging.ERROR)

# Setup new logging configuration.
logging.basicConfig(
    format=fmt,
    datefmt=datefmt,
    level=logging.DEBUG,
    handlers=[console_handler, file_handler]
)

# Configure uvicorn loggers.
LOGGING_CONFIG["loggers"]["uvicorn.access"]["propagate"] = True
LOGGING_CONFIG["loggers"]["uvicorn.access"].pop("handlers", None)
LOGGING_CONFIG["loggers"]["uvicorn"].pop("handlers", None)

