{"name": "manga-manager", "version": "1.0.0", "description": "A modern and responsive manga management webapp", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"bcrypt": "^5.1.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-session": "^1.17.3", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "pg": "^8.10.0", "pg-hstore": "^2.3.4", "sequelize": "^6.31.0", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "sharp": "^0.32.0", "unzipper": "^0.10.14", "yauzl": "^2.10.0"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "sequelize-cli": "^6.6.0"}}