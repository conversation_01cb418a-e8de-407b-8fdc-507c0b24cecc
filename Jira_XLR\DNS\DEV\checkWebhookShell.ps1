$reportDate = Get-Date -Format "yyyy-MM-dd HH:mm"

function jsonReturn {
    param(
        [Parameter(Mandatory = $true)]
        [string]$success,
        [Parameter(Mandatory = $true)]
        [string]$status
    )
    $objectReturn = @{
        "vmName" = "srv009484"
        "timeStamp" = $reportDate
        "service" = "WebhookShell"
    }
    $jsonResponse = @{
        "success" = $success
        "status" = $status
        "data" = $objectReturn
    }
    $jsonResponse | ConvertTo-Json
}

return jsonReturn -success "success" -status "online"