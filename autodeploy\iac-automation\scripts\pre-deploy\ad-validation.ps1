[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [string]$VMName,

    [Parameter(Mandatory=$true)]
    [string]$OrgUnit,

    [Parameter(Mandatory=$false)]
    [string]$DomainController = $env:TF_VAR_ad_domain_controller
)

if (-not $DomainController) {
    throw "✗ DomainController parameter is not set and TF_VAR_ad_domain_controller environment variable is missing."
}

Write-Host "=== Active Directory Validation for $VMName ===" -ForegroundColor Green
Write-Verbose "=== Active Directory Validation for $VMName ==="

try {
    # Check for existing computer object
    Write-Host "Checking for existing computer object: $VMName" -ForegroundColor Yellow
    Write-Verbose "Checking for existing computer object: $VMName"

    # Actual AD check for existing computer object
    $existingObject = Get-ADComputer -Filter "Name -eq '$VMName'" -Server $DomainController -ErrorAction SilentlyContinue

    # For testing/simulation, uncomment the line below to simulate no existing object
    # $existingObject = $null

    if ($existingObject) {
        throw "✗ Computer object $VMName already exists in Active Directory"
    } else {
        Write-Host "✓ Computer object $VMName does not exist - safe to proceed" -ForegroundColor Green
        Write-Verbose "✓ Computer object $VMName does not exist - safe to proceed"
    }

    # Validate OU exists
    Write-Host "Validating organizational unit: $OrgUnit" -ForegroundColor Yellow
    Write-Verbose "Validating organizational unit: $OrgUnit"

    # Actual OU validation
    $ouObject = Get-ADOrganizationalUnit -Filter "DistinguishedName -eq '$OrgUnit'" -Server $DomainController -ErrorAction SilentlyContinue

    if ($null -eq $ouObject) {
        throw "✗ Organizational Unit '$OrgUnit' does not exist in Active Directory"
    } else {
        Write-Host "✓ Organizational Unit validated successfully" -ForegroundColor Green
        Write-Verbose "✓ Organizational Unit validated successfully"
    }

    Write-Host "=== Active Directory validation completed successfully ===" -ForegroundColor Green
    exit 0
}
catch {
    Write-Error "✗ Active Directory validation failed: $($_.Exception.Message)"
    exit 1
}