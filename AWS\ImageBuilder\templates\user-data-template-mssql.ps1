# ImageBuilder User Data Template for MSSQL Servers
# This script is used during ImageBuilder recipe execution to prepare the base AMI for SQL Server
# It should NOT contain business-specific configurations like admin groups or domain joining

<powershell>
# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope LocalMachine -Force

# Create scripts directory structure for future deployment use
$directories = @(
    "C:\Temp\ServerInstalls",
    "C:\Temp\ServerInstalls\Logs",
    "C:\Temp\ServerInstalls\Config",
    "C:\Temp\ServerInstalls\Tools",
    "C:\Temp\ServerInstalls\MSSQL",
    "C:\Temp\ServerInstalls\MSSQL\Backups",
    "C:\Temp\ServerInstalls\MSSQL\Scripts",
    "C:\Temp\ServerInstalls\Clustering"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

# Create log file
$logFile = "C:\Temp\ServerInstalls\imagebuilder-mssql-prep.log"
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

Write-Log "Starting ImageBuilder MSSQL preparation script..."

# Enable WinRM for remote management
Write-Log "Enabling WinRM for remote management"
Enable-PSRemoting -Force -SkipNetworkProfileCheck

# Configure WinRM settings
Write-Log "Configuring WinRM settings"
winrm quickconfig -q
winrm set winrm/config/winrs '@{MaxMemoryPerShellMB="512"}'
winrm set winrm/config '@{MaxTimeoutms="1800000"}'
winrm set winrm/config/service '@{AllowUnencrypted="false"}'
winrm set winrm/config/service/auth '@{Basic="true"}'

# Create self-signed certificate for HTTPS
Write-Log "Creating self-signed certificate for WinRM HTTPS"
$cert = New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "cert:\LocalMachine\My"
winrm create winrm/config/Listener?Address=*+Transport=HTTPS "@{Hostname=`"localhost`";CertificateThumbprint=`"$($cert.Thumbprint)`"}"

# Configure firewall for WinRM HTTPS
Write-Log "Configuring firewall for WinRM HTTPS"
netsh advfirewall firewall add rule name="WinRM-HTTPS" dir=in localport=5986 protocol=TCP action=allow

# Restart WinRM service
Write-Log "Restarting WinRM service"
Restart-Service winrm

Write-Log "WinRM configuration completed"

try {
    # ImageBuilder preparation tasks
    Write-Log "Starting ImageBuilder MSSQL preparation tasks..."

    # Set up Windows optimizations for SQL Server
    Write-Log "Applying Windows server optimizations for SQL Server..."

    # Configure firewall rules (including SQL Server specific rules)
    Write-Log "Configuring firewall rules..."
    $firewallRules = @(
        @{Name="File and Printer Sharing (Echo Request - ICMPv4-In)"; DisplayName="File and Printer Sharing (Echo Request - ICMPv4-In)"},
        @{Name="Remote Service Management (RPC-EPMAP)"; DisplayName="Remote Service Management (RPC-EPMAP)"},
        @{Name="Remote Service Management (RPC)"; DisplayName="Remote Service Management (RPC)"},
        @{Name="Remote Service Management (NP-In)"; DisplayName="Remote Service Management (NP-In)"},
        @{Name="Windows Management Instrumentation (DCOM-In)"; DisplayName="Windows Management Instrumentation (DCOM-In)"},
        @{Name="Windows Management Instrumentation (WMI-In)"; DisplayName="Windows Management Instrumentation (WMI-In)"}
    )

    foreach ($rule in $firewallRules) {
        try {
            Enable-NetFirewallRule -DisplayName $rule.DisplayName -ErrorAction SilentlyContinue
            Write-Log "Enabled firewall rule: $($rule.DisplayName)"
        } catch {
            Write-Log "Could not enable firewall rule $($rule.DisplayName): $($_.Exception.Message)" "WARNING"
        }
    }

    # Add SQL Server specific firewall rules
    Write-Log "Adding SQL Server specific firewall rules..."
    $sqlFirewallRules = @(
        @{Name="SQL Server"; Port=1433; Protocol="TCP"},
        @{Name="SQL Server Browser"; Port=1434; Protocol="UDP"},
        @{Name="SQL Server Analysis Services"; Port=2383; Protocol="TCP"},
        @{Name="SQL Server Reporting Services"; Port=80; Protocol="TCP"}
    )

    foreach ($rule in $sqlFirewallRules) {
        try {
            New-NetFirewallRule -DisplayName $rule.Name -Direction Inbound -Protocol $rule.Protocol -LocalPort $rule.Port -Action Allow -ErrorAction SilentlyContinue
            Write-Log "Added SQL Server firewall rule: $($rule.Name) on port $($rule.Port)"
        } catch {
            Write-Log "Could not add SQL Server firewall rule $($rule.Name): $($_.Exception.Message)" "WARNING"
        }
    }

    # Enable RDP with Network Level Authentication
    Write-Log "Enabling RDP with Network Level Authentication..."
    try {
        Set-ItemProperty -Path "HKLM:\System\CurrentControlSet\Control\Terminal Server" -Name "fDenyTSConnections" -Value 0
        Set-ItemProperty -Path "HKLM:\System\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp" -Name "UserAuthentication" -Value 1
        Enable-NetFirewallRule -DisplayGroup "Remote Desktop"
        Write-Log "RDP enabled with NLA"
    } catch {
        Write-Log "Could not configure RDP: $($_.Exception.Message)" "WARNING"
    }

    # Configure page file for SQL Server (larger than default)
    Write-Log "Configuring page file for SQL Server..."
    try {
        $cs = Get-WmiObject -Class Win32_ComputerSystem
        $totalRAM = [math]::Round($cs.TotalPhysicalMemory / 1GB)
        
        # For SQL Server, set page file to 1.5x RAM or minimum 8GB
        $pageFileSize = [math]::Max(8, [math]::Round($totalRAM * 1.5))
        
        # Remove existing page files
        $pf = Get-WmiObject -Class Win32_PageFileSetting
        if ($pf) {
            $pf.Delete()
        }

        # Create new page file
        $pf = ([wmiclass]"Win32_PageFileSetting").CreateInstance()
        $pf.Name = "C:\pagefile.sys"
        $pf.InitialSize = $pageFileSize * 1024
        $pf.MaximumSize = $pageFileSize * 1024
        $pf.Put() | Out-Null

        Write-Log "Configured page file for SQL Server: ${pageFileSize}GB"
    } catch {
        Write-Log "Could not configure page file: $($_.Exception.Message)" "WARNING"
    }

    # Adjust for best performance
    Write-Log "Configuring system for best performance..."
    try {
        Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" -Name "VisualFXSetting" -Value 2
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" -Name "VisualFXSetting" -Value 2
        Write-Log "Set system for best performance"
    } catch {
        Write-Log "Could not set performance settings: $($_.Exception.Message)" "WARNING"
    }

    # Set power plan to High Performance
    Write-Log "Setting power plan to High Performance..."
    try {
        $highPerfGuid = (powercfg -list | Where-Object { $_ -match "High performance" } | ForEach-Object { $_.Split()[3] })
        if ($highPerfGuid) {
            powercfg -setactive $highPerfGuid
            Write-Log "Power plan set to High Performance"
        }
    } catch {
        Write-Log "Could not set power plan: $($_.Exception.Message)" "WARNING"
    }

    # Turn on DEP for essential Windows programs and services
    Write-Log "Configuring Data Execution Prevention..."
    try {
        bcdedit /set nx OptIn
        Write-Log "DEP configured for essential Windows programs and services"
    } catch {
        Write-Log "Could not configure DEP: $($_.Exception.Message)" "WARNING"
    }

    # Configure disk allocation unit size to 64K for SQL Server data drives
    Write-Log "Configuring disk allocation unit size for SQL Server..."
    try {
        $disks = Get-Disk | Where-Object { $_.Number -ne 0 }
        foreach ($disk in $disks) {
            # Convert to GPT if not already
            if ($disk.PartitionStyle -eq "MBR") {
                Clear-Disk -Number $disk.Number -RemoveData -Confirm:$false
                Initialize-Disk -Number $disk.Number -PartitionStyle GPT
                Write-Log "Converted disk $($disk.Number) to GPT for SQL Server"
            }
        }
        Write-Log "Non-OS drives configured for GPT (64K allocation recommended for SQL Server data files)"
    } catch {
        Write-Log "Could not configure disk settings: $($_.Exception.Message)" "WARNING"
    }

    # Configure RecycleBin settings for C: drive
    Write-Log "Configuring RecycleBin settings..."
    try {
        Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\BitBucket\Volume\{C:}" -Name "NukeOnDelete" -Value 1 -ErrorAction SilentlyContinue
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\BitBucket\Volume\{C:}" -Name "NukeOnDelete" -Value 1 -ErrorAction SilentlyContinue
        Write-Log "RecycleBin configured to not move files to recycle bin for C:"
    } catch {
        Write-Log "Could not configure RecycleBin settings: $($_.Exception.Message)" "WARNING"
    }

    # Disable IPv6 (uncheck but not remove)
    Write-Log "Disabling IPv6..."
    try {
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip6\Parameters" -Name "DisabledComponents" -Value 0xFF
        Write-Log "IPv6 disabled"
    } catch {
        Write-Log "Could not disable IPv6: $($_.Exception.Message)" "WARNING"
    }

    # Disable UAC Remote Restrictions
    Write-Log "Disabling UAC Remote Restrictions..."
    try {
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "LocalAccountTokenFilterPolicy" -Value 1
        Write-Log "UAC Remote Restrictions disabled"
    } catch {
        Write-Log "Could not disable UAC Remote Restrictions: $($_.Exception.Message)" "WARNING"
    }

    # Apply additional registry settings
    Write-Log "Applying additional registry settings..."
    try {
        # Internet Explorer security settings
        New-Item -Path "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX" -Name "iexplore.exe" -Value 1

        New-Item -Path "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Internet Explorer\Main\FeatureControl\FEATURE_ENABLE_PRINT_INFO_DISCLOSURE_FIX" -Name "iexplore.exe" -Value 1

        New-Item -Path "HKLM:\SOFTWARE\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING" -Name "iexplore.exe" -Value 1

        New-Item -Path "HKLM:\SOFTWARE\Wow6432Node\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Wow6432Node\Microsoft\Internet Explorer\MAIN\FeatureControl\FEATURE_ALLOW_USER32_EXCEPTION_HANDLER_HARDENING" -Name "iexplore.exe" -Value 1

        # Cryptography settings
        New-Item -Path "HKLM:\Software\Microsoft\Cryptography\Wintrust\Config" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\Software\Microsoft\Cryptography\Wintrust\Config" -Name "EnableCertPaddingCheck" -Value "1"

        New-Item -Path "HKLM:\Software\Wow6432Node\Microsoft\Cryptography\Wintrust\Config" -Force | Out-Null
        Set-ItemProperty -Path "HKLM:\Software\Wow6432Node\Microsoft\Cryptography\Wintrust\Config" -Name "EnableCertPaddingCheck" -Value "1"

        Write-Log "Additional registry settings applied"
    } catch {
        Write-Log "Could not apply additional registry settings: $($_.Exception.Message)" "WARNING"
    }

    # SQL Server specific optimizations
    Write-Log "Applying SQL Server specific optimizations..."
    try {
        # Enable .NET Framework 3.5 (required for SQL Server)
        Write-Log "Enabling .NET Framework 3.5..."
        Enable-WindowsOptionalFeature -Online -FeatureName NetFx3 -All -NoRestart

        # Configure Windows to optimize for background services (better for SQL Server)
        Write-Log "Configuring Windows for background services..."
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\PriorityControl" -Name "Win32PrioritySeparation" -Value 24

        # Disable Windows Search service (can interfere with SQL Server)
        Write-Log "Disabling Windows Search service..."
        Set-Service -Name "WSearch" -StartupType Disabled -ErrorAction SilentlyContinue

        # Configure lock pages in memory privilege (will be set for SQL Server service account later)
        Write-Log "Preparing lock pages in memory privilege configuration..."

        Write-Log "SQL Server specific optimizations completed"
    } catch {
        Write-Log "Could not apply SQL Server optimizations: $($_.Exception.Message)" "WARNING"
    }

    # Install Windows Clustering feature for SQL Server Always On
    Write-Log "Installing Windows Clustering feature for SQL Server Always On..."
    try {
        # Install Failover Clustering feature
        Write-Log "Installing Failover-Clustering feature..."
        $clusterFeature = Install-WindowsFeature -Name Failover-Clustering -IncludeManagementTools -NoRestart

        if ($clusterFeature.Success) {
            Write-Log "Failover-Clustering feature installed successfully"

            # Install RSAT-Clustering-PowerShell for PowerShell cmdlets
            Write-Log "Installing RSAT-Clustering-PowerShell..."
            $clusterPSFeature = Install-WindowsFeature -Name RSAT-Clustering-PowerShell -NoRestart

            if ($clusterPSFeature.Success) {
                Write-Log "RSAT-Clustering-PowerShell installed successfully"
            } else {
                Write-Log "Failed to install RSAT-Clustering-PowerShell: $($clusterPSFeature.ExitCode)" "WARNING"
            }

            # Configure clustering firewall rules
            Write-Log "Configuring clustering firewall rules..."
            $clusterFirewallRules = @(
                @{Name="Failover Cluster Manager"; Port=135; Protocol="TCP"},
                @{Name="Cluster Service"; Port=3343; Protocol="TCP"},
                @{Name="Cluster Service UDP"; Port=3343; Protocol="UDP"},
                @{Name="RPC Dynamic Ports"; Port="1024-5000"; Protocol="TCP"},
                @{Name="ICMPv4"; DisplayGroup="File and Printer Sharing"}
            )

            foreach ($rule in $clusterFirewallRules) {
                try {
                    if ($rule.DisplayGroup) {
                        Enable-NetFirewallRule -DisplayGroup $rule.DisplayGroup -ErrorAction SilentlyContinue
                        Write-Log "Enabled firewall rule group: $($rule.DisplayGroup)"
                    } elseif ($rule.Port -like "*-*") {
                        # Handle port ranges
                        $portRange = $rule.Port.Split('-')
                        New-NetFirewallRule -DisplayName $rule.Name -Direction Inbound -Protocol $rule.Protocol -LocalPort $portRange[0]-$portRange[1] -Action Allow -ErrorAction SilentlyContinue
                        Write-Log "Added clustering firewall rule: $($rule.Name) for port range $($rule.Port)"
                    } else {
                        New-NetFirewallRule -DisplayName $rule.Name -Direction Inbound -Protocol $rule.Protocol -LocalPort $rule.Port -Action Allow -ErrorAction SilentlyContinue
                        Write-Log "Added clustering firewall rule: $($rule.Name) on port $($rule.Port)"
                    }
                } catch {
                    Write-Log "Could not configure clustering firewall rule $($rule.Name): $($_.Exception.Message)" "WARNING"
                }
            }

            # Create clustering preparation directory
            $clusterDir = "C:\Temp\ServerInstalls\Clustering"
            if (!(Test-Path $clusterDir)) {
                New-Item -ItemType Directory -Path $clusterDir -Force | Out-Null
                Write-Log "Created clustering directory: $clusterDir"
            }

            # Create cluster validation preparation script
            $clusterValidationScript = @"
# Cluster Validation Preparation Script
# This script should be run after domain join and before cluster creation
# Run this on all nodes that will participate in the cluster

# Validate cluster prerequisites
Write-Host "Validating cluster prerequisites..."

# Check if Failover Clustering feature is installed
`$clusterFeature = Get-WindowsFeature -Name Failover-Clustering
if (`$clusterFeature.InstallState -eq "Installed") {
    Write-Host "✓ Failover Clustering feature is installed"
} else {
    Write-Host "✗ Failover Clustering feature is not installed"
}

# Check if RSAT-Clustering-PowerShell is installed
`$clusterPSFeature = Get-WindowsFeature -Name RSAT-Clustering-PowerShell
if (`$clusterPSFeature.InstallState -eq "Installed") {
    Write-Host "✓ RSAT-Clustering-PowerShell is installed"
} else {
    Write-Host "✗ RSAT-Clustering-PowerShell is not installed"
}

# Test cluster connectivity (run after all nodes are domain joined)
# Test-Cluster -Node "Node1","Node2" -Include "Storage Spaces Direct","Inventory","Network","System Configuration"

Write-Host "Cluster validation preparation complete"
Write-Host "Next steps:"
Write-Host "1. Join all nodes to the domain"
Write-Host "2. Configure shared storage (if using traditional clustering)"
Write-Host "3. Run Test-Cluster cmdlet to validate cluster readiness"
Write-Host "4. Create the cluster using New-Cluster cmdlet"
"@

            $clusterValidationScript | Out-File -FilePath "$clusterDir\cluster-validation-prep.ps1" -Encoding UTF8
            Write-Log "Created cluster validation preparation script: $clusterDir\cluster-validation-prep.ps1"

        } else {
            Write-Log "Failed to install Failover-Clustering feature: $($clusterFeature.ExitCode)" "WARNING"
        }

        Write-Log "Windows Clustering feature installation completed"
    } catch {
        Write-Log "Could not install Windows Clustering feature: $($_.Exception.Message)" "WARNING"
    }

    # Download SQL Server configuration from S3
    Write-Log "Downloading SLMSQL_Config.json from S3..."
    try {
        $s3Bucket = "your-deployment-bucket"  # Replace with your S3 bucket name
        $sqlConfigS3Key = "configs/SLMSQL_Config.json"
        $sqlConfigPath = "C:\Temp\ServerInstalls\Config\SLMSQL_Config.json"
        
        # Download the SQL Server configuration file
        aws s3 cp "s3://$s3Bucket/$sqlConfigS3Key" $sqlConfigPath --region af-south-1 2>&1 | Out-Null
        
        if (Test-Path $sqlConfigPath) {
            Write-Log "SLMSQL_Config.json downloaded successfully to: $sqlConfigPath"
            
            # Load and validate the configuration
            $sqlConfig = Get-Content -Path $sqlConfigPath | ConvertFrom-Json
            Write-Log "SQL Server configuration loaded - Features: $($sqlConfig.SQL_SERVER.INSTALLATION.FEATURES)"
        } else {
            Write-Log "Failed to download SLMSQL_Config.json from S3" "ERROR"
        }
    } catch {
        Write-Log "Error downloading SQL Server configuration: $($_.Exception.Message)" "ERROR"
    }

    # Prepare D: drive directory structure for SQL Server
    Write-Log "Preparing D: drive directory structure for SQL Server..."
    try {
        $sqlDirectories = @(
            "D:\Program Files\Microsoft SQL Server",
            "D:\MSSQL\Data",
            "D:\MSSQL\Logs", 
            "D:\MSSQL\TempDB",
            "D:\MSSQL\Backup"
        )

        foreach ($dir in $sqlDirectories) {
            if (!(Test-Path $dir)) {
                New-Item -ItemType Directory -Path $dir -Force | Out-Null
                Write-Log "Created SQL Server directory: $dir"
            }
        }
    } catch {
        Write-Log "Could not create D: drive directories (D: drive may not exist yet): $($_.Exception.Message)" "WARNING"
    }

    # Create SQL Server preparation info
    Write-Log "Creating SQL Server image information..."
    $imageInfo = @{
        ImageType = "MSSQL Server"
        PreparedDate = Get-Date
        WindowsVersion = (Get-WmiObject -Class Win32_OperatingSystem).Caption
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
        OptimizedFor = "Microsoft SQL Server"
        Features = @(
            "WinRM HTTPS",
            "RDP with NLA",
            "SQL Server Firewall Rules",
            "Windows Failover Clustering",
            "High Performance Power Plan",
            "Optimized Page File",
            "Background Services Priority",
            ".NET Framework 3.5",
            "GPT Disk Format",
            "Security Hardening"
        )
        Notes = @(
            "Ready for SQL Server installation",
            "Optimized for database workloads",
            "64K allocation unit size recommended for data drives",
            "Lock pages in memory privilege ready for configuration",
            "SQL Server will be installed to D: drive",
            "DBA-approved configuration template available",
            "Windows Failover Clustering feature installed for Always On Availability Groups",
            "Cluster validation script available at C:\Temp\ServerInstalls\Clustering\cluster-validation-prep.ps1",
            "SQL Admin Groups: MUD\UL-SPFBEL01A-SQLServiceAccounts, MUD\UL-PAM-SGT-DbaMssqlAdmin-ADM"
        )
        SQLServerConfig = @{
            Features = "SQLENGINE,REPLICATION"
            InstanceName = "MSSQLSERVER"
            InstallDirectory = "D:\Program Files\Microsoft SQL Server"
            Collation = "Latin1_General_CI_AS"
            SecurityMode = "SQL"
            AdminGroups = @(
                "MUD\UL-SPFBEL01A-SQLServiceAccounts",
                "MUD\UL-PAM-SGT-DbaMssqlAdmin-ADM"
            )
            ConfigFile = "C:\Temp\ServerInstalls\Config\SLMSQL_Config.json"
        }
    }

    $imageInfoPath = "C:\Temp\ServerInstalls\mssql-image-info.json"
    $imageInfo | ConvertTo-Json -Depth 10 | Out-File -FilePath $imageInfoPath -Encoding UTF8
    Write-Log "SQL Server image information saved to: $imageInfoPath"

    # Create SQL Server deployment placeholder
    Write-Log "Creating SQL Server deployment placeholder..."
    $deploymentPlaceholder = @"
# SQL Server ImageBuilder Preparation Complete
# This file is created during ImageBuilder preparation for SQL Server
# 
# Available directories:
# - C:\Temp\ServerInstalls\Logs         - For deployment logs
# - C:\Temp\ServerInstalls\Config       - For configuration files
# - C:\Temp\ServerInstalls\Tools        - For deployment tools
# - C:\Temp\ServerInstalls\MSSQL        - For SQL Server specific files
# - C:\Temp\ServerInstalls\MSSQL\Backups - For database backups
# - C:\Temp\ServerInstalls\MSSQL\Scripts - For SQL scripts
# - C:\Temp\ServerInstalls\Clustering   - For clustering scripts and tools
#
# SQL Server Optimizations Applied:
# - Firewall rules for SQL Server ports (1433, 1434, 2383, 80)
# - Windows Failover Clustering feature installed with management tools
# - Clustering firewall rules configured (135, 3343 TCP/UDP, RPC dynamic ports)
# - Page file optimized for SQL Server workloads
# - .NET Framework 3.5 enabled
# - Background services priority set
# - Windows Search disabled
# - High performance power plan
# - GPT disk format for data drives
# - Cluster validation preparation script available
#
# Image prepared on: $(Get-Date)
# Ready for SQL Server installation and business-specific deployment
"@

    $deploymentPlaceholder | Out-File -FilePath "C:\Temp\ServerInstalls\README-MSSQL-Deployment.txt" -Encoding UTF8

    Write-Log "SQL Server ImageBuilder preparation completed successfully"

} catch {
    Write-Log "Error during ImageBuilder preparation: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
}

Write-Log "ImageBuilder MSSQL preparation script completed"
</powershell>






