# Add to system PATH (requires Administrator)
$packerPath = "C:\Program Files\Packer"
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
if ($currentPath -notlike "*$packerPath*") {
    [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$packerPath", "Machine")
    Write-Host "Packer added to system PATH" -ForegroundColor Green
} else {
    Write-Host "Packer already in PATH" -ForegroundColor Yellow
}