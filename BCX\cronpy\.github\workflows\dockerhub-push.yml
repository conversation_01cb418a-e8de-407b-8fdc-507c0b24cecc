name: Build and Publish Docker Image

on:
  push:
    branches: [ "dev", "ppe", "main" ]
  workflow_dispatch:


jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Check out the repo
      uses: actions/checkout@v4

    - name: Log in to Docker Hub
      uses: docker/login-action@f4ef78c080cd8ba55a85445d5b36e214a81df20a
      with:
        username: ${{ secrets.DOCKERHUB_MARCEL_USER }}
        password: ${{ secrets.DOCKERHUB_MARCEL_PUSH }}

    - name: Extract metadata for Docker
      id: meta
      uses: docker/metadata-action@9ec57ed1fcdbf14dcef7dfbe97b2010124a938b7
      with:
        images: maximus1202/cronpy
        tags: |
          type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}
          type=raw,value=prd,enable=${{ github.ref == 'refs/heads/main' }}
          type=raw,value=ppe,enable=${{ github.ref == 'refs/heads/ppe' }}
          type=raw,value=dev,enable=${{ github.ref == 'refs/heads/dev' }}
          type=sha,format=short

    - name: Build and push Docker image
      uses: docker/build-push-action@3b5e8027fcad23fda98b2e3ac259d8d67585f671
      with:
        context: .
        file: ./docker/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
