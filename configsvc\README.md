# DCS ADMIN AUTOMATIONs

This is an API service for common DCS Admin tasks to enable automations.
<!-- Table of Contents -->

### API Documentation
Open browser to:
URL/docs
<!-- Table of Contents -->


## Usage

Runs on port specified, currently suggest 8030 for this service.
```
uvicorn app.main:app --host 0.0.0.0 --port 8030 --reload
```

## Features

### Project:


## License

Distributed under the MIT License. See [LICENSE](./LICENSE) for more information.

## Contact

<PERSON> - [<EMAIL>][email]
