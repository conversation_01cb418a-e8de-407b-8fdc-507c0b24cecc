# Define helper function to reduce repetition
def handle_dns_operation(operation_success, operation_type):
    success = operation_success  # Now directly using the boolean value
    message = "DNS record %s %s" % (operation_type, "successfully" if success else "failed")
    
    # Set specific operation result variables
    releaseVariables['dns%sSuccess' % operation_type.capitalize()] = success
    releaseVariables['dns%sMessage' % operation_type.capitalize()] = message
    
    # Return all required values including requestSuccess and requestStatus
    if success:
        return True, "DNS record %s successfully" % operation_type, 'SUCCESS', 'COMPLETE'
    else:
        return False, "An error occurred in the DNS request, please log a BMC request for further assistance", 'FAILURE', 'CRITICAL'

# Process based on operation type
if releaseVariables['updateRecord'] == True:
    remove_success = releaseVariables['dnsRemoveSuccess']
    add_success = releaseVariables['dnsAddSuccess']
    
    if remove_success and add_success:
        dnsRequestSuccess, dnsRequestMessage = True, "DNS record updated successfully"
        releaseVariables['dnsUpdateSuccess'] = True
        releaseVariables['dnsUpdateMessage'] = "DNS record updated successfully"
        requestSuccess = 'SUCCESS'
        requestStatus = 'COMPLETE'
    else:
        dnsRequestSuccess, dnsRequestMessage = False, "An error occurred in the DNS request, please log a BMC request for further assistance"
        releaseVariables['dnsUpdateSuccess'] = False
        releaseVariables['dnsUpdateMessage'] = "DNS record update failed"
        requestSuccess = 'FAILURE'
        requestStatus = 'CRITICAL'

elif releaseVariables['addRecord'] == True:
    dnsRequestSuccess, dnsRequestMessage, requestSuccess, requestStatus = handle_dns_operation(releaseVariables['dnsAddSuccess'], "added")

elif releaseVariables['deleteRecord'] == True:
    dnsRequestSuccess, dnsRequestMessage, requestSuccess, requestStatus = handle_dns_operation(releaseVariables['dnsRemoveSuccess'], "removed")

else:
    dnsRequestSuccess = False
    dnsRequestMessage = "An error occurred in the DNS request, please log a BMC request for further assistance"
    requestSuccess = 'FAILURE'
    requestStatus = 'CRITICAL'

print dnsRequestSuccess
print dnsRequestMessage
print requestStatus
print requestSuccess

releaseVariables['dnsRequestSuccess'] = dnsRequestSuccess
releaseVariables['dnsRequestMessage'] = dnsRequestMessage
releaseVariables['success'] = requestSuccess
releaseVariables['status'] = requestStatus