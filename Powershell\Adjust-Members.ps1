function Grant-ClusterNodePermissions {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [Microsoft.ActiveDirectory.Management.ADComputer]$adObject,

        [Parameter(Mandatory = $true)]
        [string]$clusterNodeList,

        [Parameter(Mandatory = $true)]
        [string]$domainName,

        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]$adCreds
    )

    # Split by multiple delimiters: comma, space, or pipe
    $nodeList = $clusterNodeList -split '[,\s\|]+' | ForEach-Object { $_.Trim() } | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }

    foreach ($node in $nodeList) {
        if (-not [string]::IsNullOrWhiteSpace($node)) {
            try {
                $nodeAccount = Get-ADComputer -Identity $node -Server $domainName -Credential $adCreds -ErrorAction Stop
                $acl = Get-Acl -Path "AD:\$($adObject.DistinguishedName)"

                $accessRule = New-Object System.DirectoryServices.ActiveDirectoryAccessRule(
                    $nodeAccount.SID,
                    [System.DirectoryServices.ActiveDirectoryRights]::GenericAll,
                    [System.Security.AccessControl.AccessControlType]::Allow
                )

                $acl.SetAccessRule($accessRule)
                Set-Acl -Path "AD:\$($adObject.DistinguishedName)" -AclObject $acl
            }
            catch {
                Write-Warning "Failed to grant permissions to $node`: $($_.Exception.Message)"
            }
        }
    }
}
