# Upload Business Configuration Files to S3
# This script uploads the business configuration JSON files to an S3 bucket

param(
    [Parameter(Mandatory=$true)]
    [string]$S3Bucket,
    
    [Parameter(Mandatory=$false)]
    [string]$S3Prefix = "business-configs",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "af-south-1",
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigsPath = "../configs"
)

# Function to write log messages
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
}

Write-Log "Starting business configuration upload to S3"
Write-Log "S3 Bucket: $S3Bucket"
Write-Log "S3 Prefix: $S3Prefix"
Write-Log "Region: $Region"
Write-Log "Configs Path: $ConfigsPath"

try {
    # Check if AWS CLI is available
    $awsVersion = aws --version 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "AWS CLI is not installed or not in PATH"
    }
    Write-Log "AWS CLI version: $awsVersion"
    
    # Check if configs directory exists
    if (!(Test-Path $ConfigsPath)) {
        throw "Configs directory not found: $ConfigsPath"
    }
    
    # Get all JSON files in the configs directory
    $configFiles = Get-ChildItem -Path $ConfigsPath -Filter "*.json"
    
    if ($configFiles.Count -eq 0) {
        throw "No JSON configuration files found in: $ConfigsPath"
    }
    
    Write-Log "Found $($configFiles.Count) configuration files"
    
    # Check if S3 bucket exists
    Write-Log "Checking if S3 bucket exists: $S3Bucket"
    aws s3api head-bucket --bucket $S3Bucket --region $Region 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Log "S3 bucket does not exist or is not accessible: $S3Bucket" "ERROR"
        Write-Log "Please ensure the bucket exists and you have proper permissions" "ERROR"
        exit 1
    }
    Write-Log "S3 bucket is accessible"
    
    # Upload each configuration file
    $uploadedFiles = @()
    $failedFiles = @()
    
    foreach ($file in $configFiles) {
        $fileName = $file.Name
        $filePath = $file.FullName
        $s3Key = "$S3Prefix/$fileName"
        
        Write-Log "Uploading $fileName to s3://$S3Bucket/$s3Key"
        
        try {
            # Validate JSON before upload
            $jsonContent = Get-Content $filePath -Raw | ConvertFrom-Json
            Write-Log "JSON validation passed for $fileName"
            
            # Upload to S3
            aws s3 cp $filePath "s3://$S3Bucket/$s3Key" --region $Region 2>&1 | Out-Null
            
            if ($LASTEXITCODE -eq 0) {
                Write-Log "Successfully uploaded: $fileName" "SUCCESS"
                $uploadedFiles += $fileName
                
                # Set appropriate permissions (private by default)
                aws s3api put-object-acl --bucket $S3Bucket --key $s3Key --acl private --region $Region 2>&1 | Out-Null
                
            } else {
                throw "AWS S3 upload failed"
            }
            
        } catch {
            Write-Log "Failed to upload $fileName : $($_.Exception.Message)" "ERROR"
            $failedFiles += $fileName
        }
    }
    
    # Summary
    Write-Log "Upload Summary:"
    Write-Log "Successfully uploaded: $($uploadedFiles.Count) files"
    if ($uploadedFiles.Count -gt 0) {
        foreach ($file in $uploadedFiles) {
            Write-Log "  ✓ $file" "SUCCESS"
        }
    }
    
    if ($failedFiles.Count -gt 0) {
        Write-Log "Failed uploads: $($failedFiles.Count) files" "ERROR"
        foreach ($file in $failedFiles) {
            Write-Log "  ✗ $file" "ERROR"
        }
    }
    
    # Create a verification script
    $verificationScript = @"
# Verification script to test configuration downloads
# Run this script to verify that configurations can be downloaded from S3

`$s3Bucket = "$S3Bucket"
`$s3Prefix = "$S3Prefix"
`$region = "$Region"

Write-Host "Testing configuration downloads from S3..."

"@
    
    foreach ($file in $uploadedFiles) {
        $clientTag = $file.Replace("_PRD.json", "")
        $verificationScript += @"

# Test $clientTag configuration
Write-Host "Testing $clientTag configuration..."
aws s3 cp "s3://`$s3Bucket/`$s3Prefix/$file" "test-$file" --region `$region
if (`$LASTEXITCODE -eq 0) {
    `$config = Get-Content "test-$file" -Raw | ConvertFrom-Json
    Write-Host "✓ $clientTag config downloaded and parsed successfully"
    Write-Host "  Business: `$(`$config.businessName)"
    Write-Host "  Unit: `$(`$config.business_unit)"
    Write-Host "  Tag: `$(`$config.client_tag)"
    Remove-Item "test-$file" -Force
} else {
    Write-Host "✗ Failed to download $clientTag configuration"
}
"@
    }
    
    $verificationScriptPath = "verify-s3-configs.ps1"
    $verificationScript | Out-File -FilePath $verificationScriptPath -Encoding UTF8
    Write-Log "Created verification script: $verificationScriptPath"
    
    # Display usage information
    Write-Log ""
    Write-Log "Configuration files uploaded successfully!"
    Write-Log ""
    Write-Log "Usage in user data template:"
    Write-Log "1. Update the S3 bucket name in user-data-template.ps1:"
    Write-Log "   `$s3Bucket = `"$S3Bucket`""
    Write-Log ""
    Write-Log "2. Ensure EC2 instances have IAM permissions to read from S3:"
    Write-Log "   - s3:GetObject on arn:aws:s3:::$S3Bucket/$S3Prefix/*"
    Write-Log ""
    Write-Log "3. Available client tags:"
    foreach ($file in $uploadedFiles) {
        $clientTag = $file.Replace("_PRD.json", "")
        Write-Log "   - $clientTag (from $file)"
    }
    Write-Log ""
    Write-Log "4. Test the configuration download with:"
    Write-Log "   .\$verificationScriptPath"
    
    if ($failedFiles.Count -gt 0) {
        Write-Log ""
        Write-Log "Some files failed to upload. Please check the errors above and retry." "WARNING"
        exit 1
    }
    
} catch {
    Write-Log "Script execution failed: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.Exception.StackTrace)" "ERROR"
    exit 1
}

Write-Log "Business configuration upload completed successfully!"
