import json

print "${ebx_release_id}"

ebx_release_vars = releaseApi.getVariables(releaseVariables["ebx_release_id"])

for r in ebx_release_vars:
    if r._delegate.key == "ebx_response_status_code":
        ebx_response_status_code = r._delegate.value
        print ebx_response_status_code  #  r._delegate.key +"="+ ebx_subflow_success
        if ebx_response_status_code == "201":
            # releaseVariables["ebx_insert_success"] = True
            releaseVariables["ebx_subflow_success"] = True
            # releaseVariables["ebx_update_success"] = False
        elif ebx_response_status_code == "204":
            # releaseVariables["ebx_update_success"] = True
            releaseVariables["ebx_subflow_success"] = True
            # releaseVariables["ebx_insert_success"] = False
        else:
            # releaseVariables["ebx_update_success"] = False
            releaseVariables["ebx_subflow_success"] = False
            # releaseVariables["ebx_insert_success"] = False
    else:
        pass