[tool.poetry]
name = "dcs-configsvc"
version = "0.9.1"
description = "DCS Config Management Service by BCX DevOps team"
authors = ["marcel-vermeulen <<EMAIL>>"]
license = "MIT"
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"

[tool.poetry.scripts]
start = "app.main:app"

[tool.poetry.dev-dependencies]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
