# Install Common Software
# This script installs commonly used software packages

Write-Host "Installing common software packages..."

# Set TLS 1.2 for downloads
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

# Install Chocolatey package manager
Write-Host "Installing Chocolatey..."
try {
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    Write-Host "Chocolatey installed successfully."
} catch {
    Write-Host "Failed to install Chocolatey: $($_.Exception.Message)"
}

# Refresh environment variables
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

# Install common software using Chocolatey
$softwarePackages = @(
    "7zip",
    "notepadplusplus",
    "googlechrome",
    "firefox",
    "putty",
    "winscp",
    "sysinternals",
    "git",
    "powershell-core",
    "vscode",
    "dotnetfx",
    "vcredist-all"
)

foreach ($package in $softwarePackages) {
    Write-Host "Installing $package..."
    try {
        choco install $package -y --no-progress
        Write-Host "$package installed successfully."
    } catch {
        Write-Host "Failed to install $package : $($_.Exception.Message)"
    }
}

# Install Microsoft Visual C++ Redistributables manually (backup method)
Write-Host "Installing Visual C++ Redistributables..."
try {
    $vcRedistUrls = @(
        "https://aka.ms/vs/17/release/vc_redist.x64.exe",
        "https://aka.ms/vs/17/release/vc_redist.x86.exe"
    )
    
    foreach ($url in $vcRedistUrls) {
        $fileName = Split-Path $url -Leaf
        $filePath = "$env:TEMP\$fileName"
        
        Invoke-WebRequest -Uri $url -OutFile $filePath
        Start-Process -FilePath $filePath -ArgumentList "/quiet" -Wait
        Remove-Item $filePath -Force
    }
    
    Write-Host "Visual C++ Redistributables installed."
} catch {
    Write-Host "Failed to install Visual C++ Redistributables: $($_.Exception.Message)"
}

# Install Windows Management Framework 5.1 (PowerShell 5.1) if needed
$psVersion = $PSVersionTable.PSVersion.Major
if ($psVersion -lt 5) {
    Write-Host "Installing Windows Management Framework 5.1..."
    try {
        $wmfUrl = "https://download.microsoft.com/download/6/F/5/6F5FF66C-6775-42B0-86C4-47D41F2DA187/Win8.1AndW2K12R2-KB3191564-x64.msu"
        $wmfPath = "$env:TEMP\Win8.1AndW2K12R2-KB3191564-x64.msu"
        
        Invoke-WebRequest -Uri $wmfUrl -OutFile $wmfPath
        Start-Process -FilePath "wusa.exe" -ArgumentList "$wmfPath /quiet /norestart" -Wait
        Remove-Item $wmfPath -Force
        
        Write-Host "Windows Management Framework 5.1 installed."
    } catch {
        Write-Host "Failed to install Windows Management Framework 5.1: $($_.Exception.Message)"
    }
} else {
    Write-Host "PowerShell version $psVersion is already installed."
}

# Install Windows Server Administration Tools
Write-Host "Installing Remote Server Administration Tools..."
try {
    $rsatFeatures = @(
        "RSAT-AD-Tools",
        "RSAT-DNS-Server",
        "RSAT-DFS-Mgmt-Con",
        "RSAT-File-Services",
        "RSAT-NPS",
        "RSAT-RemoteAccess",
        "RSAT-ADCS",
        "RSAT-ADCS-Mgmt"
    )
    
    foreach ($feature in $rsatFeatures) {
        try {
            Install-WindowsFeature -Name $feature -IncludeAllSubFeature
            Write-Host "RSAT feature $feature installed."
        } catch {
            Write-Host "Failed to install RSAT feature $feature : $($_.Exception.Message)"
        }
    }
} catch {
    Write-Host "RSAT installation failed: $($_.Exception.Message)"
}

# Install BGInfo for system information display
Write-Host "Installing BGInfo..."
try {
    $bginfoUrl = "https://download.sysinternals.com/files/BGInfo.zip"
    $bginfoZip = "$env:TEMP\BGInfo.zip"
    $bginfoDir = "C:\Tools\BGInfo"
    
    New-Item -ItemType Directory -Path $bginfoDir -Force
    Invoke-WebRequest -Uri $bginfoUrl -OutFile $bginfoZip
    Expand-Archive -Path $bginfoZip -DestinationPath $bginfoDir -Force
    Remove-Item $bginfoZip -Force
    
    # Create BGInfo configuration
    $bginfoConfig = @"
<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Fields>
        <Field>Computer Name</Field>
        <Field>IP Address</Field>
        <Field>OS Version</Field>
        <Field>Boot Time</Field>
        <Field>Logon Server</Field>
        <Field>CPU</Field>
        <Field>Memory</Field>
    </Fields>
</Configuration>
"@
    $bginfoConfig | Out-File -FilePath "$bginfoDir\bginfo.bgi" -Encoding UTF8
    
    Write-Host "BGInfo installed successfully."
} catch {
    Write-Host "Failed to install BGInfo: $($_.Exception.Message)"
}

Write-Host "Common software installation completed."
