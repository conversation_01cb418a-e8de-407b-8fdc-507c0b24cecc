#!/bin/sh

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Initial check for required files ---
echo "Verifying build environment..."
if [ ! -f "iac-tf-pwsh.Dockerfile" ] || [ ! -f "iac-api.Dockerfile" ]; then
    echo "Error: Required <PERSON><PERSON><PERSON><PERSON> not found. Please run this script from the root of the project."
    exit 1
fi
echo "Environment check passed."

# --- Step 1: Define variables ---
IAC_CLI_IMAGE="bcxslm/iac-automation:iac-cli-dev"
IAC_API_IMAGE="bcxslm/iac-automation:iac-api-dev"
REGISTRY="ghcr.io/bcxslm/iac-automation"

# --- Step 2: Build the Docker Images ---
echo "Building Docker images..."
docker build -f iac-tf-pwsh.Dockerfile -t "${IAC_CLI_IMAGE}" .
docker build -f iac-api.Dockerfile -t "${IAC_API_IMAGE}" .

# --- Step 3: Tag and Push the Images to a Registry ---
echo "Tagging and pushing images to registry..."
docker tag "${IAC_CLI_IMAGE}" "${REGISTRY}:iac-cli-dev"
docker tag "${IAC_API_IMAGE}" "${REGISTRY}:iac-api-dev"

docker push "${REGISTRY}:iac-cli-dev"
docker push "${REGISTRY}:iac-api-dev"

echo "Build process completed successfully!"