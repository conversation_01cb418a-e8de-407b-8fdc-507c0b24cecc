# MariaDB Galera Cluster Environment Variables
# Copy this file to .env and modify the values according to your setup

# Database Configuration
MYSQL_ROOT_PASSWORD=rootpassword123
MYSQL_DATABASE=testdb
MYSQL_USER=appuser
MYSQL_PASSWORD=apppassword123

# Host IP Addresses - Replace with your actual server IPs
HOST1_IP=*************
HOST2_IP=*************

# Galera Cluster Configuration
CLUSTER_NAME=mariadb_galera_cluster

# SST (State Snapshot Transfer) Configuration
SST_USER=sst_user
SST_PASSWORD=sst_password

# Monitoring Configuration
MONITOR_USER=monitor
MONITOR_PASSWORD=monitor_password

# Replication Configuration
REPL_USER=repl_user
REPL_PASSWORD=repl_password

# Network Configuration
GALERA_SUBNET=**********/16
GALERA_GATEWAY=**********
NODE1_IP=**********0
NODE2_IP=***********
