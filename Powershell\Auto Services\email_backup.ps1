param(
    [Parameter(Mandatory=$true)]
    [string]$ReferenceNumber,
    
    [Parameter(Mandatory=$true)]
    [string]$VMName,
    
    [Parameter(Mandatory=$true)]
    [string]$IPAddress,

    [Parameter(Mandatory=$true)]
    [string]$Envoronment

)

#Variables
$Subject = "VM Deployment Notification"
$body = @"
Good day Backup Team,

This is an automated notification regarding the VM deployment.

VM Deployment Details:

Reference Number: $ReferenceNumber
VM Name: $VMName
IP Address: $IPAddress
Timestamp: $((Get-Date -Format 'yyyy-MM-dd HH:mm:ss'))

Please add the following server to the backup schedule.

Thank you,
DevOps Team
"@

if ($Envoronment -eq "production" -or $Envoronment -eq "prd") {
    try {
        Send-MailMessage -SmtpServer $global:SMTPServer -Port $global:SMTPPort -From $global:SenderEmail -To $global:BackupRecipientEmail -Cc $global:BackupCCEmail -Subject $Subject -Body $body -Encoding UTF8
        Write-Host "Email sent successfully for reference: $ReferenceNumber" -ForegroundColor Green
    }
    catch {
        Write-Warning "Failed to send email: $($_.Exception.Message)"
        Write-Host "Continuing execution despite email failure for reference: $ReferenceNumber" -ForegroundColor Yellow
    }
}
else {
    Write-Host "Skipping email notification for non-production environment: $Envoronment" -ForegroundColor Yellow
}
