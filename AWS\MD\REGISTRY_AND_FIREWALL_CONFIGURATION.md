# Registry Tweaks and Firewall Rules Configuration

This document explains the registry optimizations and firewall rules applied to the Windows Server 2022 base image.

## Registry Tweaks Component (`ConfigureRegistryTweaks`)

### Performance Optimizations

#### Windows Error Reporting
- **Registry Path**: `HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting`
- **Setting**: `Disabled = 1`
- **Purpose**: Disables automatic error reporting to Microsoft, reducing network traffic and improving privacy

#### Customer Experience Improvement Program
- **Registry Path**: `HKLM:\SOFTWARE\Microsoft\SQMClient\Windows`
- **Setting**: `CEIPEnable = 0`
- **Purpose**: Disables telemetry collection, improving performance and privacy

#### Visual Effects Optimization
- **Registry Path**: `HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects`
- **Setting**: `VisualFXSetting = 2` (Adjust for performance)
- **Purpose**: Optimizes visual effects for server performance over appearance

#### Service Startup Optimization
- **Themes Service**: `Start = 4` (Disabled)
- **Tablet Input Service**: `Start = 4` (Disabled)
- **Purpose**: Disables unnecessary services to improve boot time and reduce resource usage

### Security Configurations

#### Windows Defender
- **Registry Path**: `HKLM:\SOFTWARE\Microsoft\Windows Defender\Real-Time Protection`
- **Setting**: `DisableRealtimeMonitoring = 0` (Enabled)
- **Purpose**: Ensures real-time protection is enabled

#### AutoRun Security
- **Registry Path**: `HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer`
- **Setting**: `NoDriveTypeAutoRun = 255` (All drives disabled)
- **Purpose**: Prevents automatic execution of programs from removable media

#### User Account Control (UAC)
- **Registry Path**: `HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System`
- **Settings**:
  - `EnableLUA = 1` (UAC enabled)
  - `ConsentPromptBehaviorAdmin = 2` (Prompt for consent)
- **Purpose**: Maintains security through UAC prompts

### Network Optimizations

#### TCP Performance
- **Registry Path**: `HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters`
- **Settings**:
  - `TcpAckFrequency = 1` (Immediate ACK)
  - `TCPNoDelay = 1` (Disable Nagle algorithm)
  - `TcpTimedWaitDelay = 30` (Reduce TIME_WAIT)
- **Purpose**: Optimizes TCP performance for server workloads

#### IPv6 Configuration
- **Registry Path**: `HKLM:\SYSTEM\CurrentControlSet\Services\Tcpip6\Parameters`
- **Setting**: `DisabledComponents = 32` (Prefer IPv4)
- **Purpose**: Prioritizes IPv4 in enterprise environments where IPv6 may not be fully deployed

#### DNS Optimization
- **Registry Path**: `HKLM:\SYSTEM\CurrentControlSet\Services\Dnscache\Parameters`
- **Settings**:
  - `MaxCacheTtl = 86400` (24 hours)
  - `MaxNegativeCacheTtl = 300` (5 minutes)
- **Purpose**: Optimizes DNS caching for better performance

### System Optimizations

#### Hibernation
- **Registry Path**: `HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Power`
- **Setting**: `HibernateEnabled = 0`
- **Purpose**: Disables hibernation to save disk space (servers typically don't hibernate)

#### Memory Management
- **Registry Path**: `HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management`
- **Settings**:
  - `ClearPageFileAtShutdown = 0` (Don't clear for faster boot)
  - `DisablePagingExecutive = 1` (Keep kernel in memory)
- **Purpose**: Optimizes memory management for server performance

#### Boot Optimization
- **Registry Path**: `HKLM:\SYSTEM\CurrentControlSet\Control`
- **Settings**:
  - `WaitToKillServiceTimeout = 2000` (2 seconds)
  - `ServicesPipeTimeout = 60000` (60 seconds)
- **Purpose**: Reduces shutdown/startup times

#### Windows Update
- **Registry Path**: `HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU`
- **Settings**:
  - `NoAutoUpdate = 0` (Enable updates)
  - `AUOptions = 3` (Download and notify for install)
- **Purpose**: Configures controlled update behavior for enterprise environments

#### Event Log Configuration
- **Application Log**: 100MB, no auto-archive
- **System Log**: 100MB, no auto-archive
- **Security Log**: 200MB, no auto-archive
- **Purpose**: Increases log sizes for better monitoring and troubleshooting

## Firewall Rules Component (`ConfigureFirewallRules`)

### Basic Security Configuration

#### Firewall State
- **All Profiles**: Enabled (Domain, Private, Public)
- **Default Policy**: Block inbound, Allow outbound
- **Logging**: Enabled for dropped connections

### Remote Management Rules

#### Remote Desktop Protocol (RDP)
- **Ports**: 3389 (TCP/UDP)
- **Purpose**: Essential for Windows Server remote management
- **Security**: Should be restricted to management networks in production

#### Windows Remote Management (WinRM)
- **Ports**: 5985 (HTTP), 5986 (HTTPS)
- **Purpose**: PowerShell remoting and management automation
- **Security**: HTTPS preferred for production environments

#### ICMP (Ping)
- **Protocol**: ICMPv4/ICMPv6 Echo Request
- **Purpose**: Network diagnostics and connectivity testing

### Web Server Rules

#### HTTP/HTTPS
- **Ports**: 80 (HTTP), 443 (HTTPS)
- **Purpose**: Web server traffic
- **Security**: HTTPS should be preferred for production

#### Common Web Application Ports
- **Ports**: 8080, 8443
- **Purpose**: Alternative web application ports
- **Usage**: Development and non-standard web applications

### Database Server Rules

#### SQL Server
- **Ports**: 
  - 1433 (TCP) - Database Engine
  - 1434 (UDP) - Browser Service
  - 2383 (TCP) - Analysis Services
- **Purpose**: Microsoft SQL Server connectivity
- **Security**: Should be restricted to application servers only

### Active Directory Rules

#### DNS
- **Ports**: 53 (TCP/UDP)
- **Purpose**: Domain Name System resolution
- **Critical**: Essential for domain-joined servers

#### LDAP/LDAPS
- **Ports**: 389 (LDAP), 636 (LDAPS)
- **Purpose**: Active Directory authentication and queries
- **Security**: LDAPS preferred for sensitive environments

#### Global Catalog
- **Ports**: 3268 (GC), 3269 (GC SSL)
- **Purpose**: Active Directory global catalog access
- **Usage**: Multi-domain environments

#### Kerberos
- **Ports**: 88 (TCP/UDP)
- **Purpose**: Authentication protocol
- **Critical**: Essential for domain authentication

### System Service Rules

#### RPC Endpoint Mapper
- **Port**: 135 (TCP)
- **Purpose**: Remote Procedure Call services
- **Usage**: Various Windows services and applications

#### NetBIOS
- **Ports**: 137 (UDP), 138 (UDP), 139 (TCP)
- **Purpose**: Legacy Windows networking
- **Note**: May be disabled in modern environments

#### SMB/CIFS
- **Port**: 445 (TCP)
- **Purpose**: File and printer sharing
- **Security**: Should be carefully controlled

#### SNMP
- **Port**: 161 (UDP)
- **Purpose**: Network monitoring
- **Usage**: Monitoring systems integration

### Security Rules

#### Blocked Ports
- **Telnet**: 23 (TCP) - Insecure protocol
- **FTP**: 21 (TCP) - Insecure file transfer
- **TFTP**: 69 (UDP) - Insecure file transfer
- **SNMP Trap**: 162 (UDP) - Potential security risk

#### Time Synchronization
- **Port**: 123 (UDP) - NTP
- **Purpose**: Time synchronization with domain controllers
- **Critical**: Essential for Kerberos authentication

#### DHCP Client
- **Ports**: 67/68 (UDP)
- **Purpose**: Dynamic IP address assignment
- **Usage**: When using DHCP for IP configuration

### Outbound Rules

#### Essential Outbound Traffic
- **HTTP/HTTPS**: 80/443 - Updates and management
- **DNS**: 53 - Name resolution
- **LDAP/LDAPS**: 389/636 - Domain authentication
- **Kerberos**: 88 - Authentication
- **NTP**: 123 - Time synchronization

## Security Considerations

### Production Recommendations

1. **Restrict RDP Access**: Limit to management networks only
2. **Use HTTPS**: Prefer encrypted protocols (HTTPS, LDAPS, WinRM HTTPS)
3. **Monitor Logs**: Review firewall logs regularly
4. **Least Privilege**: Only open required ports for specific services
5. **Network Segmentation**: Use security groups and NACLs for additional protection

### Compliance Benefits

1. **CIS Benchmarks**: Many settings align with CIS Windows Server benchmarks
2. **Security Baselines**: Follows Microsoft security recommendations
3. **Audit Readiness**: Enhanced logging for compliance requirements
4. **Defense in Depth**: Multiple layers of security controls

## Validation and Monitoring

### Registry Validation
The component validates key registry settings:
- Windows Error Reporting disabled
- UAC enabled
- AutoRun disabled
- Critical security settings applied

### Firewall Validation
The component verifies:
- Firewall enabled on all profiles
- Required rules present
- Logging configured
- Default policies set correctly

### Ongoing Monitoring
- Review firewall logs: `%systemroot%\system32\LogFiles\Firewall\pfirewall.log`
- Monitor event logs for security events
- Regular security assessments
- Update rules as business requirements change

## Customization

### Adding Custom Registry Tweaks
1. Edit `components/configure-registry-tweaks.yml`
2. Add new PowerShell commands in appropriate phase
3. Include validation in the validate phase
4. Test thoroughly before production deployment

### Adding Custom Firewall Rules
1. Edit `components/configure-firewall-rules.yml`
2. Add netsh commands for new rules
3. Update validation to check for new rules
4. Document business justification for new rules

### Business-Specific Modifications
Consider creating separate components for:
- Industry-specific compliance requirements
- Custom application ports
- Specialized security configurations
- Performance tuning for specific workloads
