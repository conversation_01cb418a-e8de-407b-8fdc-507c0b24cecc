import json

# Initialize default values
paramAddRecord = ''
paramRemoveRecord = ''
updateMessageReceived = ''
deleteRecord = False
addRecord = False
updateRecord = False
dnsDevURL = 'https://ad-api.dev.mud.internal.co.za/webhook/v1?'
key = 'key=18adecad-df09-47e2-ae05-de7686ae3156'
script = '&script=dnschange.ps1'
processor = r'dev\svcDevDnsAutomation'
devuser = r'svcDevSlmDcsRelease'
record_type = releaseVariables['record_type']
category = releaseVariables['category']

# Validate required fields
required_fields = ['record_type', 'category']
for field in required_fields:
    if field not in releaseVariables:
        raise Exception('Missing required field: {0}'.format(field))

if releaseVariables['record_type'] == 'C-Record' and releaseVariables['category'] == 'Update Record':
    updateMessageReceived = 'We will {0} of type {1} from {2} - {3} to {4} - {5}'.format(
        releaseVariables['category'],
        releaseVariables['record_type'],
        releaseVariables['host_alias'],
        releaseVariables['hostname'],
        releaseVariables['new_host_alias'],
        releaseVariables['new_hostname']
    )
    paramRemoveRecord = '&param=-reference {0} -requester {1} -processor {2} -task RemoveC -source {3} -destination {4} -sequence 0'.format(
        releaseVariables['jira_ref'],
        releaseVariables['folder.devuser'],
        releaseVariables['folder.processor'],
        releaseVariables['source'],
        releaseVariables['destination']
    )
    paramAddRecord = '&param=-reference {0} -requester {1} -processor {2} -task AddC -source {3} -destination {4} -sequence 1'.format(
        releaseVariables['jira_ref'],
        releaseVariables['folder.devuser'],
        releaseVariables['folder.processor'],
        releaseVariables['source'],
        releaseVariables['destination']
    )
elif releaseVariables['record_type'] == 'C-Record' and releaseVariables['category'] == 'Remove Record':
    updateMessageReceived = 'We will {0} of type {1} - {2} pointing to {3}'.format(
        releaseVariables['category'],
        releaseVariables['record_type'],
        releaseVariables['host_alias'],
        releaseVariables['hostname']
    )
    paramRemoveRecord = '&param=-reference {0} -requester {1} -processor {2} -task RemoveC -source {3} -destination {4}'.format(
        releaseVariables['jira_ref'],
        releaseVariables['folder.devuser'],
        releaseVariables['folder.processor'],
        releaseVariables['source'],
        releaseVariables['destination']
    )
elif releaseVariables['record_type'] == 'C-Record' and releaseVariables['category'] == 'Add Record':
    updateMessageReceived = 'We will {0} of type {1} - {2} pointing to {3}'.format(
        releaseVariables['category'],
        releaseVariables['record_type'],
        releaseVariables['host_alias'],
        releaseVariables['hostname']
    )
    paramAddRecord = '&param=-reference {0} -requester {1} -processor {2} -task AddC -source {3} -destination {4}'.format(
        releaseVariables['jira_ref'],
        releaseVariables['folder.devuser'],
        releaseVariables['folder.processor'],
        releaseVariables['source'],
        releaseVariables['destination']
    )
elif releaseVariables['record_type'] == 'A-Record' and releaseVariables['category'] == 'Update Record':
    updateMessageReceived = 'We will {0} of type {1} from {2} - {3} to {4} - {5}'.format(
        releaseVariables['category'],
        releaseVariables['record_type'],
        releaseVariables['hostname'],
        releaseVariables['ip_address'],
        releaseVariables['new_hostname'],
        releaseVariables['new_ip_address']
    )
    paramRemoveRecord = '&param=-reference {0} -requester {1} -processor {2} -task RemoveA -source {3} -destination {4} -sequence 0'.format(
        releaseVariables['jira_ref'],
        releaseVariables['folder.devuser'],
        releaseVariables['folder.processor'],
        releaseVariables['source'],
        releaseVariables['destination']
    )
    paramAddRecord = '&param=-reference {0} -requester {1} -processor {2} -task AddA -source {3} -destination {4} -sequence 1'.format(
        releaseVariables['jira_ref'],
        releaseVariables['folder.devuser'],
        releaseVariables['folder.processor'],
        releaseVariables['source'],
        releaseVariables['destination']
    )
elif releaseVariables['record_type'] == 'A-Record' and releaseVariables['category'] == 'Remove Record':
    updateMessageReceived = 'We will {0} of type {1} - {2} with an IP of {3}'.format(
        releaseVariables['category'],
        releaseVariables['record_type'],
        releaseVariables['hostname'],
        releaseVariables['ip_address']
    )
    paramRemoveRecord = '&param=-reference {0} -requester {1} -processor {2} -task RemoveA -source {3} -destination {4}'.format(
        releaseVariables['jira_ref'],
        releaseVariables['folder.devuser'],
        releaseVariables['folder.processor'],
        releaseVariables['source'],
        releaseVariables['destination']
    )
elif releaseVariables['record_type'] == 'A-Record' and releaseVariables['category'] == 'Add Record':
    updateMessageReceived = 'We will {0} of type {1} - {2} with an IP of {3}'.format(
        releaseVariables['category'],
        releaseVariables['record_type'],
        releaseVariables['hostname'],
        releaseVariables['ip_address']
    )
    paramAddRecord = '&param=-reference {0} -requester {1} -processor {2} -task AddA -source {3} -destination {4}'.format(
        releaseVariables['jira_ref'],
        devuser,
        processor,
        releaseVariables['hostname'],
        releaseVariables['ip_address']
    )
else:
    updateMessageReceived = 'This request has missing information! Unable to proceed.'
    raise ValueError('This request has missing information! Unable to proceed.')

releaseVariables['processor'] = processor
releaseVariables['updateMessageReceived'] = updateMessageReceived
releaseVariables['updateRecord'] = updateRecord
releaseVariables['addRecord'] = addRecord
releaseVariables['deleteRecord'] = deleteRecord
releaseVariables['paramAddRecord'] = paramAddRecord
releaseVariables['paramRemoveRecord'] = paramRemoveRecord

final_dnsRemoveDevURL = dnsDevURL + key + script + paramRemoveRecord if paramRemoveRecord else ''
final_dnsAddDevURL = dnsDevURL + key + script + paramAddRecord if paramAddRecord else ''

releaseVariables['dnsDevURL'] = dnsDevURL
releaseVariables['key'] = key
releaseVariables['script'] = script
releaseVariables['processor'] = processor
releaseVariables['final_dnsRemoveDevURL'] = final_dnsRemoveDevURL if final_dnsRemoveDevURL else ''
releaseVariables['final_dnsAddDevURL'] = final_dnsAddDevURL if final_dnsAddDevURL else ''