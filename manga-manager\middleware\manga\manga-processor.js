const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { exec } = require('child_process');
const execAsync = promisify(exec);
const sharp = require('sharp');
const { Chapter, Page } = require('../../backend/models');

/**
 * Process manga files (CBR, CBZ, or image folders)
 * @param {string} filePath - Path to the manga file
 * @param {string} mangaId - ID of the manga
 * @param {string} outputDir - Directory to extract files to
 */
const processMangaFile = async (filePath, mangaId, outputDir) => {
  try {
    const fileExt = path.extname(filePath).toLowerCase();
    
    // Create chapters directory
    const chaptersDir = path.join(outputDir, 'chapters');
    if (!fs.existsSync(chaptersDir)) {
      fs.mkdirSync(chaptersDir, { recursive: true });
    }
    
    // Process based on file type
    if (fileExt === '.cbr' || fileExt === '.rar') {
      await processCBR(filePath, mangaId, chaptersDir);
    } else if (fileExt === '.cbz' || fileExt === '.zip') {
      await processCBZ(filePath, mangaId, chaptersDir);
    } else if (fs.statSync(filePath).isDirectory()) {
      await processImageFolder(filePath, mangaId, chaptersDir);
    } else {
      // Single image file
      await processSingleImage(filePath, mangaId, chaptersDir);
    }
    
    console.log(`Processed manga file: ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error processing manga file: ${filePath}`, error);
    throw error;
  }
};

/**
 * Process CBR file
 * @param {string} filePath - Path to the CBR file
 * @param {string} mangaId - ID of the manga
 * @param {string} chaptersDir - Directory to extract chapters to
 */
const processCBR = async (filePath, mangaId, chaptersDir) => {
  try {
    // Create chapter directory
    const chapterNumber = 1; // Default for single file
    const chapterDir = path.join(chaptersDir, chapterNumber.toString());
    
    if (!fs.existsSync(chapterDir)) {
      fs.mkdirSync(chapterDir, { recursive: true });
    }
    
    // Extract CBR file
    await execAsync(`unrar x "${filePath}" "${chapterDir}"`);
    
    // Create chapter in database
    const chapter = await Chapter.create({
      mangaId,
      chapterNumber,
      title: `Chapter ${chapterNumber}`
    });
    
    // Process extracted images
    await processExtractedImages(chapterDir, chapter.id);
  } catch (error) {
    console.error(`Error processing CBR file: ${filePath}`, error);
    throw error;
  }
};

/**
 * Process CBZ file
 * @param {string} filePath - Path to the CBZ file
 * @param {string} mangaId - ID of the manga
 * @param {string} chaptersDir - Directory to extract chapters to
 */
const processCBZ = async (filePath, mangaId, chaptersDir) => {
  try {
    // Create chapter directory
    const chapterNumber = 1; // Default for single file
    const chapterDir = path.join(chaptersDir, chapterNumber.toString());
    
    if (!fs.existsSync(chapterDir)) {
      fs.mkdirSync(chapterDir, { recursive: true });
    }
    
    // Extract CBZ file
    await execAsync(`unzip -o "${filePath}" -d "${chapterDir}"`);
    
    // Create chapter in database
    const chapter = await Chapter.create({
      mangaId,
      chapterNumber,
      title: `Chapter ${chapterNumber}`
    });
    
    // Process extracted images
    await processExtractedImages(chapterDir, chapter.id);
  } catch (error) {
    console.error(`Error processing CBZ file: ${filePath}`, error);
    throw error;
  }
};

/**
 * Process image folder
 * @param {string} folderPath - Path to the image folder
 * @param {string} mangaId - ID of the manga
 * @param {string} chaptersDir - Directory to extract chapters to
 */
const processImageFolder = async (folderPath, mangaId, chaptersDir) => {
  try {
    // Check if folder contains subfolders (chapters)
    const items = fs.readdirSync(folderPath);
    const subfolders = items.filter(item => {
      const itemPath = path.join(folderPath, item);
      return fs.statSync(itemPath).isDirectory();
    });
    
    if (subfolders.length > 0) {
      // Process each subfolder as a chapter
      for (let i = 0; i < subfolders.length; i++) {
        const subfolder = subfolders[i];
        const subfolderPath = path.join(folderPath, subfolder);
        
        // Create chapter directory
        const chapterNumber = i + 1;
        const chapterDir = path.join(chaptersDir, chapterNumber.toString());
        
        if (!fs.existsSync(chapterDir)) {
          fs.mkdirSync(chapterDir, { recursive: true });
        }
        
        // Create chapter in database
        const chapter = await Chapter.create({
          mangaId,
          chapterNumber,
          title: `Chapter ${chapterNumber}`
        });
        
        // Copy images to chapter directory
        const images = fs.readdirSync(subfolderPath).filter(file => {
          const ext = path.extname(file).toLowerCase();
          return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);
        });
        
        for (let j = 0; j < images.length; j++) {
          const image = images[j];
          const imagePath = path.join(subfolderPath, image);
          const destPath = path.join(chapterDir, `${j + 1}${path.extname(image)}`);
          
          fs.copyFileSync(imagePath, destPath);
          
          // Create page in database
          await Page.create({
            chapterId: chapter.id,
            pageNumber: j + 1,
            filePath: `${j + 1}${path.extname(image)}`
          });
        }
      }
    } else {
      // Process folder as a single chapter
      const chapterNumber = 1;
      const chapterDir = path.join(chaptersDir, chapterNumber.toString());
      
      if (!fs.existsSync(chapterDir)) {
        fs.mkdirSync(chapterDir, { recursive: true });
      }
      
      // Create chapter in database
      const chapter = await Chapter.create({
        mangaId,
        chapterNumber,
        title: `Chapter ${chapterNumber}`
      });
      
      // Copy images to chapter directory
      const images = fs.readdirSync(folderPath).filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);
      });
      
      for (let i = 0; i < images.length; i++) {
        const image = images[i];
        const imagePath = path.join(folderPath, image);
        const destPath = path.join(chapterDir, `${i + 1}${path.extname(image)}`);
        
        fs.copyFileSync(imagePath, destPath);
        
        // Create page in database
        await Page.create({
          chapterId: chapter.id,
          pageNumber: i + 1,
          filePath: `${i + 1}${path.extname(image)}`
        });
      }
    }
  } catch (error) {
    console.error(`Error processing image folder: ${folderPath}`, error);
    throw error;
  }
};

/**
 * Process single image
 * @param {string} filePath - Path to the image file
 * @param {string} mangaId - ID of the manga
 * @param {string} chaptersDir - Directory to extract chapters to
 */
const processSingleImage = async (filePath, mangaId, chaptersDir) => {
  try {
    // Create chapter directory
    const chapterNumber = 1;
    const chapterDir = path.join(chaptersDir, chapterNumber.toString());
    
    if (!fs.existsSync(chapterDir)) {
      fs.mkdirSync(chapterDir, { recursive: true });
    }
    
    // Create chapter in database
    const chapter = await Chapter.create({
      mangaId,
      chapterNumber,
      title: `Chapter ${chapterNumber}`
    });
    
    // Copy image to chapter directory
    const destPath = path.join(chapterDir, `1${path.extname(filePath)}`);
    fs.copyFileSync(filePath, destPath);
    
    // Create page in database
    await Page.create({
      chapterId: chapter.id,
      pageNumber: 1,
      filePath: `1${path.extname(filePath)}`
    });
  } catch (error) {
    console.error(`Error processing single image: ${filePath}`, error);
    throw error;
  }
};

/**
 * Process extracted images
 * @param {string} directory - Directory containing extracted images
 * @param {string} chapterId - ID of the chapter
 */
const processExtractedImages = async (directory, chapterId) => {
  try {
    // Get all image files
    const files = fs.readdirSync(directory).filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);
    });
    
    // Sort files naturally (1, 2, 10 instead of 1, 10, 2)
    files.sort((a, b) => {
      const aNum = parseInt(a.match(/\d+/) || 0);
      const bNum = parseInt(b.match(/\d+/) || 0);
      return aNum - bNum;
    });
    
    // Process each image
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const filePath = path.join(directory, file);
      const newFileName = `${i + 1}${path.extname(file)}`;
      const newFilePath = path.join(directory, newFileName);
      
      // Optimize image
      await sharp(filePath)
        .resize(1200, null, { fit: 'inside', withoutEnlargement: true })
        .jpeg({ quality: 85 })
        .toFile(newFilePath);
      
      // Remove original file if different from new file
      if (file !== newFileName) {
        fs.unlinkSync(filePath);
      }
      
      // Create page in database
      await Page.create({
        chapterId,
        pageNumber: i + 1,
        filePath: newFileName
      });
    }
  } catch (error) {
    console.error(`Error processing extracted images: ${directory}`, error);
    throw error;
  }
};

module.exports = {
  processMangaFile
};