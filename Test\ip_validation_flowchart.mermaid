flowchart TD
    A[Start: validate_ip_request<br/>iprequest_valid = False<br/>allocate_ip = False] --> B{ip_range == NONE?}
    
    B -->|Yes| C[Add comment: No IP Address requested<br/>Set iprequest_valid = True]
    B -->|No| D{ip_range == OTHER?}
    
    D -->|Yes| E[Add comment: IP Address Allocation must be done manually<br/>Set iprequest_valid = True]
    D -->|No| F[Set allocate_ip = True]
    
    F --> G{object_description is empty?}
    G -->|Yes| H[Add comment: Value expected for Object Description]
    G -->|No| I{ip_range == POC?}
    
    I -->|Yes| J{object_type == CUSTOM AND<br/>POC NOT in object_name?}
    J -->|Yes| K[Add comment: POCxxxxxx expected as CUSTOM Object Name]
    J -->|No| L{object_type NOT in<br/>poc_allowed_objects?}
    
    L -->|Yes| M[Add comment: Only allowed objects in POC range]
    L -->|No| N[Add comment: Issuing IPAM address in POC range<br/>Set iprequest_valid = True]
    
    I -->|No| O{object_type == CUSTOM AND<br/>POC in object_name?}
    O -->|Yes| P[Add comment: Object name invalid for IP Range]
    O -->|No| Q[Add comment: Issuing IPAM address in range<br/>Set iprequest_valid = True]
    
    C --> R[Return: iprequest_valid, allocate_ip]
    E --> R
    H --> R
    K --> R
    M --> R
    N --> R
    P --> R
    Q --> R
    
    style A fill:#e1f5fe
    style R fill:#f3e5f5
    style C fill:#c8e6c9
    style E fill:#fff3e0
    style N fill:#c8e6c9
    style Q fill:#c8e6c9
    style H fill:#ffebee
    style K fill:#ffebee
    style M fill:#ffebee
    style P fill:#ffebee