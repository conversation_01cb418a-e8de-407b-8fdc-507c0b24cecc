<#
    .SYNOPSIS
    Updates the Splunk Agent.

    .DESCRIPTION
    Updates the Splunk Agent by using the latest msi available.

    .INPUTS
    appVersion      - Specify the Splunk agent version like 9.1.4
    serverName      - Specify the server name that you want to check.
    credentials     - Specify a credential file or object.
    serverList      - Specify a list of servers to update. This file should be in the route of scripts.
    skipConfigUpdate - Skip the configuration update step during the upgrade process.

    Example.
    To update the agent on a single server, specify the appVersion and serverName:
        .\Splunk-Agent-Update.ps1 -appVersion "9.1.4" -serverName "srv007321"

    To update the agent on a list of servers, populate the serverlist.txt and run it with the -serverList parameter:
        .\Update-SplunkAgent.ps1 -appVersion "9.1.4" -serverList

    To specify a credentials file, specify this at the end:
        .\Update-SplunkAgent.ps1 -appVersion "9.1.4" -serverName "srv007321" -credentials ".\creds.xml"

    To skip the configuration update step:
        .\Update-SplunkAgent.ps1 -appVersion "9.1.4" -serverName "srv007321" -skipConfigUpdate

    .NOTES
    File Name: Update-SplunkAgent.ps1
    Author: Rudi van Zyl
    Version: 1.4 - 04/07/2025

    .NOTES
    Version History:
    Version 1.4 - Added skipConfigUpdate parameter
    Version 1.3 - Fixed parameter handling and improved error handling
    Version 1.2 - Combined the Splunk service functions.
    Version 1.1 - Optimized Script
    Version 1.0 - Base Script.
#>
#requires -Version 5
#requires -RunAsAdministrator

param(
    [Parameter(Mandatory=$true,ValueFromPipeline=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$appVersion,
    
    [Parameter()]
    [ValidateNotNullOrEmpty()]
    [string]$serverName,
    
    [Parameter()]
    [ValidateNotNullOrEmpty()]
    [PSCredential]$credentials,
    
    [Parameter()]
    [switch]$serverListSwitch,
    
    [Parameter()]
    [ValidateNotNullOrEmpty()]
    [string]$serverListPath = ".\serverList.txt",
    
    [Parameter()]
    [switch]$skipConfigUpdate
)

### Variables ###
$Error.Clear()
$ErrorActionPreference = "Stop"
$baseURL = "http://pulp.mud.internal.co.za/pulp/software/splunk"
$date = Get-Date -Format "yyyy-MM-dd_HH-mm"
$logFile = "..\..\Logs\Splunk\SplunkUpdate_$date.log"
$errorLogFile = ".\SplunkUpdateErrors_$date.log"
$oldServer = "srv005466.mud.internal.co.za:8089"
$newServer = "srv010344.mud.internal.co.za:8089"

"Splunk Agent Update - Started at $(Get-Date)" | Out-File -FilePath $logFile

if (!$credentials){
    $credentials = Get-Credential -Message "Enter credentials for remote server access"
    "Credentials requested from user" | Out-File -FilePath $logFile -Append
}

function Write-Log {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter()]
        [ValidateSet("Info", "Warning", "Error")]
        [string]$Level = "Info"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "Info" { Write-Host $logMessage -ForegroundColor White }
        "Warning" { Write-Host $logMessage -ForegroundColor Yellow }
        "Error" { Write-Host $logMessage -ForegroundColor Red }
    }
    
    $logMessage | Out-File -FilePath $logFile -Append
    
    if ($Level -eq "Error") {
        $logMessage | Out-File -FilePath $errorLogFile -Append
    }
}

function Test-Input {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$appVersion,
        
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$appURL
    )

    try {
        Write-Log "Testing for Splunk version $appVersion at $appURL"
        $agentURL = "$appURL/$appVersion"
        $agentContent = Invoke-WebRequest -Uri $agentURL -UseBasicParsing -ErrorAction Stop

        if ($agentContent.StatusCode -ge 200 -and $agentContent.StatusCode -lt 300) {
            $agentMSI = $agentContent.Links | Where-Object { $_.href -like "*$appVersion*.msi" } | Select-Object -First 1

            if ($null -ne $agentMSI) {
                $global:finalAppURL = "$agentURL/$($agentMSI.href)"
                $global:agentMSI = $agentMSI.href
                Write-Log "Found MSI: $($agentMSI.href)"
            } else {
                throw "MSI file for version $appVersion not found."
            }
        } else {
            throw "Invalid response status code: $($agentContent.StatusCode)"
        }
    }
    catch {
        Write-Log "An error occurred during input testing: $_" -Level "Error"
        throw $_
    }
}

function Set-SplunkService {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$serverName,

        [Parameter(Mandatory = $true)]
        [ValidateSet("Start", "Stop")]
        [string]$action
    )

    try {
        Write-Log "Attempting to $action Splunk service on $serverName"
        $service = Get-Service -ComputerName $serverName -Name "SplunkForwarder" -ErrorAction Stop

        switch ($action) {
            "Start" {
                if ($service.Status -ne "Running") {
                    $service | Start-Service -ErrorAction Stop
                    $counter = 0
                    $maxRetries = 10
                    
                    while ($service.Status -ne "Running" -and ($counter -lt $maxRetries)) {
                        Write-Log "Service status: $($service.Status). Attempt $($counter+1) of $maxRetries"
                        Start-Sleep 2
                        $counter++
                        $service = Get-Service -ComputerName $serverName -Name "SplunkForwarder" -ErrorAction Stop
                    }
                    
                    if ($service.Status -eq "Running") {
                        Write-Log "The $($service.DisplayName) service is running." 
                    } else {
                        Write-Log "Failed to start the $($service.DisplayName) service after $maxRetries attempts." -Level "Error"
                        return $false
                    }
                } else {
                    Write-Log "The $($service.DisplayName) service is already running."
                }
            }
            "Stop" {
                if ($service.Status -ne "Stopped") {
                    $service | Stop-Service -Force -NoWait -ErrorAction Stop
                    $counter = 0
                    $maxRetries = 10
                    
                    while ($service.Status -ne "Stopped" -and ($counter -lt $maxRetries)) {
                        Write-Log "Service status: $($service.Status). Attempt $($counter+1) of $maxRetries"
                        Start-Sleep 2
                        $counter++
                        $service = Get-Service -ComputerName $serverName -Name "SplunkForwarder" -ErrorAction Stop
                    }
                    
                    if ($service.Status -eq "Stopped") {
                        Write-Log "The $($service.DisplayName) service has been stopped successfully."
                    } else {
                        Write-Log "Failed to stop the $($service.DisplayName) service after $maxRetries attempts." -Level "Error"
                        return $false
                    }
                } else {
                    Write-Log "The $($service.DisplayName) service is already stopped."
                }
            }
        }
        return $true
    }
    catch {
        Write-Log "An error occurred while managing the service: $_" -Level "Error"
        return $false
    }
}

Function Install-Splunk {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$serverName,

        [Parameter(Mandatory = $true)]
        [PSCredential]$credentials
    )
    
    $sourcePath = "C:\Temp\"
    $destinationPath = "\\$serverName\C$\Temp"
    $driveCreated = $false
    
    try {
        Write-Log "Starting Splunk installation on $serverName"
        
        if (-not (Test-Path -Path $sourcePath)) {
            New-Item -Path $sourcePath -ItemType Directory -Force | Out-Null
            Write-Log "Created local temp directory: $sourcePath"
        }
        
        New-PSDrive -Name "SourceServer" -PSProvider FileSystem -Root $sourcePath -ErrorAction Stop
        New-PSDrive -Name "DestinationServer" -PSProvider FileSystem -Root $destinationPath -Credential $credentials -ErrorAction Stop
        $driveCreated = $true
        Write-Log "Created network drives for installation"

        if (-not (Test-Path -Path "$sourcePath\$global:agentMSI")) {
            Write-Log "Downloading MSI file: $global:agentMSI"
            Invoke-WebRequest -Uri $global:finalAppURL -UseBasicParsing -OutFile "$sourcePath\$global:agentMSI" -ErrorAction Stop
            Write-Log "Download completed successfully"
        } else {
            Write-Log "MSI file already exists locally, skipping download"
        }

        if (-not (Test-Path -Path "DestinationServer:\$global:agentMSI")) {
            Write-Log "Copying MSI file to $serverName"
            Copy-Item -Path "SourceServer:\$global:agentMSI" -Destination "DestinationServer:\$global:agentMSI" -Recurse -ErrorAction Stop
            Write-Log "Copy completed successfully"
        } else {
            Write-Log "MSI file already exists on target server, skipping copy"
        }

        $installerPath = "C:\Temp\$global:agentMSI"
        Write-Log "Starting the installation process on $serverName"
        
        $result = Invoke-Command -ComputerName $serverName -Credential $credentials -ScriptBlock {
            param ($installerPath)
            
            try {
                $process = Start-Process -FilePath "msiexec" -ArgumentList "/i `"$installerPath`" AGREETOLICENSE=Yes /quiet DEPLOYMENT_SERVER=srv010344.mud.internal.co.za" -NoNewWindow -Wait -PassThru
                return @{
                    Success = ($process.ExitCode -eq 0)
                    ExitCode = $process.ExitCode
                }
            } catch {
                return @{
                    Success = $false
                    Error = $_.ToString()
                }
            }
        } -ArgumentList $installerPath -ErrorAction Stop

        if ($result.Success) {
            Write-Log "Installation completed successfully on $serverName"
            return $true
        } else {
            if ($result.ContainsKey('ExitCode')) {
                Write-Log "Installation failed with exit code: $($result.ExitCode)" -Level "Error"
            } else {
                Write-Log "Installation failed: $($result.Error)" -Level "Error"
            }
            return $false
        }
    }
    catch {
        Write-Log "An error occurred during installation: $_" -Level "Error"
        return $false
    }
    finally {
        if ($driveCreated) {
            Remove-PSDrive -Name "SourceServer" -ErrorAction SilentlyContinue
            Remove-PSDrive -Name "DestinationServer" -ErrorAction SilentlyContinue
            Write-Log "Removed network drives"
        }
    }
}

function Update-Config {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$serverName
    )
    
    $filePath = "\\$serverName\c$\Program Files\SplunkUniversalForwarder\etc\system\local\deploymentclient.conf"
    
    try {
        Write-Log "Updating configuration on $serverName"
        
        # Check if the configuration directory exists
        $dirPath = Split-Path -Path $filePath -Parent
        if (-not (Test-Path -Path $dirPath)) {
            Write-Log "Creating directory: $dirPath"
            New-Item -Path $dirPath -ItemType Directory -Force | Out-Null
        }
        
        # Check if the file exists before trying to read it
        if (Test-Path -Path $filePath) {
            $file = Get-Content -Path $filePath -ErrorAction Stop

            if ($file -notmatch [regex]::Escape($newServer)) {
                $updateNeeded = $true
            } else {
                Write-Log "Configuration already contains the new server."
                $updateNeeded = $false
            }
        } else {
            Write-Log "Configuration file does not exist, creating new file."
            $updateNeeded = $true
        }
        
        if ($updateNeeded) {
            $newFileContent = @"
[target-broker:deploymentServer]
targetUri = $newServer
"@
            $newFileContent | Out-File -FilePath $filePath -Force -ErrorAction Stop
            Write-Log "Configuration updated successfully."
        }
        
        return $true
    }
    catch {
        Write-Log "Failed to update configuration: $_" -Level "Error"
        return $false
    }
}

function Update-SplunkServer {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$serverName,
        
        [Parameter(Mandatory = $true)]
        [PSCredential]$credentials,
        
        [Parameter()]
        [switch]$skipConfigUpdate
    )
    
    Write-Log "===== Starting update process for $serverName ====="
    
    $serviceStopSuccess = Set-SplunkService -serverName $serverName -action Stop
    if (-not $serviceStopSuccess) {
        Write-Log "Failed to stop Splunk service on $serverName, skipping update." -Level "Error"
        return $false
    }
    
    Write-Log "Waiting for service to fully stop before installation..."
    Start-Sleep 10
    
    $installSuccess = Install-Splunk -serverName $serverName -credentials $credentials
    if (-not $installSuccess) {
        Write-Log "Installation failed on $serverName, attempting to restart service anyway." -Level "Warning"
    } else {
        Write-Log "Installation succeeded on $serverName"
    }
    
    Write-Log "Waiting for installation to complete before updating configuration..."
    Start-Sleep 10
    
    $configSuccess = $true
    if (-not $skipConfigUpdate) {
        $configSuccess = Update-Config -serverName $serverName
        if (-not $configSuccess) {
            Write-Log "Configuration update failed on $serverName" -Level "Warning"
        } else {
            Write-Log "Configuration update succeeded on $serverName"
        }
    } else {
        Write-Log "Skipping configuration update for $serverName as requested"
    }
    
    $serviceStartSuccess = Set-SplunkService -serverName $serverName -action Start
    if (-not $serviceStartSuccess) {
        Write-Log "Failed to start Splunk service on $serverName" -Level "Error"
    }
    
    $overallSuccess = $installSuccess -and $configSuccess -and $serviceStartSuccess
    if ($overallSuccess) {
        Write-Log "===== Update completed successfully for $serverName ====="
    } else {
        Write-Log "===== Update completed with errors for $serverName ====="
    }
    
    return $overallSuccess
}

try {
    Write-Log "Script started with appVersion: $appVersion"
    
    Test-Input -appVersion $appVersion -appURL $baseURL
    
    $successServers = @()
    $failedServers = @()
    
    if ($serverName) {
        Write-Log "Processing single server: $serverName"
        $success = Update-SplunkServer -serverName $serverName -credentials $credentials -skipConfigUpdate:$skipConfigUpdate
        
        if ($success) {
            $successServers += $serverName
        } else {
            $failedServers += $serverName
        }
    }
    elseif ($serverListSwitch) {
        Write-Log "Processing server list from: $serverListPath"
        
        if (-not (Test-Path -Path $serverListPath)) {
            throw "Server list file not found: $serverListPath"
        }
        
        $servers = Get-Content -Path $serverListPath -ErrorAction Stop
        
        if ($servers.Count -eq 0) {
            throw "Server list is empty"
        }
        
        Write-Log "Found $($servers.Count) servers in the list"
        
        foreach ($server in $servers) {
            $server = $server.Trim()
            if ([string]::IsNullOrWhiteSpace($server)) { continue }
            
            $success = Update-SplunkServer -serverName $server -credentials $credentials -skipConfigUpdate:$skipConfigUpdate
            
            if ($success) {
                $successServers += $server
            } else {
                $failedServers += $server
            }
        }
    }
    else {
        throw "No server name or server list specified. Use -serverName or -serverListSwitch parameter."
    }
    
    Write-Log "===== Update Summary ====="
    Write-Log "Successful updates: $($successServers.Count)"
    Write-Log "Failed updates: $($failedServers.Count)"
    
    if ($successServers.Count -gt 0) {
        Write-Log "Successfully updated servers: $($successServers -join ', ')"
    }
    
    if ($failedServers.Count -gt 0) {
        Write-Log "Failed updates on servers: $($failedServers -join ', ')" -Level "Warning"
    }
}
catch {
    Write-Log "A critical error occurred during the Splunk agent update process: $_" -Level "Error"
}
finally {
    Write-Log "Script completed at $(Get-Date)"
}