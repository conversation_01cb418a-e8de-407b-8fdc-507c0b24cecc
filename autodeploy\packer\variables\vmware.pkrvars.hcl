# VMware vSphere Variables
# Update these values to match your environment

# vCenter connection details
vcenter_server   = "your-vcenter-server.domain.com"
vcenter_username = "<EMAIL>"
vcenter_password = "your-password"

# vSphere infrastructure
datacenter = "Your-Datacenter"
cluster    = "Your-Cluster"
host       = ""  # Leave empty to let vSphere choose
datastore  = "Your-Datastore"
network    = "Your-Network-PortGroup"
folder     = "Templates"

# ISO paths (update to match your environment)
# Windows Server 2022
iso_path_2022 = "[Your-Datastore] ISO/Windows_Server_2022.iso"

# Windows Server 2019  
iso_path_2019 = "[Your-Datastore] ISO/Windows_Server_2019.iso"

# VM specifications
vm_cpu_num  = 2
vm_mem_size = 4096  # 4GB in MB
vm_disk_size = 61440  # 60GB in MB

# WinRM credentials (used during build)
winrm_username = "Administrator"
winrm_password = "P@ssw0rd123!"  # Change this to a secure password

# Template naming
vm_name_2022 = "windows-server-2022-template"
vm_name_2019 = "windows-server-2019-template"
