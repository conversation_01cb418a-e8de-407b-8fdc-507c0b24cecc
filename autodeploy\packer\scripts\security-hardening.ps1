# Security Hardening Script
# This script applies security hardening measures to the Windows server

Write-Host "Applying security hardening measures..."

# Disable unnecessary services
Write-Host "Disabling unnecessary services..."
$servicesToDisable = @(
    "Fax",
    "TapiSrv",
    "Telephony",
    "RemoteRegistry",
    "SharedAccess",
    "SSDPSRV",
    "upnphost",
    "WMPNetworkSvc",
    "WSearch",
    "XblAuthManager",
    "XblGameSave",
    "XboxNetApiSvc"
)

foreach ($service in $servicesToDisable) {
    try {
        $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
        if ($svc) {
            Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
            Set-Service -Name $service -StartupType Disabled
            Write-Host "Disabled service: $service"
        }
    } catch {
        Write-Host "Could not disable service $service : $($_.Exception.Message)"
    }
}

# Configure Windows Firewall
Write-Host "Configuring Windows Firewall..."
try {
    # Enable Windows Firewall for all profiles
    Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled True
    
    # Set default actions
    Set-NetFirewallProfile -Profile Domain -DefaultInboundAction Block -DefaultOutboundAction Allow
    Set-NetFirewallProfile -Profile Public -DefaultInboundAction Block -DefaultOutboundAction Allow
    Set-NetFirewallProfile -Profile Private -DefaultInboundAction Block -DefaultOutboundAction Allow
    
    Write-Host "Windows Firewall configured."
} catch {
    Write-Host "Failed to configure Windows Firewall: $($_.Exception.Message)"
}

# Configure User Account Control (UAC)
Write-Host "Configuring User Account Control..."
try {
    # Set UAC to highest level
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "ConsentPromptBehaviorAdmin" -Value 2
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "ConsentPromptBehaviorUser" -Value 3
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "EnableLUA" -Value 1
    
    Write-Host "UAC configured."
} catch {
    Write-Host "Failed to configure UAC: $($_.Exception.Message)"
}

# Disable SMBv1
Write-Host "Disabling SMBv1..."
try {
    Disable-WindowsOptionalFeature -Online -FeatureName SMB1Protocol -NoRestart
    Set-SmbServerConfiguration -EnableSMB1Protocol $false -Force
    Write-Host "SMBv1 disabled."
} catch {
    Write-Host "Failed to disable SMBv1: $($_.Exception.Message)"
}

# Configure password policy
Write-Host "Configuring password policy..."
try {
    # Set minimum password length
    secedit /export /cfg "$env:TEMP\secpol.cfg"
    (Get-Content "$env:TEMP\secpol.cfg") -replace "MinimumPasswordLength = \d+", "MinimumPasswordLength = 12" | Set-Content "$env:TEMP\secpol.cfg"
    (Get-Content "$env:TEMP\secpol.cfg") -replace "PasswordComplexity = \d+", "PasswordComplexity = 1" | Set-Content "$env:TEMP\secpol.cfg"
    (Get-Content "$env:TEMP\secpol.cfg") -replace "MaximumPasswordAge = \d+", "MaximumPasswordAge = 90" | Set-Content "$env:TEMP\secpol.cfg"
    secedit /configure /db "$env:TEMP\secedit.sdb" /cfg "$env:TEMP\secpol.cfg"
    Remove-Item "$env:TEMP\secpol.cfg" -Force
    Remove-Item "$env:TEMP\secedit.sdb" -Force
    
    Write-Host "Password policy configured."
} catch {
    Write-Host "Failed to configure password policy: $($_.Exception.Message)"
}

# Disable unnecessary protocols and features
Write-Host "Disabling unnecessary protocols..."
try {
    # Disable NetBIOS over TCP/IP
    $adapters = Get-WmiObject -Class Win32_NetworkAdapterConfiguration | Where-Object { $_.IPEnabled -eq $true }
    foreach ($adapter in $adapters) {
        $adapter.SetTcpipNetbios(2)  # 2 = Disable NetBIOS over TCP/IP
    }
    
    Write-Host "NetBIOS over TCP/IP disabled."
} catch {
    Write-Host "Failed to disable NetBIOS: $($_.Exception.Message)"
}

# Configure audit policies
Write-Host "Configuring audit policies..."
try {
    auditpol /set /category:"Logon/Logoff" /success:enable /failure:enable
    auditpol /set /category:"Account Logon" /success:enable /failure:enable
    auditpol /set /category:"Account Management" /success:enable /failure:enable
    auditpol /set /category:"Policy Change" /success:enable /failure:enable
    auditpol /set /category:"Privilege Use" /success:enable /failure:enable
    auditpol /set /category:"System" /success:enable /failure:enable
    
    Write-Host "Audit policies configured."
} catch {
    Write-Host "Failed to configure audit policies: $($_.Exception.Message)"
}

# Configure Windows Defender (if available)
Write-Host "Configuring Windows Defender..."
try {
    # Enable real-time protection
    Set-MpPreference -DisableRealtimeMonitoring $false
    
    # Enable cloud protection
    Set-MpPreference -MAPSReporting Advanced
    Set-MpPreference -SubmitSamplesConsent SendAllSamples
    
    # Update signatures
    Update-MpSignature
    
    Write-Host "Windows Defender configured."
} catch {
    Write-Host "Windows Defender configuration failed: $($_.Exception.Message)"
}

# Disable AutoRun/AutoPlay
Write-Host "Disabling AutoRun/AutoPlay..."
try {
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" -Name "NoDriveTypeAutoRun" -Value 255
    Set-ItemProperty -Path "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" -Name "NoDriveTypeAutoRun" -Value 255
    
    Write-Host "AutoRun/AutoPlay disabled."
} catch {
    Write-Host "Failed to disable AutoRun/AutoPlay: $($_.Exception.Message)"
}

# Configure Event Log sizes
Write-Host "Configuring Event Log sizes..."
try {
    wevtutil sl Security /ms:1073741824  # 1GB
    wevtutil sl System /ms:1073741824    # 1GB
    wevtutil sl Application /ms:1073741824  # 1GB
    
    Write-Host "Event Log sizes configured."
} catch {
    Write-Host "Failed to configure Event Log sizes: $($_.Exception.Message)"
}

# Remove unnecessary features
Write-Host "Removing unnecessary Windows features..."
$featuresToRemove = @(
    "TelnetClient",
    "TFTP",
    "SimpleTCP",
    "Internet-Explorer-Optional-amd64"
)

foreach ($feature in $featuresToRemove) {
    try {
        Disable-WindowsOptionalFeature -Online -FeatureName $feature -NoRestart -ErrorAction SilentlyContinue
        Write-Host "Removed feature: $feature"
    } catch {
        Write-Host "Could not remove feature $feature : $($_.Exception.Message)"
    }
}

# Configure registry settings for security
Write-Host "Applying security registry settings..."
try {
    # Disable anonymous enumeration
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa" -Name "RestrictAnonymous" -Value 1
    
    # Disable LLMNR
    New-Item -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows NT\DNSClient" -Force
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows NT\DNSClient" -Name "EnableMulticast" -Value 0
    
    # Disable WDigest
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\SecurityProviders\WDigest" -Name "UseLogonCredential" -Value 0
    
    Write-Host "Security registry settings applied."
} catch {
    Write-Host "Failed to apply security registry settings: $($_.Exception.Message)"
}

Write-Host "Security hardening completed."
