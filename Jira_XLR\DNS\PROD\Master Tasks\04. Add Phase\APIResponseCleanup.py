import json

dnsAddAPIResponse = releaseVariables.get('dnsAddAPIResponse', '{}')

if isinstance(dnsAddAPIResponse, basestring):
    try:
        dnsAddAPIResponse = json.loads(dnsAddAPIResponse)
    except (ValueError, TypeError) as e:
        print "Error parsing JSON: " + str(e)
        # Set defaults to avoid further errors
        dnsAddAPIResponse = {
            "date": "N/A",
            "status": "ERROR",
            "message": "Failed to parse API response",
            "success": "false"
        }

print "Type of dnsAddAPIResponse: " + str(type(dnsAddAPIResponse))
print "Value of dnsAddAPIResponse: " + str(dnsAddAPIResponse)

releaseVariables['dnsAddDate'] = dnsAddAPIResponse.get('date', 'N/A')
releaseVariables['dnsAddStatus'] = dnsAddAPIResponse.get('status', 'ERROR')
releaseVariables['dnsAddMessage'] = dnsAddAPIResponse.get('message', 'No message available')
releaseVariables['dnsAddSuccess'] = dnsAddAPIResponse.get('success', 'false')

is_success = str(releaseVariables['dnsAddSuccess']).lower() == "true"

if is_success and releaseVariables['dnsAddStatus'] == 'NORMAL':
    releaseVariables['action_proceed'] = True
    dnsAddUpdateMessage = "Adding of DNS record succeeded\n" + \
                         releaseVariables.get('dnsAddRequestDataRaw', 'No request data available')
else:
    releaseVariables['action_proceed'] = False
    dnsAddUpdateMessage = "Adding of DNS record failed\n" + \
                         releaseVariables.get('dnsAddRequestDataRaw', 'No request data available')

releaseVariables['dnsAddUpdateMessage'] = dnsAddUpdateMessage

print dnsAddUpdateMessage