# Generate User Data Script for Business-Specific Domain Join
# This script creates user data for launching EC2 instances with automatic domain join

param(
    [Parameter(Mandatory=$true)]
    [string]$BusinessConfigPath,
    
    [Parameter(Mandatory=$true)]
    [string]$ServerRole,
    
    [Parameter(Mandatory=$true)]
    [string]$DomainUser,
    
    [Parameter(Mandatory=$true)]
    [string]$DomainPassword,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = "user-data.ps1",
    
    [Parameter(Mandatory=$false)]
    [switch]$UseParameterStore = $false,
    
    [Parameter(Mandatory=$false)]
    [string]$ParameterPrefix = "/ec2/domain"
)

Write-Host "Generating User Data for Business-Specific Domain Join" -ForegroundColor Green
Write-Host "====================================================" -ForegroundColor Green

# Function to read business configuration
function Get-BusinessConfig {
    param([string]$ConfigPath)
    
    try {
        if (!(Test-Path $ConfigPath)) {
            throw "Business configuration file not found: $ConfigPath"
        }
        
        $config = Get-Content $ConfigPath -Raw | ConvertFrom-Json
        Write-Host "✓ Loaded configuration for business: $($config.businessName)" -ForegroundColor Green
        return $config
        
    } catch {
        Write-Error "Failed to load business configuration: $($_.Exception.Message)"
        return $null
    }
}

# Function to validate server role
function Test-ServerRole {
    param([object]$Config, [string]$Role)
    
    if ($Config.serverOUs.$Role) {
        Write-Host "✓ Server role '$Role' found in configuration" -ForegroundColor Green
        Write-Host "  Target OU: $($Config.serverOUs.$Role)" -ForegroundColor Cyan
        return $true
    } else {
        Write-Error "Server role '$Role' not found in business configuration"
        Write-Host "Available roles:" -ForegroundColor Yellow
        $Config.serverOUs.PSObject.Properties | ForEach-Object {
            Write-Host "  - $($_.Name)" -ForegroundColor White
        }
        return $false
    }
}

# Function to generate user data script
function New-UserDataScript {
    param(
        [object]$BusinessConfig,
        [string]$Role,
        [string]$User,
        [string]$Password,
        [bool]$UseSSM
    )
    
    # Convert business config to JSON string for embedding
    $configJson = $BusinessConfig | ConvertTo-Json -Depth 10 -Compress
    $configJson = $configJson -replace '"', '\"'
    
    # Generate credential section
    if ($UseSSM) {
        $credentialSection = @"
    # Get domain credentials from SSM Parameter Store
    try {
        `$domainUser = (Get-SSMParameter -Name "$ParameterPrefix/username").Value
        `$domainPassword = (Get-SSMParameter -Name "$ParameterPrefix/password" -WithDecryption `$true).Value
        Write-Log "Retrieved domain credentials from Parameter Store"
    } catch {
        Write-Log "Failed to retrieve credentials from Parameter Store: `$(`$_.Exception.Message)" "ERROR"
        throw "Cannot proceed without domain credentials"
    }
"@
    } else {
        $credentialSection = @"
    # Domain credentials (consider using Parameter Store for production)
    `$domainUser = "$User"
    `$domainPassword = "$Password"
    Write-Log "Using embedded domain credentials"
"@
    }
    
    # Generate role-specific software installation
    $roleInstallation = switch ($Role) {
        "WebServer-2019" { 
            @"
            Write-Log "Installing IIS for Web Server 2019 role"
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole -All -NoRestart
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-ASPNET45 -All -NoRestart
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-NetFxExtensibility45 -All -NoRestart
"@
        }
        "WebServer-2022" { 
            @"
            Write-Log "Installing IIS for Web Server 2022 role"
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole -All -NoRestart
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-ASPNET45 -All -NoRestart
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-NetFxExtensibility45 -All -NoRestart
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-HttpRedirect -All -NoRestart
"@
        }
        "SQLServer-2019" { 
            @"
            Write-Log "Preparing system for SQL Server 2019"
            # Configure Windows features for SQL Server
            Enable-WindowsOptionalFeature -Online -FeatureName NetFx3 -All -NoRestart
            # Set SQL Server service account permissions (placeholder)
            Write-Log "SQL Server 2019 preparation completed"
"@
        }
        "SQLServer-2022" { 
            @"
            Write-Log "Preparing system for SQL Server 2022"
            # Configure Windows features for SQL Server
            Enable-WindowsOptionalFeature -Online -FeatureName NetFx3 -All -NoRestart
            # Set SQL Server service account permissions (placeholder)
            Write-Log "SQL Server 2022 preparation completed"
"@
        }
        default { 
            @"
            Write-Log "No specific role configuration for: $Role"
"@
        }
    }
    
    $userDataScript = @"
<powershell>
# EC2 User Data Script for $($BusinessConfig.businessName) - $Role
# Generated on $(Get-Date)

# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Create log file
`$logFile = "C:\Scripts\user-data.log"
if (!(Test-Path "C:\Scripts")) {
    New-Item -ItemType Directory -Path "C:\Scripts" -Force | Out-Null
}

function Write-Log {
    param([string]`$Message, [string]`$Level = "INFO")
    `$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    `$logEntry = "[`$timestamp] [`$Level] `$Message"
    Write-Host `$logEntry
    `$logEntry | Out-File -FilePath `$logFile -Append -Encoding UTF8
}

Write-Log "Starting user data execution for $($BusinessConfig.businessName) - $Role"

try {
    # Create business configuration
    `$businessConfigJson = @"
$configJson
"@
    
    `$businessConfig = `$businessConfigJson | ConvertFrom-Json
    `$configPath = "C:\Scripts\business-config.json"
    `$businessConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath `$configPath -Encoding UTF8
    Write-Log "Business configuration saved to: `$configPath"

    # Configure default administrator groups
    Write-Log "Configuring default administrator groups..."
    try {
        if (`$businessConfig.DEFAULT_ADM -and `$businessConfig.DEFAULT_ADM.Count -gt 0) {
            `$currentAdmins = @()
            try {
                `$currentAdmins = Get-LocalGroupMember -Group "Administrators" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Name
            } catch {
                Write-Log "Warning: Could not retrieve current administrators: `$(`$_.Exception.Message)" "WARNING"
            }

            `$addedGroups = @()
            `$skippedGroups = @()
            `$failedGroups = @()

            foreach (`$group in `$businessConfig.DEFAULT_ADM) {
                try {
                    if (`$currentAdmins -contains `$group) {
                        Write-Log "Group '`$group' is already in Administrators group"
                        `$skippedGroups += `$group
                        continue
                    }

                    Add-LocalGroupMember -Group "Administrators" -Member `$group -ErrorAction Stop
                    Write-Log "Successfully added '`$group' to Administrators group"
                    `$addedGroups += `$group

                } catch {
                    Write-Log "Failed to add '`$group' to Administrators group: `$(`$_.Exception.Message)" "WARNING"
                    `$failedGroups += `$group
                }
            }

            Write-Log "Admin groups configuration completed - Added: `$(`$addedGroups.Count), Skipped: `$(`$skippedGroups.Count), Failed: `$(`$failedGroups.Count)"
        } else {
            Write-Log "No default administrator groups defined in configuration" "WARNING"
        }
    } catch {
        Write-Log "Error configuring administrator groups: `$(`$_.Exception.Message)" "ERROR"
    }

$credentialSection

    # Set environment variables for domain join
    [Environment]::SetEnvironmentVariable("SERVER_ROLE", "$Role", "Machine")
    [Environment]::SetEnvironmentVariable("DOMAIN_USER", `$domainUser, "Machine")
    [Environment]::SetEnvironmentVariable("DOMAIN_PASSWORD", `$domainPassword, "Machine")
    Write-Log "Environment variables set for domain join"
    
    # Role-specific software installation
$roleInstallation
    
    # Trigger domain join process
    Write-Log "Triggering domain join process..."
    if (Test-Path "C:\Scripts\cloud-init-domain-join.ps1") {
        Start-Process -FilePath "PowerShell.exe" -ArgumentList "-ExecutionPolicy Bypass -File C:\Scripts\cloud-init-domain-join.ps1" -Wait
        Write-Log "Domain join process completed"
    } else {
        Write-Log "Domain join script not found, skipping automatic domain join" "WARNING"
    }
    
    Write-Log "User data execution completed successfully"
    
} catch {
    Write-Log "User data execution failed: `$(`$_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: `$(`$_.Exception.StackTrace)" "ERROR"
    
    # Create error flag file for troubleshooting
    "User data failed: `$(`$_.Exception.Message)" | Out-File -FilePath "C:\Scripts\user-data-error.txt" -Encoding UTF8
}
</powershell>
"@

    return $userDataScript
}

# Main execution
try {
    # Load business configuration
    $businessConfig = Get-BusinessConfig -ConfigPath $BusinessConfigPath
    if (!$businessConfig) {
        exit 1
    }
    
    # Validate server role
    if (!(Test-ServerRole -Config $businessConfig -Role $ServerRole)) {
        exit 1
    }
    
    # Generate user data script
    Write-Host "Generating user data script..." -ForegroundColor Yellow
    $userData = New-UserDataScript -BusinessConfig $businessConfig -Role $ServerRole -User $DomainUser -Password $DomainPassword -UseSSM $UseParameterStore
    
    # Save to file
    $userData | Out-File -FilePath $OutputPath -Encoding UTF8
    Write-Host "✓ User data script saved to: $OutputPath" -ForegroundColor Green
    
    # Display summary
    Write-Host "`nGenerated User Data Summary:" -ForegroundColor Yellow
    Write-Host "  Business: $($businessConfig.businessName)" -ForegroundColor White
    Write-Host "  Domain: $($businessConfig.domain)" -ForegroundColor White
    Write-Host "  Server Role: $ServerRole" -ForegroundColor White
    Write-Host "  Target OU: $($businessConfig.serverOUs.$ServerRole)" -ForegroundColor White
    Write-Host "  Computer Prefix: $($businessConfig.computerNamePrefix)" -ForegroundColor White
    Write-Host "  Use Parameter Store: $UseParameterStore" -ForegroundColor White
    
    Write-Host "`nNext Steps:" -ForegroundColor Yellow
    Write-Host "1. Review the generated user data script: $OutputPath" -ForegroundColor White
    Write-Host "2. Launch EC2 instance with this user data" -ForegroundColor White
    Write-Host "3. Instance will automatically join the correct OU" -ForegroundColor White
    
    if (!$UseParameterStore) {
        Write-Host "`nSecurity Note:" -ForegroundColor Red
        Write-Host "Consider using AWS Systems Manager Parameter Store for domain credentials" -ForegroundColor Yellow
        Write-Host "Use -UseParameterStore flag for production deployments" -ForegroundColor Yellow
    }
    
} catch {
    Write-Error "Failed to generate user data: $($_.Exception.Message)"
    exit 1
}
