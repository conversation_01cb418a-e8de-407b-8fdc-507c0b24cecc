{% extends "base.html" %}
{% block content %}
    <p class="text-danger"><b>{{ server_data.messages }}</b></p>

    <!-- Tabs navs  data-mdb-tab-init   -->

    <ul class="nav nav-tabs">
        <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#tab_get_configs" aria-current="page">Get Configs</a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab_update_annotations">Update EBX</a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab_get_objects">Get Objects</a></li>
      </ul>
    <!-- Tabs navs -->

    <!-- Tabs content -->
    <div class="tab-content" id="server_tabs_content">
        <div class="tab-pane fade show active" id="tab_get_configs" role="tabpanel" aria-labelledby="tab_get_configs">
            <form action="/configs" method="post" name="form_get_configs">
                <div class="form-group">
                    <div class="form-group">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th scope="col" style="width: 20%;">Client</th>
                                    <th scope="col" style="width: 20%;">Deployment</th>
                                    <th scope="col" style="width: 15%;">Platform</th>
                                    <th scope="col" style="width: 15%;">Zone</th>
                                    <th scope="col" style="width: 15%;">App Type</th>
                                    <th scope="col" style="width: 15%;">OS Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                <td scope="col"><select id="client" name="client">
                                    {% for x in ui_options.clients %}
                                    <option value="{{x}}">{{x}}</option>
                                    {% endfor %}
                                </select></td>
                                <td scope="col"><select name="deployment">
                                    {% for x in ui_options.deployments %}
                                    <option value="{{x}}">{{x}}</option>
                                    {% endfor %}
                                </select></td>
                                <td scope="col"><select name="platforms">
                                    {% for x in ui_options.platforms %}
                                    <option value="{{x}}">{{x}}</option>
                                    {% endfor %}
                                </select></td>
                                <td scope="col"><select name="zone">
                                    {% for x in ui_options.locations %}
                                    <option value="{{x}}">{{x}}</option>
                                    {% endfor %}
                                </select></td>
                                <td scope="col"><select name="app_type">
                                    {% for x in ui_options.app_types %}
                                    <option value="{{x}}">{{x}}</option>
                                    {% endfor %}
                                </select></td>
                                <td scope="col"><select name="os_type">
                                    {% for x in ui_options.os_types %}
                                    <option value="{{x}}">{{x}}</option>
                                    {% endfor %}
                                </select></td>
                            </tbody>
                        </table>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary" id="btn_get_configs">Get Configs</button>
            </form>
        </div>
        <div class="tab-pane fade" id="tab_update_annotations" role="tabpanel" aria-labelledby="tab_update_annotations">
            <table class="table">
                <thead>
                    <tr>
                        <th scope="col">Item</th> <th scope="col">Detail</th>            
                    </tr>
                </thead>
                <tbody>            

                </tbody>
            </table>
        </div>
        <div class="tab-pane fade" id="tab_get_objects" role="tabpanel" aria-labelledby="tab_get_objects">
            <form action="/getname" method="post" name="add_resources">
                <div class="form-group">
                <table class="table">
                    <thead>
                        <tr>
                            <th width="25%" scope="col">Option</th>
                            <th width="25%" scope="col">Value</th> 
                            <th width="50%" scope="col">Comment</th>   
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td scope="col">Object Type</td>  
                            <td width="25%" scope="col"><select id="object_type" name="object_type">
                                {% for x in ui_objects.object_types %}
                                <option value="{{x}}">{{x}}</option>
                                {% endfor %}
                            </select></td>
                            <td scope="col"><em>Type of object to create</em></td>
                        </tr>
                        <tr>
                            <td scope="col">OS Type</td>  
                            <td width="25%" scope="col"><select id="os_type" name="os_type">
                                {% for x in ui_objects.os_types %}
                                <option value="{{x}}">{{x}}</option>
                                {% endfor %}
                            </select></td>
                            <td scope="col"><em>To determine object creation requirement</em></td>
                        </tr>
                        <tr>
                            <td scope="col">App Type</td>  
                            <td scope="col"><select id="object_typ" name="app_type">
                                {% for x in ui_objects.app_types %}
                                <option value="{{x}}">{{x}}</option>
                                {% endfor %}
                            </select></td>
                            <td scope="col"><em>To determine object AD OU location</em></td>
                        </tr>
                        <tr>
                            <td scope="col">Members</td>  
                            <td scope="col"><input type="text" class="form-control" name="object_members" id="object_members" placeholder="Enter members"></td>
                            <td scope="col"><em>Optional Members if cluster object, Comma Spearated e.g. "member1,member2"</em></td>
                        </tr>
                    </tbody>
                </table>
                </div>
                <button type="submit" class="btn btn-primary" id="submit_object">Get Objects</button>
            </form>
        </div>

        <!-- Portrait view -->
        <div class="tab-pane fade" id="tab_getquote" role="tabpanel" aria-labelledby="tab_getquote">
            <form action="/specs" method="post" name="add_resources">
                <div class="form-group">

                    <label for="enterserver" ><b>Reference Number</b></label>
                    <input type="text" required class="form-control" name="reference" id="reference" placeholder="Enter reference number"><p></p>
                    <label for="enterserver"><b>Server Count</b></label>
                    <input name="vm_count" id="vm_count" type="number"  min="1" max="4" value="1" placeholder="Enter Instances Count">
                    <label for="enterserver"><b>Add SRM Backup</b></label>
                    <input name="add_srm" id="add_srm" type="checkbox">
                    <hr width="100%" size="2">
                    <!-- Compute Resources -->
                    <p><b>Compute:</b></p>
                    <table class="table">
                        <thead>
                            <tr>
                                <th width="15%" scope="col">Item</th>
                                <th width="15%" scope="col">Current Capacity</th> 
                                <th width="15%" scope="col">Capacity to Add</th>        
                                <th width="55%" scope="col"></th>       
                            </tr>
                        </thead>
                        <tbody>           
                            <tr>
                                <td width="15%" scope="col">vCPUs</td>  
                                <td width="15%"><input name="add_vcpus" id="add_vcpus" type="number" min="0"  max="8" value="0" placeholder="To max 8 total"></td>
                                <td width="55%" scope="col"></td>  
                            </tr>
                            <tr>   
                                <td width="15%" scope="col">vRAM GB</td>    
                                <td width="15%"><input name="add_vram_gbs" id="add_vram_gbs" type="number" min="0"  max="64" value="0" placeholder="Enter GBs, to max 64GB"></td>
                    
                            </tr>
                        </tbody>
                    </table>

                    <table class="table">                    
                        <thead>
                            <tr>                        
                                <th width="15%" scope="col">disk</th>
                                <th width="15%" scope="col">Current Capacity</th>
                                <th width="15%" scope="col">Capacity to Add</th>    
                                <th width="15%" scope="col">disk_type</th>  
                                <th width="15%" scope="col">mirror_type</th>  
                                <th width="25%" scope="col">mount(s)</th>
                            </tr>
                        </thead>
                        <tbody>              
                            

                        </tbody>
                        
                    </table>
                </div>
                <button type="submit" class="btn btn-primary" id="submit_resources">Get Quote</button>
            </form>
    </div>
    </div>
    <!-- Tabs content -->


{% endblock %}


   