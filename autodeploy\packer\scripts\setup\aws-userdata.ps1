<powershell>
# AWS EC2 UserData script to enable WinRM for Packer
Write-Host "Starting AWS UserData script for WinRM configuration..."

# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope LocalMachine -Force

# Enable WinRM
Enable-PSRemoting -Force -SkipNetworkProfileCheck

# Configure WinRM for HTTPS
winrm quickconfig -q
winrm set winrm/config/winrs '@{MaxMemoryPerShellMB="512"}'
winrm set winrm/config '@{MaxTimeoutms="1800000"}'
winrm set winrm/config/service '@{AllowUnencrypted="false"}'
winrm set winrm/config/service/auth '@{Basic="true"}'

# Create self-signed certificate for HTTPS
$cert = New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "cert:\LocalMachine\My"
winrm create winrm/config/Listener?Address=*+Transport=HTTPS "@{Hostname=`"localhost`";CertificateThumbprint=`"$($cert.Thumbprint)`"}"

# Configure firewall for WinRM HTTPS
netsh advfirewall firewall add rule name="WinRM-HTTPS" dir=in localport=5986 protocol=TCP action=allow

# Restart WinRM service
Restart-Service winrm

Write-Host "AWS UserData WinRM configuration completed."
</powershell>
