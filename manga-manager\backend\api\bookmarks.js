const express = require('express');
const { Bookmark, Manga, Chapter } = require('../models');
const { isAuthenticated } = require('../../middleware/auth/auth-middleware');

const router = express.Router();

// Get all bookmarks for the authenticated user
router.get('/', isAuthenticated, async (req, res) => {
  try {
    const bookmarks = await Bookmark.findAll({
      where: { userId: req.user.id },
      include: [
        {
          model: Manga,
          as: 'manga'
        }
      ],
      order: [['updatedAt', 'DESC']]
    });
    
    res.status(200).json({ bookmarks });
  } catch (error) {
    console.error('Error fetching bookmarks:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get bookmark by ID
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const bookmark = await Bookmark.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id
      },
      include: [
        {
          model: Manga,
          as: 'manga',
          include: [
            {
              model: Chapter,
              as: 'chapters',
              where: { chapterNumber: Bookmark.sequelize.col('Bookmark.chapter') },
              required: false
            }
          ]
        }
      ]
    });
    
    if (!bookmark) {
      return res.status(404).json({ message: 'Bookmark not found' });
    }
    
    res.status(200).json({ bookmark });
  } catch (error) {
    console.error('Error fetching bookmark:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create or update bookmark
router.post('/', isAuthenticated, async (req, res) => {
  try {
    const { mangaId, chapter, page, note } = req.body;
    
    if (!mangaId || !chapter || !page) {
      return res.status(400).json({ message: 'Missing required fields' });
    }
    
    // Check if manga exists
    const manga = await Manga.findByPk(mangaId);
    
    if (!manga) {
      return res.status(404).json({ message: 'Manga not found' });
    }
    
    // Check if bookmark already exists
    const [bookmark, created] = await Bookmark.findOrCreate({
      where: {
        userId: req.user.id,
        mangaId
      },
      defaults: {
        chapter,
        page,
        note
      }
    });
    
    if (!created) {
      // Update existing bookmark
      bookmark.chapter = chapter;
      bookmark.page = page;
      bookmark.note = note;
      await bookmark.save();
    }
    
    res.status(created ? 201 : 200).json({
      message: created ? 'Bookmark created' : 'Bookmark updated',
      bookmark
    });
  } catch (error) {
    console.error('Error creating/updating bookmark:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete bookmark
router.delete('/:id', isAuthenticated, async (req, res) => {
  try {
    const bookmark = await Bookmark.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id
      }
    });
    
    if (!bookmark) {
      return res.status(404).json({ message: 'Bookmark not found' });
    }
    
    await bookmark.destroy();
    
    res.status(200).json({ message: 'Bookmark deleted' });
  } catch (error) {
    console.error('Error deleting bookmark:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;