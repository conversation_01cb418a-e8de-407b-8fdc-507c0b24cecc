# AWS Image Builder Distribution Configuration
# Defines how and where the custom Windows Server 2022 AMI will be distributed

name: WindowsServer2022Distribution
description: Distribution configuration for custom Windows Server 2022 AMI

# Distribution settings
distributions:
# Primary region distribution
- region: af-south-1
  amiDistributionConfiguration:
    name: "BaseWindowsServer2022-{{ imagebuilder:buildDate }}"
    description: "Windows Server 2022 base image with .NET 4.8, registry optimizations, and firewall rules for business deployment - Built on {{ imagebuilder:buildDate }}"

    # AMI tags
    amiTags:
      Name: "BaseWindowsServer2022-{{ imagebuilder:buildDate }}"
      OS: "Windows Server 2022"
      BaseAMI: "{{ imagebuilder:parentImage }}"
      BuildDate: "{{ imagebuilder:buildDate }}"
      Components: ".NET Framework 4.8, Registry Optimizations, Firewall Rules"
      Environment: "Production"
      Version: "1.0.0"
      CreatedBy: "AWS Image Builder"
      Purpose: "Base Windows Server Image"
      Compliance: "CIS Benchmark Ready"

    # Target accounts for AMI sharing (optional)
    targetAccountIds:
    - "************" # Replace with target AWS account IDs
    - "************" # Add more account IDs as needed
    # Launch permissions
    launchPermission:
      userIds:
      - "************" # Replace with AWS account IDs that can launch instances
      - "************"
      # userGroups:
      #   - "all"  # Uncomment to make AMI public (not recommended for production)

      # License configuration for Windows
      # Note: When using Microsoft KMS for activation, License Manager configuration is not required
      # Windows will automatically activate against your KMS server at runtime
      # Only use licenseConfigurationArns if you're tracking Windows licenses through AWS License Manager
      # licenseConfigurationArns:
      # - "arn:aws:license-manager:af-south-1:************:license-configuration:lic-0123456789abcdef0"
