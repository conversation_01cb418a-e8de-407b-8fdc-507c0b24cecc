$share = '\\srv041603.mud.internal.co.za\Reports\Custom Reports\Daily Reports'
$share2 = '\\srv041603.mud.internal.co.za\Reports\UserDefined'
$creds = Import-Clixml "\\SRV005879\D$\Scripts\EntuityDailyCopy\svcbcxamsacc.xml"
$requiredFolders = @("BuilderReport338_files", "BuilderReport339_files", "BuilderReport340_files")
$requiredFolders2 = @("BuilderReport373_files", "BuilderReport379_files", "BuilderReport380_files", "BuilderReport381_files")
$outputPath = "\\SRV005879\Reports\Entuity"
New-PSDrive -Name EntuityReports -Root $share -PSProvider FileSystem -Credential $creds

foreach ($folder in $requiredFolders){
    # Check if folder exists before attempting to delete
    if (Test-Path "$outputPath\$folder") {
        Remove-Item "$outputPath\$folder" -Force -Recurse
    }

    $newPath = "EntuityReports:\$folder\savedReports"

    $latestFIle = Get-ChildItem $newPath | Where-Object {$_.Name -match "csv"} | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    $sourceFileFullPath = "$newPath\$($latestFIle.Name)"

    New-Item -Path "$outputPath" -Name $folder -ItemType Directory
    Copy-Item -Path $sourceFileFullPath -Destination "$outputPath\$folder\$($latestFIle.Name)"
}

New-PSDrive -Name EntuityReports2 -Root $share2 -PSProvider FileSystem -Credential $creds

foreach ($folder2 in $requiredFolders2){
    # Check if folder exists before attempting to delete
    if (Test-Path "$outputPath\$folder2") {
        Remove-Item "$outputPath\$folder2" -Force -Recurse
    }

    $newPath2 = "EntuityReports2:\$folder2\savedReports"

    $latestFIle2 = Get-ChildItem $newPath2 | Where-Object {$_.Name -match "csv"} | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    $sourceFileFullPath2 = "$newPath2\$($latestFIle2.Name)"

    New-Item -Path "$outputPath" -Name $folder2 -ItemType Directory
    Copy-Item -Path $sourceFileFullPath2 -Destination "$outputPath\$folder2\$($latestFIle2.Name)"
}