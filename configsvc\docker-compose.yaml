version: '3.8'

services:
  web:
    build: .
    environment:
      - PYTH<PERSON>UNBUFFERED=1
      - UVICORN_HOST=${UVICORN_HOST}
      - UVICORN_PORT=${UVICORN_PORT}
      - ENV=${ENV}
      - DEBUG=${DEBUG}
      - DB_USER=${DB_USER}
      - DB_PWRD=${DB_PWRD}
      - DB_NAME=${DB_NAME}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
    command: ["uvicorn", "app.main:app", "--host", "${UVICORN_HOST}", "--port", "${UVICORN_PORT}", "--reload"]
    ports:
      - ${CONTAINER_PORT}:${UVICORN_PORT}
    volumes:
      - ./:/app
