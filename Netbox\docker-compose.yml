services:
  netbox: &netbox
    image: docker.io/netboxcommunity/netbox:${VERSION-v4.1-3.0.2}
    depends_on:
      - postgres
      - redis
      - redis-cache
    env_file: env/netbox.env
    user: "unit:root"
    healthcheck:
      test: curl -f http://localhost:8080/login/ || exit 1
      start_period: 90s
      timeout: 3s
      interval: 15s
    volumes:
      - ./configuration:/etc/netbox/config:z, +ro
      - netbox-media-files:/opt/netbox/netbox/media:rw
      - netbox-reports-files:/opt/netbox/netbox/reports:rw
      - netbox-scripts-files:/opt/netbox/netbox/scripts:rw
  netbox-worker:
    <<: *netbox
    depends_on:
      netbox:
        condition: service_healthy
    command:
      - /opt/netbox/venv/bin/python
      - /opt/netbox/netbox/manage.py
      - rqworker
    healthcheck:
      test: ps -aux | grep -v grep | grep -q rqworker || exit 1
      start_period: 20s
      timeout: 3s
      interval: 15s
  netbox-housekeeping:
    <<: *netbox
    depends_on:
      netbox:
        condition: service_healthy
    command:
      - /opt/netbox/housekeeping.sh
    healthcheck:
      test: ps -aux | grep -v grep | grep -q housekeeping || exit 1
      start_period: 20s
      timeout: 3s
      interval: 15s

  # postgres
  postgres:
    image: docker.io/postgres:16-alpine
    healthcheck:
      test: pg_isready -q -t 2 -d $$POSTGRES_DB -U $$POSTGRES_USER
      start_period: 20s
      timeout: 30s
      interval: 10s
      retries: 5
    env_file: env/postgres.env
    volumes:
      - netbox-postgres-data:/var/lib/postgresql/data

  # redis
  redis:
    image: docker.io/valkey/valkey:8.0-alpine
    command:
      - sh
      - -c # this is to evaluate the $REDIS_PASSWORD from the env
      - valkey-server --appendonly yes --requirepass $$REDIS_PASSWORD ## $$ because of docker-compose
    healthcheck: &redis-healthcheck
      test: '[ $$(valkey-cli --pass "$${REDIS_PASSWORD}" ping) = ''PONG'' ]'
      start_period: 5s
      timeout: 3s
      interval: 1s
      retries: 5
    env_file: env/redis.env
    volumes:
      - netbox-redis-data:/data
  redis-cache:
    image: docker.io/valkey/valkey:8.0-alpine
    command:
      - sh
      - -c # this is to evaluate the $REDIS_PASSWORD from the env
      - valkey-server --requirepass $$REDIS_PASSWORD ## $$ because of docker-compose
    healthcheck: *redis-healthcheck
    env_file: env/redis-cache.env
    volumes:
      - netbox-redis-cache-data:/data

volumes:
  netbox-media-files:
    driver: local
  netbox-postgres-data:
    driver: local
  netbox-redis-cache-data:
    driver: local
  netbox-redis-data:
    driver: local
  netbox-reports-files:
    driver: local
  netbox-scripts-files:
    driver: local