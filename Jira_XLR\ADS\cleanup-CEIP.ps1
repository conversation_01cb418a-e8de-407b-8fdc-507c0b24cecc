param(
    [Parameter(Mandatory=$true)][string]$serverName,
    [Parameter(Mandatory=$true)][string]$domainName
)

$currentPath = split-path -parent $MyInvocation.MyCommand.Definition
$global:config = Get-Content -Path "$currentPath\configs\decommScriptConfig.json" | ConvertFrom-Json

### Paths to credentials ##
$credPath = $config.credPath
$devCredsPath = "$credPath\devDomainCreds.xml"
$ppeCredsPath = "$credPath\ppeDomainCreds.xml"
$prdCredsPath = "$credPath\prdDomainCreds.xml"

if($domainName -match "dev"){
    $global:osCreds = Import-Clixml -Path $devCredsPath
}elseif($domainName -match "ppe"){
    $global:osCreds = Import-Clixml -Path $ppeCredsPath
}else{
    $global:osCreds = Import-Clixml -Path $prdCredsPath
}

# Suppress VMware PowerCLI CEIP warnings
#Set-PowerCLIConfiguration -ParticipateInCeip $false -Confirm:$false -WarningAction SilentlyContinue -InformationAction SilentlyContinue | Out-Null
function Clean-VMwareWarningFromJSON {
    param(
        [string]$InputText
    )
    
    # Pattern to match the VMware CEIP warning
    $warningPattern = 'WARNING: Please consider joining the VMware Customer Experience Improvement Program.*?Set-PowerCLIConfiguration -Scope User -ParticipateInCEIP \$true or \$false\.'
    
    # Remove the warning text
    $cleanedText = $InputText -replace $warningPattern, ''
    
    # Extract just the JSON part (everything from first { to last })
    if ($cleanedText -match '\{.*\}') {
        return $matches[0]
    }
    
    return $cleanedText
}

function get-inCluster {
    param(
        $server
    )
        
try{

    Import-Module Failoverclusters -ErrorAction Stop -WarningAction SilentlyContinue | Out-Null

    $cluster = Invoke-Command -ComputerName $server -Credential $osCreds -ErrorAction Stop -ScriptBlock{
        Get-Cluster -WarningAction SilentlyContinue
    }
    
    $clusterDetails = Invoke-Command -ComputerName $server -Credential $osCreds -ErrorAction Stop -ScriptBlock{
        Get-ClusterNode -Cluster $using:cluster -ErrorAction Stop -WarningAction SilentlyContinue
    }

    $xlrSuccess = $true
    $xlrStatus = "NORMAL"
    $xlrMessage = "Cluster found"
    $xlrData = @{
        Server = $server
        Cluster = $cluster.Name
        NodesInCluster = $clusterDetails.Name -join ", "
    }

}catch{
    $xlrSuccess = $false
    $xlrStatus = "NORMAL"
    $xlrMessage = "No cluster found"
    $xlrData = @{
        Server = $server
        Cluster = $null
        NodesInCluster = $null
    }
}
    
    
    $xlrResponse = "" | Select-Object success, status, message, data
    $xlrResponse.success = $xlrSuccess
    $xlrResponse.status = $xlrStatus
    $xlrResponse.message = $xlrMessage
    $xlrResponse.data = $xlrData
    
    # Convert to JSON and clean any VMware warnings
    $jsonOutput = $xlrResponse | ConvertTo-Json
    $cleanOutput = Clean-VMwareWarningFromJSON -InputText $jsonOutput

    return $cleanOutput
}

get-inCluster $serverName -WarningAction SilentlyContinue