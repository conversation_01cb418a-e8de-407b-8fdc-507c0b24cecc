# Business-Specific Domain Join with Cloud-Init Guide

This guide explains how to use your business configuration JSON files to automatically join EC2 instances to the correct Active Directory OUs using cloud-init and Image Builder.

## Overview

The solution combines:
- **AWS Image Builder**: Creates AMIs with cloud-init and domain join scripts pre-installed
- **Cloud-Init**: Executes business-specific configuration during instance launch
- **Business Config JSON**: Defines domain, OUs, and server roles for each business
- **User Data**: Provides business configuration and credentials to instances

## Architecture Flow

```
Business Config JSON → User Data Generation → EC2 Launch → Cloud-Init → Domain Join to Correct OU
```

## Your Business Configuration

Your `business1.json` defines:

```json
{
  "businessName": "Business1",
  "domain": "mud.internal.co.za",
  "basePath": "OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
  "serverOUs": {
    "SQLServer-2019": "OU=SQL Server,OU=Server 2019,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "SQLServer-2022": "OU=SQL Server,OU=Server 2022,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "WebServer-2019": "OU=Web Server,OU=Server 2019,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "WebServer-2022": "OU=Web Server,OU=Server 2022,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
  },
  "computerNamePrefix": "BUS1"
}
```

## Step-by-Step Usage

### 1. Build Custom AMI with Cloud-Init

First, build your AMI with the new cloud-init component:

```powershell
# Deploy with cloud-init support
.\deployment\deploy.ps1 -Region "us-east-1" -SecurityGroupId "sg-your-id" -SubnetId "subnet-your-id" -CreateIAMRole -StartBuild
```

The AMI will include:
- ✅ RSAT tools pre-installed
- ✅ Cloud-init configured
- ✅ Domain join scripts ready
- ✅ Business configuration templates

### 2. Store Domain Credentials (Recommended)

Store your domain credentials securely in AWS Systems Manager Parameter Store:

```powershell
# Store domain credentials
aws ssm put-parameter --name "/ec2/domain/username" --value "mud\administrator" --type "String"
aws ssm put-parameter --name "/ec2/domain/password" --value "YourDomainPassword" --type "SecureString"
```

### 3. Generate User Data for Specific Business/Role

Generate user data for a specific business and server role:

```powershell
# Generate user data for Business1 Web Server
.\deployment\generate-user-data.ps1 `
    -BusinessConfigPath "configs\business1.json" `
    -ServerRole "WebServer-2022" `
    -DomainUser "mud\administrator" `
    -DomainPassword "YourPassword" `
    -OutputPath "business1-webserver-userdata.ps1" `
    -UseParameterStore
```

### 4. Launch Instance with Automatic Domain Join

#### Option A: Use the Launch Script (Recommended)

```powershell
# Launch instance with automatic domain join
.\deployment\launch-business-instance.ps1 `
    -BusinessConfigPath "configs\business1.json" `
    -ServerRole "WebServer-2022" `
    -AMIId "ami-your-custom-ami-id" `
    -InstanceType "t3.medium" `
    -KeyName "your-key-pair" `
    -SecurityGroupId "sg-your-id" `
    -SubnetId "subnet-your-id" `
    -DomainUser "mud\administrator" `
    -DomainPassword "YourPassword" `
    -UseParameterStore
```

#### Option B: Manual EC2 Launch

```powershell
# Launch manually with generated user data
aws ec2 run-instances `
    --image-id ami-your-custom-ami-id `
    --count 1 `
    --instance-type t3.medium `
    --key-name your-key-pair `
    --security-group-ids sg-your-id `
    --subnet-id subnet-your-id `
    --user-data file://business1-webserver-userdata.ps1 `
    --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=Business1-WebServer},{Key=Business,Value=Business1},{Key=ServerRole,Value=WebServer-2022}]'
```

## What Happens During Launch

1. **Instance Starts**: EC2 instance boots from your custom AMI
2. **Cloud-Init Runs**: Executes user data script
3. **Config Applied**: Business configuration saved to instance
4. **Role Setup**: Server role-specific software installed (IIS for web servers, etc.)
5. **Domain Join**: Instance joins domain in correct OU
6. **Computer Rename**: Computer renamed with business prefix (e.g., BUS1-XXXX)
7. **Restart**: Instance restarts to complete domain join

## Monitoring and Troubleshooting

### Check Instance Status

```powershell
# Monitor instance
aws ec2 describe-instances --instance-ids i-your-instance-id

# Get console output
aws ec2 get-console-output --instance-id i-your-instance-id
```

### Check Logs on Instance

Connect to the instance and check logs:

```powershell
# User data execution log
Get-Content C:\Scripts\user-data.log

# Domain join log
Get-Content C:\Scripts\domain-join.log

# Cloud-init logs
Get-Content "C:\Program Files\Cloudbase Solutions\Cloudbase-Init\log\cloudbase-init.log"
```

### Verify Domain Join

```powershell
# Check if computer is domain joined
Get-ComputerInfo | Select-Object CsDomain, CsDomainRole

# Verify OU placement
Get-ADComputer -Identity $env:COMPUTERNAME -Properties DistinguishedName
```

## Multiple Business Support

Create configuration files for each business:

```
configs/
├── business1.json    # Business1 configuration
├── business2.json    # Business2 configuration
└── business3.json    # Business3 configuration
```

Launch instances for different businesses:

```powershell
# Business1 Web Server
.\deployment\launch-business-instance.ps1 -BusinessConfigPath "configs\business1.json" -ServerRole "WebServer-2022" ...

# Business2 SQL Server
.\deployment\launch-business-instance.ps1 -BusinessConfigPath "configs\business2.json" -ServerRole "SQLServer-2022" ...
```

## Server Role Automation

The system automatically installs role-specific software:

| Server Role | Automatic Installation |
|-------------|----------------------|
| **WebServer-2019/2022** | IIS, ASP.NET, HTTP Redirect |
| **SQLServer-2019/2022** | .NET Framework 3.5, SQL prep |
| **Custom Roles** | Add your own in the user data template |

## Security Best Practices

### 1. Use Parameter Store for Credentials

```powershell
# Always use Parameter Store in production
-UseParameterStore
```

### 2. Least Privilege Domain Account

Create a dedicated service account for domain joins:
- Only has rights to join computers to domain
- Only has rights to specific OUs
- Regular password rotation

### 3. Network Security

- Use private subnets
- Restrict security groups
- Enable VPC Flow Logs

### 4. Audit and Monitoring

- Enable CloudTrail
- Monitor Parameter Store access
- Log domain join activities

## Customization Examples

### Add Custom Server Role

Edit your business config:

```json
{
  "serverOUs": {
    "FileServer-2022": "OU=File Server,OU=Server 2022,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
  }
}
```

Update the user data generation script to handle the new role.

### Business-Specific Software

Modify the role installation section in `generate-user-data.ps1`:

```powershell
"FileServer-2022" { 
    @"
    Write-Log "Installing File Server role"
    Install-WindowsFeature -Name FS-FileServer -IncludeManagementTools
    Install-WindowsFeature -Name FS-DFS-Namespace -IncludeManagementTools
"@
}
```

## Troubleshooting Common Issues

### Domain Join Fails

1. **Check connectivity**: Ensure instance can reach domain controllers
2. **Verify credentials**: Check Parameter Store values
3. **OU permissions**: Ensure service account can join computers to target OU
4. **DNS resolution**: Verify domain name resolves correctly

### Cloud-Init Not Running

1. **Check service**: `Get-Service cloudbase-init`
2. **Verify installation**: Check if cloud-init is installed in AMI
3. **User data format**: Ensure user data is properly formatted

### Wrong OU Placement

1. **Check config**: Verify business configuration JSON
2. **Role mapping**: Ensure server role exists in serverOUs
3. **OU syntax**: Verify LDAP DN syntax is correct

## Cost Optimization

- Use Spot instances for development/testing
- Right-size instance types based on role
- Automate instance lifecycle management
- Use scheduled scaling for predictable workloads

## Next Steps

1. **Test with one business**: Start with Business1 configuration
2. **Verify domain join**: Check AD for correct OU placement
3. **Scale to multiple businesses**: Add more business configs
4. **Automate deployment**: Integrate with CI/CD pipelines
5. **Monitor and optimize**: Set up CloudWatch monitoring

This solution provides a scalable, secure way to automatically join EC2 instances to the correct Active Directory OUs based on business requirements and server roles.
