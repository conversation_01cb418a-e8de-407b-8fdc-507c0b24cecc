# Cloud-Init and Domain Join in AWS

This document explains how cloud-init handles setup in AWS and compares it to traditional methods, plus covers domain joining strategies for cloud deployments.

## How Cloud-Init Works in AWS

### Boot Process Flow
```
EC2 Instance Starts → Cloud-Init Runs → User Data Executed → Packer Connects
```

### User Data Mechanism

When you launch an EC2 instance, you can provide **User Data** - a script that runs during first boot. In your Packer template, this is handled by the `user_data_file` parameter pointing to `scripts/setup/aws-userdata.ps1`.

### Cloud-Init vs Traditional Methods

| Method | How Files Are Delivered | When It Runs |
|--------|------------------------|--------------|
| **Hyper-V** | CD/DVD ISO attached to VM | During Windows Setup (autounattend.xml) |
| **VMware** | Floppy disk attached to VM | During Windows Setup (autounattend.xml) |
| **AWS** | User Data via metadata service | After Windows boots (cloud-init) |

### AWS User Data Process

#### Step 1: Instance Metadata Service
```
EC2 Instance → Queries → http://***************/latest/user-data
                      ↓
                Returns your PowerShell script
```

### Understanding the AWS Instance Metadata Service (IMDS)

#### What is ***************?

The URL `http://***************/latest/user-data` refers to the **AWS EC2 Instance Metadata Service (IMDS)**:

- ******************* is a **link-local IP address** that AWS reserves for the Instance Metadata Service
- It's **only accessible from within the EC2 instance itself** - not from outside
- This is AWS's internal service that provides metadata about the running instance

#### How It Works

1. **From inside any EC2 instance**, you can query this IP address
2. **AWS intercepts these requests** and responds with instance-specific information
3. **No authentication required** - the fact that you're making the request from inside the instance is authentication enough

#### Available Metadata Endpoints

You can query various endpoints to get different information:

```powershell
# Get user data (your PowerShell script)
Invoke-RestMethod -Uri "http://***************/latest/user-data"

# Get instance ID
Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"

# Get instance region
Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/region"

# Get instance type
Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-type"

# Get security groups
Invoke-RestMethod -Uri "http://***************/latest/meta-data/security-groups"

# Get IAM role credentials (if instance has an IAM role)
Invoke-RestMethod -Uri "http://***************/latest/meta-data/iam/security-credentials/"

# Get instance tags (requires IMDSv2 or specific permissions)
Invoke-RestMethod -Uri "http://***************/latest/meta-data/tags/instance"
```

#### In Your User Data Context

When you launch an EC2 instance with user data:

1. **You provide the script** (your PowerShell code) when launching the instance
2. **AWS stores it** in the metadata service
3. **EC2Launch/EC2Config service** on Windows automatically queries `http://***************/latest/user-data`
4. **Gets your script back** and executes it
5. **Your script runs** with SYSTEM privileges during first boot

#### Security Considerations

**IMDSv1 vs IMDSv2:**
```powershell
# IMDSv1 (older, less secure)
Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"

# IMDSv2 (newer, more secure - requires token)
$token = Invoke-RestMethod -Uri "http://***************/latest/api/token" -Method PUT -Headers @{"X-aws-ec2-metadata-token-ttl-seconds"="21600"}
Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id" -Headers @{"X-aws-ec2-metadata-token"=$token}
```

**Access Control:**
- **Only accessible from the instance itself**
- **Cannot be accessed from other instances or the internet**
- **Can be disabled** if you don't want the instance to access metadata

#### Practical Example

Here's what happens when your user data script runs:

```powershell
# This is what EC2Launch does automatically:
$userData = Invoke-RestMethod -Uri "http://***************/latest/user-data"

# $userData now contains your entire PowerShell script:
# <powershell>
# # AWS EC2 UserData script to enable WinRM for Packer
# Write-Host "Starting AWS UserData script for WinRM configuration..."
# ... rest of your script ...
# </powershell>

# EC2Launch then executes this script
```

#### Why This Design?

1. **Security**: No credentials needed - physical access to the instance is the authentication
2. **Simplicity**: Standard HTTP requests, works with any programming language
3. **Reliability**: Always available, no external dependencies
4. **Consistency**: Same interface across all AWS regions and instance types

So `***************` is essentially AWS's "internal web server" that every EC2 instance can talk to for getting information about itself!

#### Step 2: EC2Config/EC2Launch Execution
- **Windows Server 2016+**: EC2Launch service runs user data
- **Windows Server 2012**: EC2Config service runs user data
- Scripts run with **SYSTEM privileges**

#### Step 3: Packer Connection
The user data script enables WinRM, creates HTTPS listener with self-signed cert, opens firewall port 5986, then Packer can connect via WinRM over HTTPS.

### Key Advantages of Cloud-Init Approach

#### No Physical Media Needed
- No floppy disks or CD/DVD ISOs to create
- User data is delivered via AWS metadata service
- Works with any AMI that has EC2Config/EC2Launch

#### Dynamic Configuration
```powershell
# User data can query AWS metadata for dynamic config
$instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"
$region = Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/region"
```

#### Cloud-Native Integration
- Integrates with AWS Systems Manager
- Can download files from S3
- Can join Active Directory domains
- Can configure CloudWatch logging

### Comparison: Setup Methods

#### Traditional (Hyper-V/VMware)
```
Windows ISO → autounattend.xml → Automated Installation → WinRM Setup
     ↓              ↓                      ↓                    ↓
  Boot from     Answer file         Unattended setup      Packer connects
   media       on floppy/CD        during installation
```

#### AWS Cloud-Init
```
AMI Boot → Windows Starts → EC2Launch → User Data Script → WinRM Setup
    ↓           ↓              ↓             ↓                ↓
 Pre-built   Normal boot   Cloud service   Your script    Packer connects
   image                   runs script     enables WinRM
```

## Platform Requirements for ISO Creation Tools

### Linux Requirements by Builder

| Builder | ISO Creation Tool Needed? | Why? |
|---------|---------------------------|------|
| **Hyper-V** | ✅ **YES** (for `cd_files`) | Generation 2 VMs need CD/DVD for unattended files |
| **VMware** | ❌ **NO** | Uses `floppy_files` - no ISO creation needed |
| **AWS** | ❌ **NO** | Uses `user_data_file` - cloud-init handles setup |

### Linux ISO Creation Tools

Linux typically has these tools **pre-installed** or easily available:

```bash
# Most Linux distros have one of these by default:
which xorriso     # Usually available
which mkisofs     # Common on older systems  
which genisoimage # Alternative to mkisofs

# Install if needed:
# Ubuntu/Debian
sudo apt install xorriso

# RHEL/CentOS/Fedora  
sudo yum install xorriso
# or
sudo dnf install xorriso
```

### Key Differences

1. **Hyper-V** = Only platform that needs ISO creation tools (for `cd_files`)
2. **VMware** = Uses floppy drives (no ISO creation needed)
3. **AWS** = Uses cloud-init/user-data (no local files needed)
4. **Linux** = Usually has `xorriso`/`mkisofs` pre-installed

**Bottom line:** If you run VMware or AWS builders from Linux, you won't need to install additional ISO creation tools. Only Hyper-V requires them, and Linux typically has them already available.

## Domain Joining in AWS

### Current On-Premises Process (Traditional)
```
1. Pre-create computer object in specific OU
2. Use service account with delegated permissions
3. Join domain during deployment
4. Computer lands in predetermined OU
```

### AWS Cloud-Init Domain Join Methods

#### Method 1: AWS Systems Manager (Recommended)

```powershell
<powershell>
# In your user data script
# Download and execute AWS domain join SSM document
$document = "AWS-JoinDirectoryServiceDomain"
$parameters = @{
    "directoryId" = "d-**********"
    "directoryName" = "corp.company.com"
    "dnsIpAddresses" = @("**********", "**********")
}

# This uses AWS Directory Service integration
Start-SSMAssociation -Name $document -Parameters $parameters
</powershell>
```

#### Method 2: Traditional Domain Join via User Data

```powershell
<powershell>
# Store domain credentials in AWS Secrets Manager or Parameter Store
$domainUser = (Get-SSMParameter -Name "/company/domain/join-user" -WithDecryption $true).Value
$domainPass = (Get-SSMParameter -Name "/company/domain/join-password" -WithDecryption $true).Value | ConvertTo-SecureString -AsPlainText -Force
$credential = New-Object System.Management.Automation.PSCredential($domainUser, $domainPass)

# Get instance metadata for computer name
$instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"
$computerName = "AWS-$instanceId"

# Join domain
Add-Computer -DomainName "corp.company.com" -Credential $credential -NewName $computerName -Restart
</powershell>
```

#### Method 3: AWS Directory Service Seamless Join

For **AWS Managed Microsoft AD** or **AD Connector**:

```powershell
<powershell>
# Instance automatically joins domain if:
# 1. Instance is in VPC with Directory Service
# 2. Instance has proper IAM role
# 3. Security groups allow domain traffic

# The join happens automatically - no credentials needed!
# AWS handles the domain join using the directory service
</powershell>
```

## OU Placement Strategies in AWS

### Option 1: Pre-Create Computer Objects (Like Current Process)

```powershell
<powershell>
# Use service account to pre-create computer object
$instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"
$region = Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/region"
$computerName = "AWS-$region-$instanceId"

# Pre-create in specific OU
$targetOU = "OU=AWS-Servers,OU=Cloud,DC=corp,DC=company,DC=com"

# Service account creates computer object first
New-ADComputer -Name $computerName -Path $targetOU -Enabled $true

# Then join domain
Add-Computer -DomainName "corp.company.com" -Credential $credential -NewName $computerName
</powershell>
```

### Option 2: Post-Join OU Movement

```powershell
<powershell>
# Join domain first (lands in default Computers container)
Add-Computer -DomainName "corp.company.com" -Credential $credential

# Then move to correct OU
$computerDN = "CN=$env:COMPUTERNAME,CN=Computers,DC=corp,DC=company,DC=com"
$targetOU = "OU=AWS-Servers,OU=Cloud,DC=corp,DC=company,DC=com"
Move-ADObject -Identity $computerDN -TargetPath $targetOU
</powershell>
```

### Option 3: Dynamic OU Based on AWS Tags

```powershell
<powershell>
# Get instance tags to determine OU placement
$instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"
$region = Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/region"

# Query AWS API for instance tags
$tags = (Get-EC2Tag -Filter @{Name="resource-id";Values=$instanceId}).Tags
$environment = ($tags | Where-Object {$_.Key -eq "Environment"}).Value
$application = ($tags | Where-Object {$_.Key -eq "Application"}).Value

# Determine OU based on tags
$targetOU = "OU=$application,OU=$environment,OU=AWS,DC=corp,DC=company,DC=com"

# Create computer object in correct OU
New-ADComputer -Name $env:COMPUTERNAME -Path $targetOU
Add-Computer -DomainName "corp.company.com" -Credential $credential
</powershell>
```

## Security Considerations

### Credential Management

| Method | Pros | Cons |
|--------|------|------|
| **AWS Secrets Manager** | Encrypted, rotatable, audited | Additional AWS cost |
| **Systems Manager Parameter Store** | Free tier, encrypted | Less features than Secrets Manager |
| **IAM Roles + Directory Service** | No stored credentials | Requires AWS Managed AD |
| **Instance Metadata** | Simple | Credentials visible in user data |

### Service Account Permissions

```powershell
# Minimal permissions needed for domain join service account:
# 1. Create computer objects in target OUs
# 2. Reset computer account passwords
# 3. Validated write to DNS host name
# 4. Validated write to service principal name

# Example delegation:
Grant-ADPermission -Identity "CN=AWS-DomainJoin-SvcAcct,OU=Service Accounts,DC=corp,DC=company,DC=com" `
                   -TargetOU "OU=AWS-Servers,OU=Cloud,DC=corp,DC=company,DC=com" `
                   -Rights "CreateChild,DeleteChild,WriteProperty"
```

## Integration with Your Packer Templates

You could add domain join capability to your AWS template by creating a new script like `scripts/domain-join-aws.ps1` that handles the cloud-specific domain join logic, keeping it separate from your on-premises domain join processes.

## Key Advantages of Cloud-Init Domain Join

1. **Dynamic Configuration**: Can use instance metadata, tags, and AWS APIs
2. **Credential Security**: Integration with AWS Secrets Manager/Parameter Store
3. **Automation**: No manual computer object pre-creation required
4. **Scalability**: Works with Auto Scaling Groups
5. **Auditability**: All actions logged in CloudTrail

The main difference is that cloud-init gives you **runtime flexibility** - you can make domain join decisions based on the actual deployed environment rather than having to pre-configure everything.

## AWS EC2 Tagging for OU Placement

### EC2 Tag System

#### Base/Default EC2 Tags
AWS automatically creates some tags, but most are optional:

| Tag Key | Description | Auto-Created? |
|---------|-------------|---------------|
| `Name` | Instance name | ❌ (user-defined) |
| `aws:autoscaling:groupName` | Auto Scaling Group name | ✅ (if using ASG) |
| `aws:ec2:fleet-id` | EC2 Fleet ID | ✅ (if using Fleet) |
| `aws:ec2spot:fleet-request-id` | Spot Fleet ID | ✅ (if using Spot) |

#### Custom Tags (Your Choice)
You can create any custom tags you want! For complex OU structures, here's how to map them.

### Mapping OU Structure to Tags

#### Example OU Structure:
```
OU=Server 2022,OU=Windows Server,OU=Servers,OU=GTI,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za
```

#### Suggested Tag Structure:

| Tag Key | Tag Value | OU Component |
|---------|-----------|--------------|
| `OSVersion` | `Server 2022` | OU=Server 2022 |
| `OSFamily` | `Windows Server` | OU=Windows Server |
| `ServerType` | `Servers` | OU=Servers |
| `BusinessUnit` | `GTI` | OU=GTI |
| `Division` | `Businesses` | OU=Businesses |
| `Environment` | `Production` / `Development` / `Test` | (Additional classification) |
| `Application` | `WebServer` / `Database` / `FileServer` | (Additional classification) |

### Example Tag Implementation

#### In Terraform (when creating instances):
```hcl
resource "aws_instance" "windows_server" {
  ami           = "ami-12345678"
  instance_type = "t3.medium"

  tags = {
    Name         = "GTI-WEB-01"
    OSVersion    = "Server 2022"
    OSFamily     = "Windows Server"
    ServerType   = "Servers"
    BusinessUnit = "GTI"
    Division     = "Businesses"
    Environment  = "Production"
    Application  = "WebServer"
    Owner        = "IT-Team"
    CostCenter   = "12345"
  }
}
```

#### In Packer (during AMI creation):
```hcl
# In your aws.pkr.hcl template
source "amazon-ebs" "windows-server-2022" {
  # ... other configuration ...

  tags = {
    Name         = "Windows-Server-2022-Base-AMI"
    OSVersion    = "Server 2022"
    OSFamily     = "Windows Server"
    ServerType   = "Servers"
    BaseImage    = "true"
    CreatedBy    = "Packer"
    BuildDate    = "{{timestamp}}"
  }

  run_tags = {
    Name = "Packer-Windows-Server-2022-Build"
    Purpose = "AMI-Creation"
  }
}
```

### Dynamic OU Construction from Tags

#### PowerShell Script for User Data:
```powershell
<powershell>
# Get instance metadata
$instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"
$region = Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/region"

# Get instance tags
$tags = (Get-EC2Tag -Filter @{Name="resource-id";Values=$instanceId} -Region $region).Tags

# Extract OU components from tags
$osVersion = ($tags | Where-Object {$_.Key -eq "OSVersion"}).Value
$osFamily = ($tags | Where-Object {$_.Key -eq "OSFamily"}).Value
$serverType = ($tags | Where-Object {$_.Key -eq "ServerType"}).Value
$businessUnit = ($tags | Where-Object {$_.Key -eq "BusinessUnit"}).Value
$division = ($tags | Where-Object {$_.Key -eq "Division"}).Value

# Construct OU path dynamically
$ouPath = "OU=$osVersion,OU=$osFamily,OU=$serverType,OU=$businessUnit,OU=$division,DC=mud,DC=internal,DC=co,DC=za"

Write-Host "Constructed OU Path: $ouPath"

# Get domain join credentials from Parameter Store
$domainUser = (Get-SSMParameter -Name "/mud/domain/join-user" -WithDecryption $true -Region $region).Value
$domainPass = (Get-SSMParameter -Name "/mud/domain/join-password" -WithDecryption $true -Region $region).Value | ConvertTo-SecureString -AsPlainText -Force
$credential = New-Object System.Management.Automation.PSCredential($domainUser, $domainPass)

# Create computer name based on tags and instance
$environment = ($tags | Where-Object {$_.Key -eq "Environment"}).Value
$application = ($tags | Where-Object {$_.Key -eq "Application"}).Value
$computerName = "$businessUnit-$application-$($instanceId.Substring($instanceId.Length-4))"

# Pre-create computer object in correct OU
try {
    New-ADComputer -Name $computerName -Path $ouPath -Enabled $true -Credential $credential
    Write-Host "Computer object created in OU: $ouPath"
} catch {
    Write-Host "Failed to create computer object: $($_.Exception.Message)"
}

# Join domain
Add-Computer -DomainName "mud.internal.co.za" -Credential $credential -NewName $computerName -OUPath $ouPath -Restart
</powershell>
```

### Advanced Tag Strategies

#### Hierarchical Tags:
```hcl
tags = {
  # Hierarchical structure
  "OU:Level1" = "Businesses"
  "OU:Level2" = "GTI"
  "OU:Level3" = "Servers"
  "OU:Level4" = "Windows Server"
  "OU:Level5" = "Server 2022"

  # Functional tags
  "Environment" = "Production"
  "Application" = "WebServer"
  "Owner" = "<EMAIL>"
  "CostCenter" = "IT-12345"
  "Backup" = "Daily"
  "Monitoring" = "Enabled"
}
```

#### Dynamic OU Construction from Hierarchical Tags:
```powershell
# Get all OU-related tags and sort them
$ouTags = $tags | Where-Object {$_.Key -like "OU:*"} | Sort-Object Key

# Build OU path dynamically
$ouComponents = @()
foreach ($tag in $ouTags) {
    $ouComponents += "OU=$($tag.Value)"
}

# Add domain components
$ouComponents += @("DC=mud", "DC=internal", "DC=co", "DC=za")

# Join into full OU path
$ouPath = $ouComponents -join ","
```

### Tag Validation and Defaults

#### PowerShell with Fallbacks:
```powershell
# Function to get tag value with fallback
function Get-TagValue {
    param($tags, $tagKey, $defaultValue)
    $value = ($tags | Where-Object {$_.Key -eq $tagKey}).Value
    return if ($value) { $value } else { $defaultValue }
}

# Get OU components with defaults
$osVersion = Get-TagValue -tags $tags -tagKey "OSVersion" -defaultValue "Server 2022"
$osFamily = Get-TagValue -tags $tags -tagKey "OSFamily" -defaultValue "Windows Server"
$serverType = Get-TagValue -tags $tags -tagKey "ServerType" -defaultValue "Servers"
$businessUnit = Get-TagValue -tags $tags -tagKey "BusinessUnit" -defaultValue "Default"
$division = Get-TagValue -tags $tags -tagKey "Division" -defaultValue "Businesses"
```

## Benefits of Tag-Based OU Placement

1. **Flexibility**: Change OU structure without modifying code
2. **Consistency**: Same tagging strategy across all AWS resources
3. **Automation**: No manual OU specification needed
4. **Scalability**: Works with Auto Scaling Groups
5. **Governance**: Enforce tagging policies via AWS Config
6. **Cost Management**: Track costs by business unit/application
7. **Compliance**: Audit trail of resource classification

## Tag Governance

### AWS Config Rules for Tag Enforcement:
```json
{
  "ConfigRuleName": "required-tags-ec2",
  "Source": {
    "Owner": "AWS",
    "SourceIdentifier": "REQUIRED_TAGS"
  },
  "InputParameters": {
    "requiredTagKeys": "OSVersion,OSFamily,ServerType,BusinessUnit,Division,Environment"
  }
}
```

## Configuration-Driven OU Mapping (Recommended for Complex Environments)

### The Challenge with Tag-Based OU Construction

While tag-based OU construction works well for standardized environments, it becomes problematic when:
- **OU names vary between businesses** (e.g., "SQL Server" vs "Database Servers")
- **OU structures differ** between organizations
- **Naming conventions change** over time

### Solution: Configuration-Driven OU Mapping

Instead of constructing OU paths from tags, store **complete OU mappings** in configuration files.

#### OU Mapping Configuration Structure

Store business-specific OU mappings in S3:

```
s3://your-company-config-bucket/
├── business-configs/
│   ├── Business1/
│   │   ├── ou-mappings.json          # Complete OU paths for this business
│   │   ├── firewall-rules.json
│   │   └── gpo-settings.json
│   ├── Business2/
│   │   ├── ou-mappings.json          # Different OU names/structure
│   │   ├── firewall-rules.json
│   │   └── gpo-settings.json
│   └── Business3/
│       ├── ou-mappings.json
│       ├── firewall-rules.json
│       └── gpo-settings.json
```

#### Example OU Mapping Files

**Business1 OU Mappings** (`business-configs/Business1/ou-mappings.json`):
```json
{
  "businessName": "Business1",
  "domain": "mud.internal.co.za",
  "basePath": "OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
  "serverOUs": {
    "SQLServer-2019": "OU=SQL Server,OU=Server 2019,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "SQLServer-2022": "OU=SQL Server,OU=Server 2022,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "WebServer-2019": "OU=Web Server,OU=Server 2019,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "WebServer-2022": "OU=Web Server,OU=Server 2022,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
  },
  "computerNamePrefix": "BUS1"
}
```

**Business2 OU Mappings** (different naming convention):
```json
{
  "businessName": "Business2",
  "domain": "mud.internal.co.za",
  "basePath": "OU=Business2,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
  "serverOUs": {
    "SQLServer-2019": "OU=Database Servers,OU=2019 Servers,OU=Windows,OU=Servers,OU=Business2,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "SQLServer-2022": "OU=Database Servers,OU=2022 Servers,OU=Windows,OU=Servers,OU=Business2,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "WebServer-2019": "OU=Application Servers,OU=2019 Servers,OU=Windows,OU=Servers,OU=Business2,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za",
    "WebServer-2022": "OU=Application Servers,OU=2022 Servers,OU=Windows,OU=Servers,OU=Business2,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
  },
  "computerNamePrefix": "B2"
}
```

#### Enhanced User Data Script with Configuration-Driven OU Mapping

```powershell
# scripts/setup/aws-userdata-enhanced.ps1
<powershell>
Write-Host "Starting business-aware deployment script..." -ForegroundColor Green

# Get instance metadata
$instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"
$region = Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/region"

# Get instance tags
$tags = (Get-EC2Tag -Filter @{Name="resource-id";Values=$instanceId} -Region $region).Tags

# Extract deployment context from tags
$business = ($tags | Where-Object {$_.Key -eq "Business"}).Value
$serverRole = ($tags | Where-Object {$_.Key -eq "ServerRole"}).Value
$osVersion = ($tags | Where-Object {$_.Key -eq "OSVersion"}).Value

Write-Host "Deployment Context:" -ForegroundColor Cyan
Write-Host "  Business: $business" -ForegroundColor White
Write-Host "  Server Role: $serverRole" -ForegroundColor White
Write-Host "  OS Version: $osVersion" -ForegroundColor White

# Validate required tags
if (-not $business -or -not $serverRole -or -not $osVersion) {
    Write-Host "ERROR: Missing required tags (Business, ServerRole, OSVersion)" -ForegroundColor Red
    exit 1
}

# Download business-specific OU mappings
$configBucket = "your-company-config-bucket"
try {
    Write-Host "Downloading OU mappings for $business..." -ForegroundColor Yellow
    $ouMappingsJson = Read-S3Object -BucketName $configBucket -Key "business-configs/$business/ou-mappings.json"
    $ouMappings = $ouMappingsJson | ConvertFrom-Json

    # Construct the OU key
    $ouKey = "$serverRole-$osVersion"
    $targetOU = $ouMappings.serverOUs.$ouKey

    if (-not $targetOU) {
        Write-Host "ERROR: No OU mapping found for $ouKey in business $business" -ForegroundColor Red
        Write-Host "Available mappings:" -ForegroundColor Yellow
        $ouMappings.serverOUs | Get-Member -MemberType NoteProperty | ForEach-Object { Write-Host "  $($_.Name)" }
        exit 1
    }

    Write-Host "Target OU: $targetOU" -ForegroundColor Green

    # Generate computer name using business prefix
    $computerName = "$($ouMappings.computerNamePrefix)-$serverRole-$($instanceId.Substring($instanceId.Length-4))"
    Write-Host "Computer Name: $computerName" -ForegroundColor Green

} catch {
    Write-Host "ERROR: Failed to download OU mappings: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Download and apply business-specific configurations
try {
    Write-Host "Applying business-specific configurations..." -ForegroundColor Yellow

    # Firewall rules
    $firewallRules = Read-S3Object -BucketName $configBucket -Key "business-configs/$business/firewall-rules.json" | ConvertFrom-Json
    foreach ($rule in $firewallRules) {
        New-NetFirewallRule -DisplayName $rule.Name -Direction $rule.Direction -LocalPort $rule.Port -Protocol $rule.Protocol -Action $rule.Action
        Write-Host "  Applied firewall rule: $($rule.Name)" -ForegroundColor White
    }

    # Role-specific configuration
    if ($serverRole -eq "SQLServer") {
        $sqlConfig = Read-S3Object -BucketName $configBucket -Key "role-configs/SQLServer/sql-server-config.json" | ConvertFrom-Json
        Enable-WindowsOptionalFeature -Online -FeatureName $sqlConfig.WindowsFeatures -All
        Write-Host "  Applied SQL Server features" -ForegroundColor White
    }

} catch {
    Write-Host "Warning: Could not apply some configurations: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Enable WinRM for Packer
Write-Host "Configuring WinRM..." -ForegroundColor Yellow
Enable-PSRemoting -Force -SkipNetworkProfileCheck
winrm quickconfig -q
winrm set winrm/config/winrs '@{MaxMemoryPerShellMB="512"}'
winrm set winrm/config '@{MaxTimeoutms="1800000"}'
winrm set winrm/config/service '@{AllowUnencrypted="false"}'
winrm set winrm/config/service/auth '@{Basic="true"}'

# Create self-signed certificate for HTTPS
$cert = New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "cert:\LocalMachine\My"
winrm create winrm/config/Listener?Address=*+Transport=HTTPS "@{Hostname=`"localhost`";CertificateThumbprint=`"$($cert.Thumbprint)`"}"

# Configure firewall for WinRM HTTPS
netsh advfirewall firewall add rule name="WinRM-HTTPS" dir=in localport=5986 protocol=TCP action=allow
Restart-Service winrm

# Domain join
Write-Host "Joining domain..." -ForegroundColor Yellow
$domainUser = (Get-SSMParameter -Name "/mud/domain/join-user" -WithDecryption $true -Region $region).Value
$domainPass = (Get-SSMParameter -Name "/mud/domain/join-password" -WithDecryption $true -Region $region).Value | ConvertTo-SecureString -AsPlainText -Force
$credential = New-Object System.Management.Automation.PSCredential($domainUser, $domainPass)

# Pre-create computer object in the correct OU
try {
    New-ADComputer -Name $computerName -Path $targetOU -Enabled $true -Credential $credential
    Write-Host "Computer object created in: $targetOU" -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not pre-create computer object: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Join domain with specific OU
Add-Computer -DomainName $ouMappings.domain -Credential $credential -NewName $computerName -OUPath $targetOU

Write-Host "Configuration complete. System will restart..." -ForegroundColor Green
</powershell>
```

#### Terraform Deployment Example

```hcl
# Business1 SQL Server
resource "aws_instance" "business1_sql" {
  ami           = var.base_windows_2022_ami
  instance_type = "r5.xlarge"

  iam_instance_profile = aws_iam_instance_profile.server_deployment.name
  user_data = file("${path.module}/scripts/aws-userdata-enhanced.ps1")

  tags = {
    Name       = "Business1-SQL-01"
    Business   = "Business1"        # Maps to business-configs/Business1/
    ServerRole = "SQLServer"        # Combined with OSVersion for OU lookup
    OSVersion  = "2022"             # Creates key "SQLServer-2022"
    Environment = "Production"
  }
}

# Business2 SQL Server (same tags, different OU result)
resource "aws_instance" "business2_sql" {
  ami           = var.base_windows_2022_ami  # Same AMI
  instance_type = "r5.xlarge"

  iam_instance_profile = aws_iam_instance_profile.server_deployment.name
  user_data = file("${path.module}/scripts/aws-userdata-enhanced.ps1")  # Same script

  tags = {
    Name       = "Business2-SQL-01"
    Business   = "Business2"        # Maps to business-configs/Business2/
    ServerRole = "SQLServer"        # Same role
    OSVersion  = "2022"             # Same OS
    Environment = "Production"
  }
  # Results in completely different OU path based on Business2's mappings!
}
```

### Advantages of Configuration-Driven Approach

#### 1. Handles OU Variations
- ✅ Each business can have completely different OU structures
- ✅ No assumptions about naming conventions
- ✅ Explicit mapping prevents errors

#### 2. Maintainable
- ✅ OU changes only require updating JSON files
- ✅ No code changes needed for new businesses
- ✅ Version control for OU mappings

#### 3. Flexible
- ✅ Can add new server roles easily
- ✅ Supports different computer naming conventions per business
- ✅ Can handle different domain names if needed

#### 4. Robust Error Handling
- ✅ Validates OU mappings exist before attempting domain join
- ✅ Clear error messages when mappings are missing
- ✅ Lists available options when lookup fails

### Recommended Architecture: Base AMI + Runtime Configuration

For complex multi-business environments, use:

1. **Single Clean Base AMI** - Common, stable configuration across all businesses
2. **Configuration-Driven OU Mapping** - Business-specific OU paths stored in S3
3. **Runtime Configuration** - Business and role-specific settings applied at deployment
4. **Consistent Tagging Strategy** - Simple tags that map to configuration files

This approach provides the flexibility to handle different OU structures per business while maintaining a single, clean base AMI and deployment process.

## Summary

The beauty of your Packer setup is that it abstracts these differences - the same provisioning scripts run regardless of whether the initial setup used autounattend.xml (local/VMware) or cloud-init (AWS). Cloud-init provides powerful runtime capabilities that traditional methods cannot match, especially for dynamic configuration and cloud-native integrations.

For complex multi-business environments with varying OU structures, the configuration-driven approach provides the most robust and maintainable solution. It combines the benefits of tag-based deployment with the reliability of explicit configuration mappings, ensuring successful domain joins regardless of organizational differences.
