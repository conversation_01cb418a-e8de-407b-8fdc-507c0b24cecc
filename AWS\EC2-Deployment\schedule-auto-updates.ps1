# AWS Image Builder Auto-Update Scheduler
# This script sets up automatic base AMI updates using Windows Task Scheduler

param(
    [Parameter(Mandatory=$false)]
    [string]$ScheduleType = "Weekly",  # Weekly, Monthly, Daily
    
    [Parameter(Mandatory=$false)]
    [string]$ScheduleTime = "02:00",   # 24-hour format
    
    [Parameter(Mandatory=$false)]
    [string]$ScheduleDay = "Sunday",   # For weekly: Monday, Tuesday, etc.
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "us-east-1",
    
    [Parameter(Mandatory=$false)]
    [string]$LogPath = "C:\Logs\ImageBuilder",
    
    [Parameter(Mandatory=$false)]
    [switch]$AutoStartBuild = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$UseAMIName = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$RemoveSchedule = $false
)

Write-Host "AWS Image Builder Auto-Update Scheduler" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

$taskName = "AWS-ImageBuilder-AutoUpdate"
$scriptPath = Join-Path $PSScriptRoot "update-base-ami.ps1"

# Function to remove existing scheduled task
function Remove-AutoUpdateTask {
    try {
        $existingTask = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($existingTask) {
            Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
            Write-Host "✓ Removed existing scheduled task: $taskName" -ForegroundColor Green
        } else {
            Write-Host "✓ No existing scheduled task found" -ForegroundColor Yellow
        }
        return $true
    } catch {
        Write-Error "Failed to remove scheduled task: $($_.Exception.Message)"
        return $false
    }
}

# Function to create log directory
function New-LogDirectory {
    param([string]$Path)
    
    try {
        if (!(Test-Path $Path)) {
            New-Item -ItemType Directory -Path $Path -Force | Out-Null
            Write-Host "✓ Created log directory: $Path" -ForegroundColor Green
        } else {
            Write-Host "✓ Log directory exists: $Path" -ForegroundColor Yellow
        }
        return $true
    } catch {
        Write-Error "Failed to create log directory: $($_.Exception.Message)"
        return $false
    }
}

# Function to create wrapper script
function New-WrapperScript {
    param(
        [string]$WrapperPath,
        [string]$UpdateScriptPath,
        [string]$LogDirectory,
        [string]$AWSRegion,
        [bool]$StartBuild,
        [bool]$UseNamePattern
    )
    
    try {
        $wrapperContent = @"
# AWS Image Builder Auto-Update Wrapper Script
# Generated on $(Get-Date)

`$ErrorActionPreference = "Continue"
`$logFile = "$LogDirectory\auto-update-`$(Get-Date -Format 'yyyyMMdd-HHmmss').log"

# Function to write log with timestamp
function Write-Log {
    param([string]`$Message, [string]`$Level = "INFO")
    `$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    `$logEntry = "[$timestamp] [`$Level] `$Message"
    Write-Host `$logEntry
    `$logEntry | Out-File -FilePath `$logFile -Append -Encoding UTF8
}

try {
    Write-Log "Starting AWS Image Builder auto-update process"
    Write-Log "Region: $AWSRegion"
    Write-Log "Start Build: $StartBuild"
    Write-Log "Use AMI Name Pattern: $UseNamePattern"
    
    # Set AWS region
    `$env:AWS_DEFAULT_REGION = "$AWSRegion"
    
    # Build parameters
    `$params = @{
        Region = "$AWSRegion"
        StartBuild = `$$StartBuild
        UseAMIName = `$$UseNamePattern
    }
    
    # Execute update script
    Write-Log "Executing update script: $UpdateScriptPath"
    & "$UpdateScriptPath" @params
    
    if (`$LASTEXITCODE -eq 0) {
        Write-Log "Auto-update completed successfully" "SUCCESS"
    } else {
        Write-Log "Auto-update failed with exit code: `$LASTEXITCODE" "ERROR"
    }
    
} catch {
    Write-Log "Auto-update failed with exception: `$(`$_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: `$(`$_.Exception.StackTrace)" "ERROR"
} finally {
    Write-Log "Auto-update process finished"
    
    # Clean up old log files (keep last 30 days)
    try {
        `$cutoffDate = (Get-Date).AddDays(-30)
        Get-ChildItem "$LogDirectory\auto-update-*.log" | Where-Object { `$_.CreationTime -lt `$cutoffDate } | Remove-Item -Force
        Write-Log "Cleaned up old log files"
    } catch {
        Write-Log "Failed to clean up old log files: `$(`$_.Exception.Message)" "WARNING"
    }
}
"@

        $wrapperContent | Out-File -FilePath $WrapperPath -Encoding UTF8
        Write-Host "✓ Created wrapper script: $WrapperPath" -ForegroundColor Green
        return $true
        
    } catch {
        Write-Error "Failed to create wrapper script: $($_.Exception.Message)"
        return $false
    }
}

# Function to create scheduled task
function New-AutoUpdateTask {
    param(
        [string]$TaskName,
        [string]$WrapperScript,
        [string]$Schedule,
        [string]$Time,
        [string]$Day
    )
    
    try {
        Write-Host "Creating scheduled task: $TaskName" -ForegroundColor Yellow
        
        # Create task action
        $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File `"$WrapperScript`""
        
        # Create task trigger based on schedule type
        switch ($Schedule.ToLower()) {
            "daily" {
                $trigger = New-ScheduledTaskTrigger -Daily -At $Time
                Write-Host "Schedule: Daily at $Time" -ForegroundColor Cyan
            }
            "weekly" {
                $dayOfWeek = [System.DayOfWeek]::$Day
                $trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek $dayOfWeek -At $Time
                Write-Host "Schedule: Weekly on $Day at $Time" -ForegroundColor Cyan
            }
            "monthly" {
                # Run on first Sunday of each month
                $trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Sunday -WeeksInterval 4 -At $Time
                Write-Host "Schedule: Monthly (first Sunday) at $Time" -ForegroundColor Cyan
            }
            default {
                throw "Invalid schedule type: $Schedule. Use Daily, Weekly, or Monthly."
            }
        }
        
        # Create task principal (run as SYSTEM)
        $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
        
        # Create task settings
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -RunOnlyIfNetworkAvailable
        
        # Register the task
        Register-ScheduledTask -TaskName $TaskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description "Automatically updates AWS Image Builder base AMI and triggers new builds"
        
        Write-Host "✓ Scheduled task created successfully" -ForegroundColor Green
        
        # Display task information
        $task = Get-ScheduledTask -TaskName $TaskName
        Write-Host "`nTask Details:" -ForegroundColor Yellow
        Write-Host "  Name: $($task.TaskName)" -ForegroundColor White
        Write-Host "  State: $($task.State)" -ForegroundColor White
        Write-Host "  Next Run: $((Get-ScheduledTask -TaskName $TaskName | Get-ScheduledTaskInfo).NextRunTime)" -ForegroundColor White
        
        return $true
        
    } catch {
        Write-Error "Failed to create scheduled task: $($_.Exception.Message)"
        return $false
    }
}

# Main execution
try {
    # Check if running as administrator
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    if (!$principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
        Write-Error "This script must be run as Administrator to create scheduled tasks."
        exit 1
    }
    
    # Remove existing schedule if requested
    if ($RemoveSchedule) {
        if (Remove-AutoUpdateTask) {
            Write-Host "`n✓ Auto-update schedule removed successfully!" -ForegroundColor Green
        }
        exit 0
    }
    
    # Validate update script exists
    if (!(Test-Path $scriptPath)) {
        Write-Error "Update script not found: $scriptPath"
        Write-Host "Make sure you're running this from the deployment directory." -ForegroundColor Yellow
        exit 1
    }
    
    # Create log directory
    if (!(New-LogDirectory -Path $LogPath)) {
        exit 1
    }
    
    # Remove existing task if it exists
    Remove-AutoUpdateTask | Out-Null
    
    # Create wrapper script
    $wrapperPath = Join-Path $LogPath "auto-update-wrapper.ps1"
    if (!(New-WrapperScript -WrapperPath $wrapperPath -UpdateScriptPath $scriptPath -LogDirectory $LogPath -AWSRegion $Region -StartBuild $AutoStartBuild -UseNamePattern $UseAMIName)) {
        exit 1
    }
    
    # Create scheduled task
    if (!(New-AutoUpdateTask -TaskName $taskName -WrapperScript $wrapperPath -Schedule $ScheduleType -Time $ScheduleTime -Day $ScheduleDay)) {
        exit 1
    }
    
    Write-Host "`n✓ Auto-update scheduler configured successfully!" -ForegroundColor Green
    Write-Host "`nConfiguration:" -ForegroundColor Yellow
    Write-Host "  Schedule: $ScheduleType" -ForegroundColor White
    Write-Host "  Time: $ScheduleTime" -ForegroundColor White
    if ($ScheduleType -eq "Weekly") {
        Write-Host "  Day: $ScheduleDay" -ForegroundColor White
    }
    Write-Host "  Region: $Region" -ForegroundColor White
    Write-Host "  Auto Start Build: $AutoStartBuild" -ForegroundColor White
    Write-Host "  Use AMI Name Pattern: $UseAMIName" -ForegroundColor White
    Write-Host "  Log Directory: $LogPath" -ForegroundColor White
    
    Write-Host "`nManagement Commands:" -ForegroundColor Yellow
    Write-Host "  View task: Get-ScheduledTask -TaskName '$taskName'" -ForegroundColor Cyan
    Write-Host "  Run now: Start-ScheduledTask -TaskName '$taskName'" -ForegroundColor Cyan
    Write-Host "  Remove: .\schedule-auto-updates.ps1 -RemoveSchedule" -ForegroundColor Cyan
    Write-Host "  View logs: Get-ChildItem '$LogPath\auto-update-*.log'" -ForegroundColor Cyan
    
} catch {
    Write-Error "Auto-update scheduler setup failed: $($_.Exception.Message)"
    exit 1
}
