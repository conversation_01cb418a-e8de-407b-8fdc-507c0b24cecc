{"metadata": {"name": "simple-test", "version": "1.0.0", "workflow_id": "WF-2025-001", "notes": "This is a simple workflow test for Terraform for development and design reference."}, "infrastructure": {"jobspecs": [{"jobspec_id": "JS-001", "vcenter_endpoint": "srv009972.mud.internal.co.za", "datacenter": "DevSiteA&B", "cluster": "CLS02-DEV-BDC&CDC", "vm_configuration": {"vm_name": "tf-ptr-test-vm", "vm_count": 1, "vm_memory": 4096, "vm_cpu": 2, "network_configuration": {"primary_interface": {"port_group": "DPortGroup"}}, "disk_configuration": [{"drive_letter": "C", "size_gb": 40, "datastore": "STM-BDC01-RUB03-NML-CMPTHN24", "type": "thick-eager-provision-zeroed", "unit_number": 0}]}}]}}