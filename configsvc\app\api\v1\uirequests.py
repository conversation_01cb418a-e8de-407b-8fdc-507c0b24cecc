from datetime import datetime, date, time
import json
from app import logging


from app.core.helpers import key_exists, dictval2list, dct2df, str2int
#import MariaDB connection
from app.core.database import dbQuery, df2sql

# Convert Dict to Dataframe
""" def dct2df(dctIn):
    dct = {k:[v] for k,v in dctIn.items()}
    df = pd.DataFrame(dct)
    # df = df.append(dctIn, ignore_index=True)
    # print(df)
    return df """

# Query DB for UI Options
def UiInit():
    try:
        # Query db for initial UI options
        qrytxt = f"select * from ui_init;"
        logging.info(f"QUERY: {qrytxt}")
        dfQry = dbQuery(qrytxt)
        dctRes = dfQry.to_dict(orient='records')
    except:
        # Return error
        message = {"message" : "Issue with UI Init data in table", "data":""}
        logging.warning(message['message'])
        return message
    return dictval2list(dctRes[0],",")

# Process Filters
def ValidateRequest(dct):
    logging.info(f"PARAMETERS => {dct}")
    if not bool(dct):
        logging.warning(f"PARAMETERS issue...")
        success = False
        data = ""
        message = f"Verify UI selections..."
        status = "WARN"
        logging.warning(message)
    else:
        respdata = UiRequest(dct)
        if respdata['data'] == "":
            success = False
            data = ""
            message = respdata['message'] #f"Invalid selection, please retry."
            status = "FAILED"
            logging.error(message)
        else:
            success =True
            message = f"Request received!"
            data = respdata['data']
            status = "NORMAL"
            logging.info(message)
    result = {
        'data' : data,
        'status': status, 
        'success' : success,
        'message' : message
    }

    return result


# Query DB for UI Options
def UiRequest(options):
    print(f"UI Request:\n{options}")
    dctRes = {}
    dctRes.update({'data':options})
    dctOptions = PrepRequest(options)
    dctBuildSpecs = {}
    dctBuildSpecs['data'] = PrepBuildSpec(dctOptions)
    return dctBuildSpecs

def PrepRequest(dct):
    print("\nSpec to DF...")
    dfSpec = dct2df(dct) #pd.DataFrame([dct])
    dfSpec['vm_count'] = dfSpec['vm_count'].astype(int)
    dfSpec['vcpus'] = dfSpec['vcpus'].astype(int)
    dfSpec['vram'] = dfSpec['vram'].astype(int)
    dfSpec['vnics'] = dfSpec['vnics'].astype(int)
    dfSpec['vm_count'] = dfSpec['vm_count'].astype(int)
    dfSpec['status'] = "NEW"
    # dfSpec['received'] = str(datetime.now()).split(".")[0]
    dfSpec['completed'] = ""
    dfSpec['comments'] = ""
    # print(dfSpec.info(),"\n")
    print(dfSpec,"\n")
    dctOut = dfSpec.to_dict('records')[0]
    print(f"Specs DF done!\n{dctOut}")
    return dctOut

# Prep Disk Requirements
def PrepDisks(disks_spec):
    dctDisk = {}
    # dctDisk['id'] = 0
    dctDisks = {}
    # dctDisks[0] = {}
    # Get Disk Totals
    for key, value in disks_spec.items():
        if "disk" in key:
            if key != "mirror_type":
                id = int(key.split("_")[0].strip("disk"))
            if not key_exists(id,dctDisks):            
                dctDisks.update({id : {}})
            # dctDisk.update({key : value})
            # print(dctDisks)
            newkey = key.split("_")[-1]
            # print(f"{id}  {newkey} : {value}") # {newkey} : {value}
            if not key_exists(newkey,dctDisks):
                if newkey != "mirror_type":
                    if newkey != "size":
                        dctDisks[id].update({newkey : value})
                    else:
                        dctDisks[id].update({newkey : int(value)})
                if not key_exists('id',dctDisks[id]):            
                    dctDisks[id].update({'id' : id})

                if not key_exists('mirror',dctDisks[id]):            
                    dctDisks[id].update({'mirror' : disks_spec['mirror_type']})
            # print(dctDisk)

    # dctDisks.pop('')
    print(dctDisks)
    return dctDisks

# Process RAW JIRA
def PrepBuildSpec(build_spec):
    print("\nPrep Build Specifications...\n") #,type(build_spec))

    mem_baseline = 32
    memswap_ratio = 1.5
    windows_os = 120
    linux_os = 50
    gold_disks = ['GOLD','SAPHIRE','DIAMOND','VDI']
    bronze_disks = ['BRONZE','QUARTZ','AMBER','EMERALD','RUBY']
    no_os_disk = ['APPLIANCE','SHELL','VDI']
    mirrored = ['ATIII']
    t2_def_type = 'DIAMOND'
    t3_def_type = 'QUARTZ'
    gold_total = 0
    bronze_total = 0
    standard_req = {}
    disks = []
    disk = {}
    #nics = []
    # Cater for OS Disk Size Adjustments
    if str(build_spec['app_type']).upper() in no_os_disk:
        if not key_exists("disk1_disk_size",build_spec):
            if "windows" in str(build_spec['os_type']).lower():
                is_windows = True
                os_disk_size = windows_os
                os_mount = 'C'
            #     os_type = "Windows"
            else:
                is_windows = False
                os_disk_size = linux_os
                os_mount = ''
            #     os_type = "Linux"
    else:
        if "windows" in str(build_spec['os_type']).lower():
            # os_type = "Windows"
            # standard_req.update({'os_type': 'Windows'})
            os_mount = 'C'
            is_windows = True
            if int(build_spec['vram']) > mem_baseline:
                os_disk_size = windows_os + round(int(build_spec['vram'])*memswap_ratio,10)
            else:
                os_disk_size = windows_os
        else:
            # standard_req.update({'os_type': 'Linux'})
            # os_type = "Linux"
            os_disk_size = linux_os
            os_mount = ''
            is_windows = False

    # Virtualization mem swap space - add RAM to OS disk
    os_storage_size = os_disk_size + int(build_spec['vram'])

    # Set Mirror Type
    if build_spec['dr_tier'].upper() in mirrored:
        mirror_type = "MSP"
    else:
        if build_spec['asset_owner'].upper() == "SANTAM":
            if build_spec['app_tier'].upper() != "ATV" and build_spec['app_type'].upper() != "MSSQL":
                mirror_type = "MSP"
            else:
                mirror_type = "NML"
        else:
            mirror_type = "NML"
    """     # Get Disk Totals
    dctDisks = {}
    storage = {}
    storage.update({'mirror_type' : mirror_type })
    if not "disk1_disk_size" in build_spec:
        build_spec['disks'] = {}
        logging.warning(f"No disks specified...")
        if build_spec['app_tier'].upper() != "ATV":
            dctDisks.update({0 : {'type':  'DIAMOND', 'size' : os_disk_size}})
            storage.update({'DIAMOND' : 0 })
        else:
            dctDisks.update({0 : {'type': 'QUARTZ'.upper(), 'size' : os_disk_size}})
            storage.update({'QUARTZ' : 0 })
    else:
        dctDisks = PrepDisks(build_spec)
        if build_spec['app_tier'].upper() != "ATV":
            dctDisks.update({0 : {'type':  build_spec['disk1_disk_type'].upper(), 'size' : os_disk_size}})            
        else:
            dctDisks.update({0 : {'type': 'QUARTZ'.upper(), 'size' : os_disk_size}})
        storage.update({build_spec['disk1_disk_type'].upper() : 0 })

        build_spec['disks'] = dctDisks
        print("DICT\n",dctDisks) """
    # Process Disk Types
    storage = {}
    storage.update({'mirror_type' : build_spec['mirror_type'].upper() })
    disks = []
    disk = {}
    os_disk_type = ""
    os_disk_type_set = False
    # disks.append(disk)
    # nics = []
    if not key_exists("disk1_disk_size",build_spec):
        if build_spec['dr_tier'].upper() == "ATV":
            os_disk_type = t3_def_type
        else:
            os_disk_type = t2_def_type

    for key, value in build_spec.items():

        if "disk" in key:
            # print(f"Disks: {disks}")
            # disk = {}
            # print(f"Found {key} : {value}")
            id = int(key.split("_")[0].strip("disk")) # get disk num as integer
            disk_key = key.split("_")[-1]
            # try:
            if not disks:
                disks.append({'id' : id})
            try:
                disks[id-1].update({disk_key : str2int(value)})
            except:
                disks.append({'id' : id})
            disks[id-1].update({disk_key : str2int(value)})
            if disk_key.lower() == 'type' and not os_disk_type_set:
                if build_spec['dr_tier'].upper() != 'ATV':
                    if value in gold_disks:
                        os_disk_type = value
                        os_disk_type_set = True
                else:
                    if value in bronze_disks:
                        os_disk_type = value
                        os_disk_type_set = True
                print(f"OS Disk Type => {os_disk_type}")
            # except:
            #     disks.update({disk_key: value})
            # try:
            # print(f"Created {id-1} => {disks}")


    # Calculate OS disk size and cater for swap space on VMware datastore side
    # os_disk_size += int(build_spec['vram'])
    os_disk_dct = {'id':0, 'type' : os_disk_type, 'mount': os_mount,'label': 'OS Disk', 'size': os_disk_size}
    disks.append(os_disk_dct)

    print(f"Disks: {disks}")
    print(f"Storage: {storage}\n")
    for disk in disks:
        print(f"Disk: {disk}")
        if not disk['label'].lower() == 'os disk':
            disk_type = str(disk['type'])
            if not key_exists(disk_type,storage):    
                # Creates a key of type DIAMOND/QUARTZ etc, value set to 0        
                storage[str(disk_type)] = 0
                print(f"Storage: {storage}")
            storage[disk_type] += disk['size']
        else:
            disk_type = str(disk['type'])
            if not key_exists(disk_type,storage):    
                # Creates a key of type DIAMOND/QUARTZ etc, value set to 0        
                storage[str(disk_type)] = 0
                print(f"Storage: {storage}")
            storage[disk_type] += disk['size']
            # Add vSwap overhead
            storage[disk_type] += int(build_spec['vram'])


    print(f"Storage: {storage}")
    """     for i, disk in enumerate(dctDisks.items()): #dctDisks:
        disk_type = str(dctDisks[i]['type'])
        if not key_exists(disk_type,storage):            
            storage[str(disk_type)] = 0
        storage[disk_type] += int(dctDisks[i]['size']) """
        # else:
        #     standard_req.update({key : value})
    # Calculate OS disk size and cater for swap space on VMware datastore side
    # if gold_total > 0:
    #     gold_total += os_disk_size + int(build_spec['vram'])
    #     tier = "GOLD"
    # elif bronze_total > 0:
    #     bronze_total += os_disk_size + int(build_spec['vram'])
    #     tier = "BRONZE"
    # elif build_spec['app_tier'].upper() in ["ATV"]:
    #     bronze_total += os_disk_size + int(build_spec['vram'])
    #     tier = "BRONZE"
    # else:
    #     gold_total += os_disk_size + int(build_spec['vram'])
    #     tier = "GOLD"
    # dctDisks.update({0 : {'label': 'os_disk', 'size': os_disk_size, 'mount' : ''}}) # 'type': dctDisks[1]['type'].upper(),
    # storage.update({'mirror_type' : mirror_type}) #, 'gold_total': gold_total, 'bronze_total': bronze_total,})
    # os_disk = {'label': 'os_disk', 'type': tier.upper(), 'size': os_disk_size, 'mount' : '','id': 0 }
    # disks.append(os_disk)
    received = str(datetime.now()).split(".")[0]
    build_spec.update({'disks':disks, 'storage' : storage, 'source':'WEB_APP'})
    # build_spec.update({'disks':dctDisks, 'storage' : storage, 'source':'WEB_APP'})
    # standard_req.update({'os_type': os_type, 'disks':dctDisks, 'mirror_type' : mirror_type, 'gold_total': gold_total, 'bronze_total': bronze_total, 'received': received, 'source':'JIRA'})

    print(f"{json.dumps(build_spec, indent=2)}\n")
    print("Specifications done!\n")
    return build_spec