# Cronicle 

### Data Import and Export
[Wiki](https://github.com/jhuckaby/Cronicle/blob/master/docs/CommandLine.md#data-import-and-export)

Cronicle can import and export data via the command-line, to/from a plain text file. This data includes all the "vital" storage records such as Users, Plugins, Categories, Servers, Server Groups, API Keys and all Scheduled Events. It excludes things like user sessions, job completions and job logs.

To export your Cronicle data, issue this command on your primary server:
``` bash
/opt/cronicle/bin/control.sh export /path/to/cronicle-data-backup.txt --verbose
```

The --verbose flag makes it emit some extra information to the console. Omit that if you want it to run silently. Omit the filename if you want it to export the data to STDOUT instead of a file.

To import data back into the system, first make sure Cronicle is stopped on all servers, and then run this command:
``` bash
/opt/cronicle/bin/control.sh import /path/to/cronicle-data-backup.txt
```

If you want daily backups of the data which auto-expire after a year, a simple shell script can do it for ya:

``` bash
#!/bin/bash
DATE_STAMP=`date "+%Y-%m-%d"`
BACKUP_DIR="/backup/cronicle/data"
BACKUP_FILE="$BACKUP_DIR/backup-$DATE_STAMP.txt"

mkdir -p $BACKUP_DIR
/opt/cronicle/bin/control.sh export $BACKUP_FILE --verbose
find $BACKUP_DIR -mtime +365 -type f -exec rm -v {} \;
```