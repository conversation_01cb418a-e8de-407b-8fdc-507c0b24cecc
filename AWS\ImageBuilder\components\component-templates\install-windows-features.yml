# AWS Image Builder Component: Install Windows Features
# This component installs common Windows Server features and roles

name: win-server-features
description: Install and configure Windows Server features and roles
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: CheckExistingFeatures
    action: ExecutePowerShell
    onFailure: Continue
    inputs:
      commands:
      - |
        Write-Host "Checking currently installed Windows features..."
        
        # Get list of installed features
        $installedFeatures = Get-WindowsFeature | Where-Object {$_.InstallState -eq "Installed"}
        Write-Host "Currently installed features:"
        $installedFeatures | Select-Object Name, DisplayName | Format-Table -AutoSize
        
        Write-Host "Total installed features: $($installedFeatures.Count)"

  - name: InstallHyperVFeatures
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing Hyper-V features..."
        
        $hypervFeatures = @(
            "Hyper-V",
            "Hyper-V-PowerShell",
            "Hyper-V-Tools"
        )
        
        foreach ($feature in $hypervFeatures) {
            Write-Host "Installing feature: $feature"
            try {
                $result = Install-WindowsFeature -Name $feature -IncludeManagementTools
                if ($result.Success) {
                    Write-Host "Successfully installed: $feature"
                } else {
                    Write-Warning "Failed to install $feature : $($result.ExitCode)"
                }
            } catch {
                Write-Warning "Error installing $feature : $($_.Exception.Message)"
            }
        }

  - name: InstallContainerFeatures
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing Container features..."
        
        $containerFeatures = @(
            "Containers",
            "Microsoft-Hyper-V-All"
        )
        
        foreach ($feature in $containerFeatures) {
            Write-Host "Installing feature: $feature"
            try {
                $result = Install-WindowsFeature -Name $feature -IncludeManagementTools
                if ($result.Success) {
                    Write-Host "Successfully installed: $feature"
                } else {
                    Write-Warning "Failed to install $feature : $($result.ExitCode)"
                }
            } catch {
                Write-Warning "Error installing $feature : $($_.Exception.Message)"
            }
        }

  - name: InstallFailoverClusteringFeatures
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing Failover Clustering features..."
        
        $clusterFeatures = @(
            "Failover-Clustering",
            "RSAT-Clustering",
            "RSAT-Clustering-PowerShell",
            "RSAT-Clustering-Mgmt"
        )
        
        foreach ($feature in $clusterFeatures) {
            Write-Host "Installing feature: $feature"
            try {
                $result = Install-WindowsFeature -Name $feature -IncludeManagementTools
                if ($result.Success) {
                    Write-Host "Successfully installed: $feature"
                } else {
                    Write-Warning "Failed to install $feature : $($result.ExitCode)"
                }
            } catch {
                Write-Warning "Error installing $feature : $($_.Exception.Message)"
            }
        }

  - name: InstallFileServicesFeatures
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing File Services features..."
        
        $fileFeatures = @(
            "FileAndStorage-Services",
            "File-Services",
            "FS-FileServer",
            "FS-DFS-Namespace",
            "FS-DFS-Replication",
            "FS-Resource-Manager",
            "FS-VSS-Agent"
        )
        
        foreach ($feature in $fileFeatures) {
            Write-Host "Installing feature: $feature"
            try {
                $result = Install-WindowsFeature -Name $feature -IncludeManagementTools
                if ($result.Success) {
                    Write-Host "Successfully installed: $feature"
                } else {
                    Write-Warning "Failed to install $feature : $($result.ExitCode)"
                }
            } catch {
                Write-Warning "Error installing $feature : $($_.Exception.Message)"
            }
        }

  - name: InstallRemoteAccessFeatures
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing Remote Access features..."
        
        $remoteFeatures = @(
            "RemoteAccess",
            "DirectAccess-VPN",
            "Routing",
            "RSAT-RemoteAccess",
            "RSAT-RemoteAccess-PowerShell"
        )
        
        foreach ($feature in $remoteFeatures) {
            Write-Host "Installing feature: $feature"
            try {
                $result = Install-WindowsFeature -Name $feature -IncludeManagementTools
                if ($result.Success) {
                    Write-Host "Successfully installed: $feature"
                } else {
                    Write-Warning "Failed to install $feature : $($result.ExitCode)"
                }
            } catch {
                Write-Warning "Error installing $feature : $($_.Exception.Message)"
            }
        }

  - name: InstallRSATTools
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing RSAT (Remote Server Administration Tools)..."
        
        $rsatFeatures = @(
            "RSAT",
            "RSAT-AD-PowerShell",
            "RSAT-ADDS-Tools",
            "RSAT-DNS-Server",
            "RSAT-DHCP",
            "RSAT-File-Services",
            "RSAT-Print-Services",
            "RSAT-Web-Server"
        )
        
        foreach ($feature in $rsatFeatures) {
            Write-Host "Installing RSAT feature: $feature"
            try {
                $result = Install-WindowsFeature -Name $feature -IncludeAllSubFeature
                if ($result.Success) {
                    Write-Host "Successfully installed: $feature"
                } else {
                    Write-Warning "Failed to install $feature : $($result.ExitCode)"
                }
            } catch {
                Write-Warning "Error installing $feature : $($_.Exception.Message)"
            }
        }

  - name: InstallNetworkingFeatures
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Installing Networking features..."
        
        $networkFeatures = @(
            "NLB",
            "SNMP-Service",
            "SNMP-WMI-Provider",
            "Telnet-Client",
            "TFTP-Client"
        )
        
        foreach ($feature in $networkFeatures) {
            Write-Host "Installing networking feature: $feature"
            try {
                $result = Install-WindowsFeature -Name $feature
                if ($result.Success) {
                    Write-Host "Successfully installed: $feature"
                } else {
                    Write-Warning "Failed to install $feature : $($result.ExitCode)"
                }
            } catch {
                Write-Warning "Error installing $feature : $($_.Exception.Message)"
            }
        }

- name: validate
  steps:
  - name: ValidateWindowsFeatures
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Validating Windows features installation..."
        
        # Check for key features that should be installed
        $expectedFeatures = @(
            "Hyper-V",
            "Containers", 
            "Failover-Clustering",
            "File-Services",
            "RSAT-AD-PowerShell"
        )
        
        $missingFeatures = @()
        $installedFeatures = @()
        
        foreach ($feature in $expectedFeatures) {
            $featureStatus = Get-WindowsFeature -Name $feature -ErrorAction SilentlyContinue
            if ($featureStatus -and $featureStatus.InstallState -eq "Installed") {
                $installedFeatures += $feature
                Write-Host "✓ $feature is installed"
            } else {
                $missingFeatures += $feature
                Write-Warning "✗ $feature is not installed"
            }
        }
        
        # Display summary
        Write-Host "`nInstallation Summary:"
        Write-Host "Successfully installed features: $($installedFeatures.Count)"
        Write-Host "Missing features: $($missingFeatures.Count)"
        
        if ($installedFeatures.Count -gt 0) {
            Write-Host "`nInstalled features:"
            $installedFeatures | ForEach-Object { Write-Host "  - $_" }
        }
        
        if ($missingFeatures.Count -gt 0) {
            Write-Host "`nMissing features:"
            $missingFeatures | ForEach-Object { Write-Host "  - $_" }
        }
        
        # Display all currently installed features
        Write-Host "`nAll installed Windows features:"
        Get-WindowsFeature | Where-Object {$_.InstallState -eq "Installed"} | 
            Select-Object Name, DisplayName | Format-Table -AutoSize
        
        Write-Host "VALIDATION SUCCESS: Windows features installation completed"
