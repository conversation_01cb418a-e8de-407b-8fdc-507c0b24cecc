const express = require('express');
const passport = require('passport');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const speakeasy = require('speakeasy');
const qrcode = require('qrcode');
const { User } = require('../models');
const { verifyMfa } = require('../../middleware/auth/passport-config');

const router = express.Router();

// Register a new user
router.post('/register', async (req, res) => {
  try {
    const { username, email, password } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({
      where: { 
        [User.sequelize.Op.or]: [
          { email },
          { username }
        ]
      }
    });
    
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }
    
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    // Create user
    const newUser = await User.create({
      username,
      email,
      password: hashedPassword
    });
    
    // Remove password from response
    const userResponse = newUser.toJSON();
    delete userResponse.password;
    
    res.status(201).json({
      message: 'User registered successfully',
      user: userResponse
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Login with email and password
router.post('/login', (req, res, next) => {
  passport.authenticate('local', (err, user, info) => {
    if (err) {
      return next(err);
    }
    
    if (!user) {
      return res.status(401).json({ message: info.message });
    }
    
    req.login(user, (err) => {
      if (err) {
        return next(err);
      }
      
      // Check if MFA is enabled
      if (user.mfaEnabled) {
        return res.status(200).json({
          message: 'MFA required',
          requireMfa: true,
          userId: user.id
        });
      }
      
      // Update last login
      user.lastLogin = new Date();
      user.save();
      
      // Generate JWT token
      const token = jwt.sign(
        { id: user.id, email: user.email },
        process.env.SESSION_SECRET || 'manga-manager-secret',
        { expiresIn: '1d' }
      );
      
      // Remove password from response
      const userResponse = user.toJSON();
      delete userResponse.password;
      delete userResponse.mfaSecret;
      
      res.status(200).json({
        message: 'Login successful',
        user: userResponse,
        token
      });
    });
  })(req, res, next);
});

// Verify MFA token
router.post('/verify-mfa', async (req, res) => {
  try {
    const { userId, token } = req.body;
    
    const user = await User.findByPk(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    const verified = speakeasy.totp.verify({
      secret: user.mfaSecret,
      encoding: 'base32',
      token
    });
    
    if (!verified) {
      return res.status(401).json({ message: 'Invalid MFA token' });
    }
    
    // Update last login
    user.lastLogin = new Date();
    await user.save();
    
    // Generate JWT token
    const jwtToken = jwt.sign(
      { id: user.id, email: user.email },
      process.env.SESSION_SECRET || 'manga-manager-secret',
      { expiresIn: '1d' }
    );
    
    // Remove sensitive data from response
    const userResponse = user.toJSON();
    delete userResponse.password;
    delete userResponse.mfaSecret;
    
    req.login(user, (err) => {
      if (err) {
        return res.status(500).json({ message: 'Session error' });
      }
      
      res.status(200).json({
        message: 'MFA verification successful',
        user: userResponse,
        token: jwtToken
      });
    });
  } catch (error) {
    console.error('MFA verification error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Setup MFA
router.post('/setup-mfa', async (req, res) => {
  try {
    const { userId } = req.body;
    
    const user = await User.findByPk(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Generate new secret
    const secret = speakeasy.generateSecret({
      name: `MangaManager:${user.email}`
    });
    
    // Save secret to user
    user.mfaSecret = secret.base32;
    await user.save();
    
    // Generate QR code
    const qrCodeUrl = await qrcode.toDataURL(secret.otpauth_url);
    
    res.status(200).json({
      message: 'MFA setup initiated',
      secret: secret.base32,
      qrCode: qrCodeUrl
    });
  } catch (error) {
    console.error('MFA setup error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Enable MFA
router.post('/enable-mfa', async (req, res) => {
  try {
    const { userId, token } = req.body;
    
    const user = await User.findByPk(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Verify token
    const verified = speakeasy.totp.verify({
      secret: user.mfaSecret,
      encoding: 'base32',
      token
    });
    
    if (!verified) {
      return res.status(401).json({ message: 'Invalid MFA token' });
    }
    
    // Enable MFA
    user.mfaEnabled = true;
    await user.save();
    
    res.status(200).json({
      message: 'MFA enabled successfully'
    });
  } catch (error) {
    console.error('MFA enable error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Disable MFA
router.post('/disable-mfa', verifyMfa, async (req, res) => {
  try {
    const { userId } = req.body;
    
    const user = await User.findByPk(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Disable MFA
    user.mfaEnabled = false;
    user.mfaSecret = null;
    await user.save();
    
    res.status(200).json({
      message: 'MFA disabled successfully'
    });
  } catch (error) {
    console.error('MFA disable error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Google OAuth login
router.get('/google',
  passport.authenticate('google', { scope: ['profile', 'email'] })
);

// Google OAuth callback
router.get('/google/callback',
  passport.authenticate('google', { failureRedirect: '/login' }),
  (req, res) => {
    // Generate JWT token
    const token = jwt.sign(
      { id: req.user.id, email: req.user.email },
      process.env.SESSION_SECRET || 'manga-manager-secret',
      { expiresIn: '1d' }
    );
    
    // Redirect to frontend with token
    res.redirect(`/?token=${token}`);
  }
);

// Logout
router.post('/logout', (req, res) => {
  req.logout((err) => {
    if (err) {
      return res.status(500).json({ message: 'Logout error' });
    }
    res.status(200).json({ message: 'Logged out successfully' });
  });
});

// Get current user
router.get('/me', (req, res) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Not authenticated' });
  }
  
  // Remove sensitive data from response
  const userResponse = req.user.toJSON();
  delete userResponse.password;
  delete userResponse.mfaSecret;
  
  res.status(200).json({
    user: userResponse
  });
});

module.exports = router;