releaseVariables['action_proceed'] = False
releaseVariables['action_cancelled'] = False

rawdata = releaseVariables['jira_response']
print rawdata["fields.status.name"]
var_progress = rawdata["fields.status.name"]

releaseVariables['action_proceed'] = (var_progress == 'In Progress')

# Define helper function to reduce repetition
def handle_dns_operation(operation_success, operation_type):
    success = operation_success  # Now directly using the boolean value
    message = "DNS record %s %s" % (operation_type, "successfully" if success else "failed")
    
    # Set specific operation result variables
    releaseVariables['dns%sSuccess' % operation_type.capitalize()] = success
    releaseVariables['dns%sMessage' % operation_type.capitalize()] = message
    
    # Set general request result variables
    if success:
        return True, "DNS record %s successfully" % operation_type
    else:
        return False, "An error occurred in the DNS request, please log a BMC request for further assistance"

# Process based on operation type
if releaseVariables['updateRecord'] == True:
    remove_success = releaseVariables['dnsRemoveSuccess']  # Direct boolean use
    add_success = releaseVariables['dnsAddSuccess']        # Direct boolean use
    
    if remove_success and add_success:
        dnsRequestSuccess, dnsRequestMessage = True, "DNS record updated successfully"
        releaseVariables['dnsUpdateSuccess'] = True
        releaseVariables['dnsUpdateMessage'] = "DNS record updated successfully"
    else:
        dnsRequestSuccess, dnsRequestMessage = False, "An error occurred in the DNS request, please log a BMC request for further assistance"
        releaseVariables['dnsUpdateSuccess'] = False
        releaseVariables['dnsUpdateMessage'] = "DNS record update failed"

elif releaseVariables['addRecord'] == True:
    dnsRequestSuccess, dnsRequestMessage = handle_dns_operation(releaseVariables['dnsAddSuccess'], "added")

elif releaseVariables['deleteRecord'] == True:
    dnsRequestSuccess, dnsRequestMessage = handle_dns_operation(releaseVariables['dnsRemoveSuccess'], "removed")

else:
    dnsRequestSuccess = False
    dnsRequestMessage = "An error occurred in the DNS request, please log a BMC request for further assistance"

print dnsRequestSuccess
print dnsRequestMessage

releaseVariables['dnsRequestSuccess'] = dnsRequestSuccess
releaseVariables['dnsRequestMessage'] = dnsRequestMessage