# Install VMware Tools
# This script installs VMware Tools for optimal VM performance

Write-Host "Installing VMware Tools..."

# Check if VMware Tools is already installed
$vmwareTools = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*VMware Tools*" }

if ($vmwareTools) {
    Write-Host "VMware Tools is already installed: $($vmwareTools.Name)"
    Write-Host "Version: $($vmwareTools.Version)"
} else {
    Write-Host "VMware Tools not found. Attempting installation..."
    
    # Try to mount VMware Tools ISO
    $cdDrives = Get-WmiObject -Class Win32_CDROMDrive
    $vmwareToolsISO = $null
    
    foreach ($drive in $cdDrives) {
        if ($drive.VolumeName -eq "VMware Tools") {
            $vmwareToolsISO = $drive.Drive
            break
        }
    }
    
    if ($vmwareToolsISO) {
        Write-Host "Found VMware Tools ISO on drive $vmwareToolsISO"
        
        # Look for setup executable
        $setupPath = Join-Path $vmwareToolsISO "setup.exe"
        $setup64Path = Join-Path $vmwareToolsISO "setup64.exe"
        
        if (Test-Path $setup64Path) {
            Write-Host "Installing VMware Tools (64-bit)..."
            Start-Process -FilePath $setup64Path -ArgumentList "/S /v/qn REBOOT=R" -Wait
        } elseif (Test-Path $setupPath) {
            Write-Host "Installing VMware Tools..."
            Start-Process -FilePath $setupPath -ArgumentList "/S /v/qn REBOOT=R" -Wait
        } else {
            Write-Host "VMware Tools setup not found on ISO."
        }
    } else {
        Write-Host "VMware Tools ISO not found. This may be normal for cloud environments."
    }
}

# Verify installation
Start-Sleep -Seconds 10
$vmwareToolsAfter = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*VMware Tools*" }

if ($vmwareToolsAfter) {
    Write-Host "VMware Tools installation completed successfully."
    Write-Host "Installed version: $($vmwareToolsAfter.Version)"
} else {
    Write-Host "VMware Tools installation may have failed or is not applicable for this environment."
}

Write-Host "VMware Tools installation script completed."
