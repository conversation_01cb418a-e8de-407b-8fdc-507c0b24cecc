$managementServer = "SRV009364.mud.internal.co.za"
$groupName = "SGT - SQL Server Management"
$computerName = "youragent.yourdomain.com"
 
Invoke-Command -ComputerName $managementServer -ScriptBlock {
  param($groupName, $computerName)
 
  Import-Module OperationsManager
  New-SCOMManagementGroupConnection -ComputerName $env:COMPUTERNAME
 
  $group = Get-SCOMGroup | Where-Object {$_.DisplayName -eq $groupName}
  if (-not $group) {
      Write-Host "Group '$groupName' not found."
      return
  }
 
  $computer = Get-SCOMClassInstance | Where-Object { $_.DisplayName -eq $computerName }
  if (-not $computer) {
      Write-Host "Computer '$computerName' not found."
      return
  }
 
  Add-SCOMGroupMember -Group $group -Member $computer
  Write-Host "Added '$computerName' to group '$groupName'"
} -ArgumentList $groupName, $computerName