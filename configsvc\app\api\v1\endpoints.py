from typing import Any
from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from datetime import datetime, date, time
import json
from app import logging
from pydantic import BaseModel, Field

from app.api.v1.uioptions import UiInit, ProcessFilters
from app.api.v1.uirequests import ValidateRequest #, ProcessRequest
from app.api.v1.uispecs import ProcessSpecs, UiSpecs

router = APIRouter()

restart_time = datetime.now()
last_restart = str(restart_time).split(".")[0]

class stdResponse(BaseModel):
    status: str | None = None
    message: str | None = None
    data: str | None = None
    success: bool

class UIRequest(BaseModel):
    client: str = Field(examples=["SGT, Santam, Retail Mass, Glacier"])
    # reference: str | None = Field(default=None, examples=["DCS-999"])
    # client: str | None = Field(default=None, examples=["BCX-Test"])
    # owner: str | None = Field(default=None, examples=["<PERSON> (G999999)"])

# UI Options POST endpoint from worker tasks
@router.get("/init/",summary="Get UI Initial Options", tags=["endpoints"]) # 
async def uiInit(request: Request):
    # params = request.query_params
    # logging.warn(f"UI Init ...")
    response = UiInit()
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.get("/uifilters/",summary="Get UI Filters", tags=["endpoints"], response_model=stdResponse) # 
async def uifiltersGet(request: Request):
    params = request.query_params
    logging.info(f"UI Options Request received: {params}")
    response = ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Options POST endpoint from worker tasks
@router.post("/uifilters/",summary="Get UI Filters", tags=["endpoints"], response_model=stdResponse) # 
async def uifiltersPost(request: Request):
    """Issue next available name from Namer Service"""
    params = await request.json() #json.loads(request.data)
    logging.info(f"UI Options Request received: {params}")
    response = ProcessFilters(params)
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Selections POST endpoint from worker tasks
@router.post("/uivalidate/",summary="Validate UI Selections", tags=["endpoints"], response_model=stdResponse) # 
async def uiValidateRequestPost(request: Request):
    """Receive completed request for validation"""
    params = await request.json() #json.loads(request.data)
    # logging.info(f"UI Selections received: {params}")
    response = ValidateRequest(params) #{"response":"User Selections Received!"}#
    # response = params
    return JSONResponse(content=jsonable_encoder(response))

# UI Selections POST endpoint from worker tasks
@router.post("/request/",summary="Retrieve Request Specifications", tags=["endpoints"], response_model=stdResponse) # 
async def uiRequestPost(request: Request):
    """Receive validate request"""
    params = await request.json() #
    specs = json.loads(params)
    logging.info(f"Specifications Request received:\n{specs['data']}\n")
    response = ProcessSpecs(specs['data']) #{"response":"User Selections Received!"}#
    # response = {"status":"RECEIVED", "message": "Specifications Received", "data": params, "success": True}
    logging.info(f"Specifications Request response: {response}")
    # response = params
    return JSONResponse(content=jsonable_encoder(response))