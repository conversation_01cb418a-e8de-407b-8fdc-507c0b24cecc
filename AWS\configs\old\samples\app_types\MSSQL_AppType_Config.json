{"APP_TYPE": "mssql", "description": "Microsoft SQL Server configuration with optimized settings", "ou_suffix": "SQL Server", "disk_configuration": {"allocation_unit_size": "65536", "file_system": "NTFS", "drives": [{"drive_letter": "C", "allocation_unit_size": "4096", "purpose": "System"}, {"drive_letter": "E", "allocation_unit_size": "65536", "purpose": "SQL Data Files"}, {"drive_letter": "F", "allocation_unit_size": "65536", "purpose": "SQL Log Files"}, {"drive_letter": "G", "allocation_unit_size": "65536", "purpose": "TempDB"}, {"drive_letter": "H", "allocation_unit_size": "65536", "purpose": "SQL Backups"}]}, "windows_features": ["NetFx4Extended-ASPNET45", "IIS-NetFxExtensibility45", "MSMQ-Container", "MSMQ-Server"], "firewall_rules": [{"name": "SQL-Server-In", "port": "1433", "protocol": "TCP", "direction": "Inbound", "action": "Allow"}, {"name": "SQL-Browser-In", "port": "1434", "protocol": "UDP", "direction": "Inbound", "action": "Allow"}, {"name": "SQL-DAC-In", "port": "1434", "protocol": "TCP", "direction": "Inbound", "action": "Allow"}, {"name": "RDP-In", "port": "3389", "protocol": "TCP", "direction": "Inbound", "action": "Allow"}], "registry_settings": [{"path": "HKLM\\SOFTWARE\\Microsoft\\Microsoft SQL Server\\MSSQL15.MSSQLSERVER\\MSSQLServer", "name": "LoginMode", "value": "2", "type": "DWORD", "description": "Mixed Mode Authentication"}, {"path": "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Terminal Server", "name": "fDenyTSConnections", "value": "0", "type": "DWORD"}], "services": [{"name": "MSSQLSERVER", "startup_type": "Automatic", "description": "SQL Server Database Engine"}, {"name": "SQLSERVERAGENT", "startup_type": "Automatic", "description": "SQL Server Agent"}, {"name": "SQLBrowser", "startup_type": "Manual", "description": "SQL Server Browser"}], "sql_settings": {"max_memory_mb": "auto", "min_memory_mb": "0", "max_degree_of_parallelism": "0", "cost_threshold_for_parallelism": "5", "tempdb_files": "auto", "backup_compression": "true", "instant_file_initialization": "true"}, "performance_settings": {"page_file": "auto", "virtual_memory": "auto", "lock_pages_in_memory": "true"}}