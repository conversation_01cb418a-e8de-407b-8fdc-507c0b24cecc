// UI Module
const UI = (() => {
  // Cache DOM elements
  const sidebar = document.getElementById('sidebar');
  const content = document.getElementById('content');
  const sidebarCollapse = document.getElementById('sidebarCollapse');
  const darkModeToggle = document.getElementById('darkModeToggle');
  const settingsDarkMode = document.getElementById('settings-dark-mode');
  const loginBtn = document.getElementById('login-btn');
  const registerBtn = document.getElementById('register-btn');
  const logoutBtn = document.getElementById('logout-btn');
  const authButtons = document.getElementById('auth-buttons');
  const userProfile = document.getElementById('user-profile');
  const username = document.getElementById('username');
  const userEmail = document.getElementById('user-email');
  const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
  const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
  const mfaSetupModal = new bootstrap.Modal(document.getElementById('mfaSetupModal'));
  
  // Initialize UI
  const init = () => {
    // Set up event listeners
    setupEventListeners();
    
    // Check for dark mode preference
    const darkMode = localStorage.getItem('darkMode') === 'true';
    if (darkMode) {
      document.body.classList.add('dark-mode');
      darkModeToggle.checked = true;
      if (settingsDarkMode) settingsDarkMode.checked = true;
    }
  };
  
  // Set up event listeners
  const setupEventListeners = () => {
    // Sidebar toggle
    sidebarCollapse.addEventListener('click', () => {
      sidebar.classList.toggle('collapsed');
      content.classList.toggle('expanded');
      localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
    });
    
    // Dark mode toggle
    darkModeToggle.addEventListener('change', () => {
      document.body.classList.toggle('dark-mode', darkModeToggle.checked);
      localStorage.setItem('darkMode', darkModeToggle.checked);
      if (settingsDarkMode) settingsDarkMode.checked = darkModeToggle.checked;
    });
    
    if (settingsDarkMode) {
      settingsDarkMode.addEventListener('change', () => {
        document.body.classList.toggle('dark-mode', settingsDarkMode.checked);
        localStorage.setItem('darkMode', settingsDarkMode.checked);
        darkModeToggle.checked = settingsDarkMode.checked;
      });
    }
    
    // Navigation
    document.querySelectorAll('[data-page]').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const page = link.getAttribute('data-page');
        navigateTo(page);
      });
    });
    
    // Auth buttons
    loginBtn.addEventListener('click', () => loginModal.show());
    registerBtn.addEventListener('click', () => registerModal.show());
    logoutBtn.addEventListener('click', () => Auth.logout());
    
    // Switch between login and register modals
    document.getElementById('switch-to-register').addEventListener('click', (e) => {
      e.preventDefault();
      loginModal.hide();
      setTimeout(() => registerModal.show(), 400);
    });
    
    document.getElementById('switch-to-login').addEventListener('click', (e) => {
      e.preventDefault();
      registerModal.hide();
      setTimeout(() => loginModal.show(), 400);
    });
    
    // Google auth
    document.getElementById('google-login-btn').addEventListener('click', () => Auth.googleLogin());
    document.getElementById('google-register-btn').addEventListener('click', () => Auth.googleLogin());
    
    // Login form
    document.getElementById('login-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const email = document.getElementById('login-email').value;
      const password = document.getElementById('login-password').value;
      
      const result = await Auth.login(email, password);
      
      if (result.success) {
        loginModal.hide();
        updateUserInterface(true);
        showAlert('Login successful!', 'success');
      } else if (result.requireMfa) {
        // MFA form is shown by the Auth module
      } else {
        showAlert(result.error, 'danger');
      }
    });
    
    // MFA verification
    document.getElementById('verify-mfa-btn').addEventListener('click', async () => {
      const token = document.getElementById('mfa-token').value;
      
      if (!token || token.length !== 6) {
        showAlert('Please enter a valid 6-digit code', 'danger');
        return;
      }
      
      const result = await Auth.verifyMfa(token);
      
      if (result.success) {
        loginModal.hide();
        updateUserInterface(true);
        showAlert('Login successful!', 'success');
      } else {
        showAlert(result.error, 'danger');
      }
    });
    
    // Register form
    document.getElementById('register-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      const username = document.getElementById('register-username').value;
      const email = document.getElementById('register-email').value;
      const password = document.getElementById('register-password').value;
      const confirmPassword = document.getElementById('register-confirm-password').value;
      
      if (password !== confirmPassword) {
        showAlert('Passwords do not match', 'danger');
        return;
      }
      
      const success = await Auth.register(username, email, password);
      
      if (success) {
        registerModal.hide();
        setTimeout(() => loginModal.show(), 400);
      }
    });
    
    // MFA setup
    document.getElementById('setup-mfa-btn').addEventListener('click', async () => {
      const result = await Auth.setupMfa();
      
      if (result.success) {
        document.getElementById('mfa-qrcode').src = result.qrCode;
        document.getElementById('mfa-secret').value = result.secret;
        mfaSetupModal.show();
      } else {
        showAlert(result.error, 'danger');
      }
    });
    
    // Copy MFA secret
    document.getElementById('copy-secret-btn').addEventListener('click', () => {
      const secretInput = document.getElementById('mfa-secret');
      secretInput.select();
      document.execCommand('copy');
      showAlert('Secret copied to clipboard', 'success');
    });
    
    // Enable MFA
    document.getElementById('enable-mfa-btn').addEventListener('click', async () => {
      const token = document.getElementById('mfa-setup-token').value;
      
      if (!token || token.length !== 6) {
        showAlert('Please enter a valid 6-digit code', 'danger');
        return;
      }
      
      const result = await Auth.enableMfa(token);
      
      if (result.success) {
        mfaSetupModal.hide();
        document.getElementById('mfa-toggle').checked = true;
        showAlert('MFA enabled successfully', 'success');
      } else {
        showAlert(result.error, 'danger');
      }
    });
    
    // MFA toggle
    const mfaToggle = document.getElementById('mfa-toggle');
    if (mfaToggle) {
      mfaToggle.addEventListener('change', async () => {
        if (mfaToggle.checked) {
          // Enable MFA
          const result = await Auth.setupMfa();
          
          if (result.success) {
            document.getElementById('mfa-qrcode').src = result.qrCode;
            document.getElementById('mfa-secret').value = result.secret;
            mfaSetupModal.show();
          } else {
            mfaToggle.checked = false;
            showAlert(result.error, 'danger');
          }
        } else {
          // Disable MFA
          // In a real app, you would prompt for MFA code first
          const result = await Auth.disableMfa();
          
          if (result.success) {
            showAlert('MFA disabled successfully', 'success');
          } else {
            mfaToggle.checked = true;
            showAlert(result.error, 'danger');
          }
        }
      });
    }
  };
  
  // Update UI based on authentication state
  const updateUserInterface = (isAuthenticated) => {
    if (isAuthenticated && Auth.currentUser) {
      // Update user profile
      username.textContent = Auth.currentUser.username;
      userEmail.textContent = Auth.currentUser.email;
      
      // Show/hide elements
      authButtons.classList.add('d-none');
      logoutBtn.classList.remove('d-none');
      
      // Update settings
      const settingsUsername = document.getElementById('settings-username');
      const settingsEmail = document.getElementById('settings-email');
      const mfaToggle = document.getElementById('mfa-toggle');
      
      if (settingsUsername) settingsUsername.value = Auth.currentUser.username;
      if (settingsEmail) settingsEmail.value = Auth.currentUser.email;
      if (mfaToggle) mfaToggle.checked = Auth.currentUser.mfaEnabled;
      
      // Show account settings
      const accountSettings = document.getElementById('account-settings');
      if (accountSettings) accountSettings.classList.remove('d-none');
      
      // Show upload form
      const uploadForm = document.getElementById('upload-form');
      if (uploadForm) uploadForm.classList.remove('d-none');
      
      // Enable dashboard categories select
      const dashboardCategories = document.getElementById('dashboard-categories');
      if (dashboardCategories) {
        dashboardCategories.disabled = false;
        dashboardCategories.innerHTML = '<option>Loading categories...</option>';
        // In a real app, you would load categories from the API
      }
      
      // Load user data
      loadUserData();
    } else {
      // Reset user profile
      username.textContent = 'Guest';
      userEmail.textContent = '';
      
      // Show/hide elements
      authButtons.classList.remove('d-none');
      logoutBtn.classList.add('d-none');
      
      // Hide account settings
      const accountSettings = document.getElementById('account-settings');
      if (accountSettings) accountSettings.classList.add('d-none');
      
      // Hide upload form
      const uploadForm = document.getElementById('upload-form');
      if (uploadForm) uploadForm.classList.add('d-none');
      
      // Disable dashboard categories select
      const dashboardCategories = document.getElementById('dashboard-categories');
      if (dashboardCategories) {
        dashboardCategories.disabled = true;
        dashboardCategories.innerHTML = '<option>Please login to select categories</option>';
      }
      
      // Reset user data
      resetUserData();
    }
  };
  
  // Load user data (library, bookmarks, etc.)
  const loadUserData = () => {
    // In a real app, you would load data from the API
    // For now, we'll just show placeholders
    
    // Dashboard
    const dashboardContent = document.getElementById('dashboard-content');
    if (dashboardContent) {
      dashboardContent.innerHTML = `
        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                    Library Size
                  </div>
                  <div class="h5 mb-0 font-weight-bold">0 Manga</div>
                </div>
                <div class="col-auto">
                  <i class="bi bi-book fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                    Bookmarks
                  </div>
                  <div class="h5 mb-0 font-weight-bold">0</div>
                </div>
                <div class="col-auto">
                  <i class="bi bi-bookmark fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                    Categories
                  </div>
                  <div class="h5 mb-0 font-weight-bold">0</div>
                </div>
                <div class="col-auto">
                  <i class="bi bi-tags fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                    Reading Progress
                  </div>
                  <div class="h5 mb-0 font-weight-bold">0%</div>
                </div>
                <div class="col-auto">
                  <i class="bi bi-bar-chart fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
              <h6 class="m-0 font-weight-bold text-primary">Recently Read</h6>
              <div class="dropdown no-arrow">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <i class="bi bi-three-dots-vertical text-gray-400"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                  <a class="dropdown-item" href="#">View All</a>
                  <div class="dropdown-divider"></div>
                  <a class="dropdown-item" href="#">Clear History</a>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="alert alert-info mb-0">
                <i class="bi bi-info-circle"></i> You haven't read any manga yet.
              </div>
            </div>
          </div>
        </div>
      `;
    }
    
    // Library
    const libraryContent = document.getElementById('library-content');
    if (libraryContent) {
      libraryContent.innerHTML = `
        <div class="col-12">
          <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> Your library is empty. Search and download manga to add to your library.
          </div>
        </div>
      `;
    }
    
    // Bookmarks
    const bookmarksContent = document.getElementById('bookmarks-content');
    if (bookmarksContent) {
      bookmarksContent.innerHTML = `
        <div class="col-12">
          <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> You don't have any bookmarks yet.
          </div>
        </div>
      `;
    }
    
    // Categories
    const categoriesContent = document.getElementById('categories-content');
    if (categoriesContent) {
      categoriesContent.innerHTML = `
        <div class="col-12">
          <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> You don't have any categories yet.
          </div>
        </div>
      `;
    }
  };
  
  // Reset user data
  const resetUserData = () => {
    // Dashboard
    const dashboardContent = document.getElementById('dashboard-content');
    if (dashboardContent) {
      dashboardContent.innerHTML = `
        <div class="col-12">
          <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> Welcome to Manga Manager! Please login to see your personalized dashboard.
          </div>
        </div>
      `;
    }
    
    // Library
    const libraryContent = document.getElementById('library-content');
    if (libraryContent) {
      libraryContent.innerHTML = `
        <div class="col-12">
          <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> Please login to view your library.
          </div>
        </div>
      `;
    }
    
    // Bookmarks
    const bookmarksContent = document.getElementById('bookmarks-content');
    if (bookmarksContent) {
      bookmarksContent.innerHTML = `
        <div class="col-12">
          <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> Please login to view your bookmarks.
          </div>
        </div>
      `;
    }
    
    // Categories
    const categoriesContent = document.getElementById('categories-content');
    if (categoriesContent) {
      categoriesContent.innerHTML = `
        <div class="col-12">
          <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> Please login to view and manage your categories.
          </div>
        </div>
      `;
    }
  };
  
  // Navigate to a page
  const navigateTo = (page) => {
    // Hide all pages
    document.querySelectorAll('.content-page').forEach(p => p.classList.remove('active'));
    
    // Show selected page
    const selectedPage = document.getElementById(`${page}-page`);
    if (selectedPage) {
      selectedPage.classList.add('active');
    }
    
    // Update active link in sidebar
    document.querySelectorAll('.sidebar li').forEach(li => li.classList.remove('active'));
    const activeLink = document.querySelector(`.sidebar li a[data-page="${page}"]`);
    if (activeLink) {
      activeLink.closest('li').classList.add('active');
    }
    
    // On mobile, close sidebar after navigation
    if (window.innerWidth < 768) {
      sidebar.classList.remove('active');
    }
  };
  
  // Show alert message
  const showAlert = (message, type = 'info') => {
    // Create alert element
    const alertEl = document.createElement('div');
    alertEl.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`;
    alertEl.style.zIndex = '9999';
    alertEl.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Add to document
    document.body.appendChild(alertEl);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
      const bsAlert = new bootstrap.Alert(alertEl);
      bsAlert.close();
    }, 5000);
  };
  
  // Public methods and properties
  return {
    init,
    updateUserInterface,
    navigateTo,
    showAlert
  };
})();