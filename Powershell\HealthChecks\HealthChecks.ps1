<#
    .SYNOPSIS
    Sends messages to Teams channels

    .DESCRIPTION
    Sends curated messages to Teams channels

    .EXAMPLE
    HealthChecks_Teams.ps1

    .NOTES
    File Name: HealthChecks_Teams.ps1
    Author: <PERSON><PERSON>
    Version 1.0 - 12/06/2023
#>
#requires -Version 5
#requires -Modules PSTeams, PSPHPIPAM

#Variables
$CurrentDate = Get-Date -Format "dd-MM-yyyy"
$TeamsID = 'https://bcx.webhook.office.com/webhookb2/df110042-736f-4313-9fad-5a05b71ec2af@32acc968-ee0c-4a4b-a2a2-f578609a3785/IncomingWebhook/bbda6d429ddd48139f10cf3cff3d4f14/f7d1b729-259a-48af-a44d-0c79cc862e41'
$TeamsMessageTitles = "DevOps Daily Health Checks"
$TeamsMessageText = "$CurrentDate"
$TeamsActivityTitle = "**DevOps Services**"
$TeamsActivitySubtitle = "Daily Health Checks - $CurrentDate"
$TeamsActivityImage = "https://pbs.twimg.com/profile_images/832257499772370944/kYwKP3xq_400x400.jpg"
$TeamsActivityText = "Testing Daily Health Checks"
#$TeamsFact1 = 
#$TeamsFact2 = 
#$TeamsFact3 = 
#$TeamsButton =  

function Get-SvcAccLogon {
    [CmdletBinding()]
        param (
            [Parameter()]
            [string]
            $ServerName,
            [Parameter()]
            [string]
            $ServiceAccount
        )
        $user = qwinsta $ServiceAccount /server:$ServerName
        if ($user) {
            Write-Host "$ServiceAccount is Logged On $ServerName"
        }
        else {
            Write-Host "Couldn't Query Server or Account Is Not Logged On!"
        }
    return
}

Function checkIPAM {
    $phpSite = Test-Connection ipam.sanlam.co.za -quiet -Count 1
    if($phpSite){
        $apiURL = "https://ipam.sanlam.co.za/ipam/api"
        $apiAppID = "automon"
        $apiKey = "2MFYKYtHs3BxGenwqVDeIvbO98g1U8PH"
        New-PhpIpamSession -UseStaticAppKeyAuth -PhpIpamApiUrl $apiURL -AppID $apiAppID -AppKey $apiKey | Out-Null
        $ipamSubnet = Get-PhpIpamSubnet -CIDR *********/22
        if ($ipamSubnet) {
            $timeDiff =  New-TimeSpan -Start $ipamSubnet.lastScan -End $timeStamp
            if ($timeDiff.Days -gt 0 | Out-Null) {
                $phpSiteCheck = "The last scan date was $($timeDiff.Days) days, $($timeDiff.Hours) hours, $($timeDiff.Minutes) minutes ago - Failed"
            }
            else {
                $phpSiteCheck = "The last scan date was $($timeDiff.Days) days, $($timeDiff.Hours) hours, $($timeDiff.Minutes) minutes ago - Passed"
            }
        }
        else {
            $phpSiteCheck = "There was an issue getting the subnet detail - Failed"
        }
    }
    $phpSiteCheck
}

<#
Function checkPAMCO {
    $allTasks = @()
    $svcAccount = "svcscvmmadmin"
    $serverName = "srv004757"
    $pamcoSchedulers = Get-ScheduledTask -TaskPath "\" | Where-Object {$_.TaskName -match "Pamco" -and $_.State -notmatch "disabled"} | Get-ScheduledTaskInfo

    foreach($pamcoJob in $pamcoSchedulers){

        $lastRunTime = Get-Date $pamcoJob.LastRunTime -Format dd/MM/yyyy   
        $today =   Get-Date -Format dd/MM/yyyy 
        if($lastRunTime -eq $today){
            $todayRan = $true
        }else{
            $todayRan = $false
        }

        $taskListDetails = [PSCustomObject]@{
            TaskName = $pamcoJob.TaskName
            LastRun = $pamcoJob.LastRunTime
            RunToday = $todayRan
        }
        $allTasks += $taskListDetails

    }
    
    $pamcoDetails = $allTasks | Where-Object {$RunToday -eq "false"}
    if($pamcoDetails){
        $pamcoResult = $pamcoDetails.TaskName -join "," + " has not run yet today - failed"
    }else{
        $pamcoResult = "All Schedulers Running as scheduled - passed"
    }

    $pamcoWebsite = Test-Connection pamco.sanlam.co.za -quiet -Count 1
    if($pamcoWebsite){
        $pamWebsite = "Pamco Deployment Webpage Accessible - passed"
    }else{
        $pamWebsite = "Pamco Deployment Webpage NOT Accessible - failed"
    }

    $svcAccountPamco = Get-SvcAccLogon -ServerName $serverName -ServiceAccount $svcAccount
    if($svcAccountPamco){
        $pamcoAccount = "Service Account is Logged On - passed"
    }else{
        $pamcoAccount = "Service Account is not Logged On - failed"
    }
    
    $finalResult = $pamcoResult + "`r`n" + $pamWebsite + "`r`n" + $pamcoAccount
    $finalResult
}
#>
Function checkAutoDeploy {
    $allTasks = @()
    $svcAccount = "svcscvmmadmin"
    $serverName = "srv009484"
    $AutoDeploySchedulers = Get-ScheduledTask -CimSession SRV009484 -TaskPath "\" | Where-Object {$_.TaskName -match "AutoDeploy" -and $_.State -notmatch "disabled"} | Get-ScheduledTaskInfo

    foreach($AutoDeployJob in $AutoDeploySchedulers){

        $lastRunTime = Get-Date $AutoDeployJob.LastRunTime -Format dd/MM/yyyy   
        $today =   Get-Date -Format dd/MM/yyyy 
        if($lastRunTime -eq $today){
            $todayRan = $true
        }else{
            $todayRan = $false
        }

        $taskListDetails = [PSCustomObject]@{
            TaskName = $AutoDeployJob.TaskName
            LastRun = $AutoDeployJob.LastRunTime
            RunToday = $todayRan
        }
        $allTasks += $taskListDetails

    }
    
    $AutoDeployDetails = $allTasks | Where-Object {$RunToday -eq "false"}
    if($AutoDeployDetails){
        $AutoDeployResult = $AutoDeployDetails.TaskName -join "," + " has not run yet today - failed"
    }else{
        $AutoDeployResult = "All Schedulers Running as scheduled - passed"
    }

    $svcAccountAutodeploy = Get-SvcAccLogon -ServerName $serverName -ServiceAccount $svcAccount
    if($svcAccountAutodeploy){
        $autodeployAccount = "Service Account is Logged On - passed"
    }else{
        $autodeployAccount = "Service Account is not Logged On - failed"
    }

    $finalResult = $AutoDeployResult + "`r`n" + $autodeployAccount
    $finalResult
}

$Highlight = @{
    Failed = 'Red'
    Passed = 'Green'
}

#$callPamco = checkPAMCO
$callIPAM = checkIPAM
$callAutoDeploy = checkAutoDeploy
$callServiceAccount = Get-SvcAccLogon -ServerName "srv009484" -ServiceAccount "svcscvmmadmin"

$contentTeams = @"
**Health checks for phpIPAM results as:**
$callIPAM

**Health checks for AutoDeploy results as:**
$callAutoDeploy

**Health checks for AutoDeploy results as:**
$callServiceAccount
"@

$contentTeams -split "`n" | ForEach-Object {
    $Output = $False
        ForEach ($Entry in $Highlight.Keys){
            $Text = $_ -split $Entry
            If ($Text.count -gt 1) { 
                Write-Host $Text[0] -NoNewline
                Write-Host $Entry -ForegroundColor $Highlight.$Entry
                $Output = $true
                Break
            }
        }

    If (-not $Output) { Write-Host $_ }
}

$payloadTeams = [PSCustomObject]@{
    contentTeams = $contentTeams
}

Send-TeamsMessage -URI $TeamsID -MessageTitle $TeamsMessageTitles -MessageText $TeamsMessageText -Color DodgerBlue {
    New-TeamsSection {
        New-TeamsActivityTitle -Title $TeamsActivityTitle 
        New-TeamsActivitySubtitle -Subtitle $TeamsActivitySubtitle
        #New-TeamsActivityImage -Image Add "D:\Temp\kYwKP3xq_400x400.jpg"
        #New-TeamsActivityText -Text $TeamsActivityText
        #New-TeamsActivityText -Text $payloadTeams
        New-TeamsFact -Name 'DevOps Helthchecks' -Value $contentTeams
        New-TeamsButton -Name 'Grafana Link to go here' -Link "https://srv009485:3000"
    }
}
Send-TeamsMessage -URI $TeamsID -MessageTitle $TeamsMessageTitles -MessageText $TeamsMessageText -Color DodgerBlue {
    New-TeamsSection {
        New-TeamsActivityTitle -Title $TeamsActivityTitle 
        New-TeamsActivitySubtitle -Subtitle $TeamsActivitySubtitle
        #New-TeamsActivityImage -Image Add "D:\Temp\kYwKP3xq_400x400.jpg"
        #New-TeamsActivityText -Text $TeamsActivityText
        New-TeamsActivityText -Text $contentTeams
        #New-TeamsFact -Name 'DevOps Daily Helthchecks' -Value $contentTeams
        New-TeamsButton -Name 'Grafana Link to go here' -Link "https://srv009485:3000"
    }
}