# AWS Variables
# Update these values to match your AWS environment

# AWS credentials and region
aws_region     = "us-east-1"  # Change to your preferred region
aws_access_key = ""  # Leave empty to use AWS CLI/IAM role
aws_secret_key = ""  # Leave empty to use AWS CLI/IAM role

# EC2 instance configuration
instance_type = "t3.large"  # Suitable for Windows builds

# Network configuration (optional - will use default VPC if not specified)
vpc_id            = ""  # Leave empty to use default VPC
subnet_id         = ""  # Leave empty to use default subnet
security_group_id = ""  # Leave empty to create temporary security group

# AMI naming
ami_name_2022        = "windows-server-2022-base-{{timestamp}}"
ami_description_2022 = "Windows Server 2022 Base Image - Built with Packer"

ami_name_2019        = "windows-server-2019-base-{{timestamp}}"
ami_description_2019 = "Windows Server 2019 Base Image - Built with Packer"

# WinRM configuration
winrm_username = "Administrator"
