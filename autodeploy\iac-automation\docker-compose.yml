x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "${LOG_MAX_SIZE:-20m}"
    max-file: "${LOG_MAX_FILE:-10}"
    compress: "true"

# Core Services:
  # Core Terraform services and toolkit.
services:
  terraform:
    image: bcxslm/iac-automation:iac-cli-dev
    build:
      context: .
      dockerfile: iac-tf-pwsh.Dockerfile
      args:
        POWERSHELL_VERSION: ${POWERSHELL_VERSION}
        TERRAFORM_VERSION: ${TERRAFORM_VERSION}
        PACKER_VERSION: ${PACKER_VERSION}
        TF_VAR_infrastructure_spec_file: ${TF_VAR_infrastructure_spec_file}
        TF_VAR_guest_id: ${TF_VAR_guest_id}
        TF_VAR_vsphere_server: ${TF_VAR_vsphere_server}
        TF_VAR_ipam_api_endpoint: ${TF_VAR_ipam_api_endpoint}
        TF_VAR_ad_domain_controller: ${TF_VAR_ad_domain_controller}
    container_name: iac-tf-pwsh
    command: ["/bin/bash", "-c", "while true; do sleep 3600; done"]
    working_dir: /
    volumes:
      - ./output:/output
    networks:
      - app-network

# Automation wrapper API for Terraform, pwsh, Packer, aws-cli and azure-cli.
  # Runs inside container with uvicorn and FastAPI as an ASGI server.
  api:
    image: bcxslm/iac-automation:iac-api-dev
    build:
      context: .
      dockerfile: iac-api.Dockerfile
    container_name: iac-api
    working_dir: /api
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - .:/api
    ports:
      - "3333:3333"
    networks:
      - app-network

# Packer builder service for Windows Server images.
  packer-builder:
    build:
      context: .
      dockerfile: iac-tf-pwsh.Dockerfile
      args:
        - POWERSHELL_VERSION=${POWERSHELL_VERSION:-7.5.2}
        - TERRAFORM_VERSION=${TERRAFORM_VERSION:-1.12.2}
        - PACKER_VERSION=${PACKER_VERSION:-1.14.1}
    container_name: packer-windows-builder
    hostname: packer-builder
    environment:
    # AWS Configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION:-us-east-1}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}
      - AWS_PROFILE=${AWS_PROFILE:-default}
    # Azure Configuration (optional)
      - AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
      - AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}
      - AZURE_TENANT_ID=${AZURE_TENANT_ID}
      - AZURE_SUBSCRIPTION_ID=${AZURE_SUBSCRIPTION_ID}
    # VMware vSphere Configuration
      - VCENTER_SERVER=${VCENTER_SERVER}
      - VCENTER_USERNAME=${VCENTER_USERNAME}
      - VCENTER_PASSWORD=${VCENTER_PASSWORD}
      - VCENTER_DATACENTER=${VCENTER_DATACENTER}
      - VCENTER_CLUSTER=${VCENTER_CLUSTER}
      - VCENTER_DATASTORE=${VCENTER_DATASTORE}
      - VCENTER_NETWORK=${VCENTER_NETWORK}
    # Packer Configuration
      - PACKER_LOG=${PACKER_LOG:-0}
      - PACKER_LOG_PATH=/workspace/logs/packer.log
      - PACKER_CACHE_DIR=/workspace/packer_cache
    # Build Configuration
      - BUILD_PLATFORM=${BUILD_PLATFORM:-both}
      - BUILD_VERSION=${BUILD_VERSION:-both}
      - BUILD_DEBUG=${BUILD_DEBUG:-false}
      - BUILD_PARALLEL=${BUILD_PARALLEL:-false}
    # System Configuration
      - TZ=${TZ:-UTC}
      - DEBIAN_FRONTEND=noninteractive
    volumes:
    # Mount the packer directory for development
      - ./packer:/workspace
    # Mount logs directory
      - ./packer/logs:/workspace/logs
    # Mount packer cache for faster builds
      - packer-cache:/workspace/packer_cache
    # Mount AWS credentials (alternative to environment variables)
      - ${HOME}/.aws:/home/<USER>/.aws:ro
    # Mount SSH keys for VMware access
      - ${HOME}/.ssh:/home/<USER>/.ssh:ro
    # Mount any ISO files
      - ${ISO_PATH:-./packer/ISO}:/workspace/ISO:ro
    # Mount Docker socket for potential nested container builds
      - /var/run/docker.sock:/var/run/docker.sock:ro
    working_dir: /workspace
  # Keep container running for interactive use
    tty: true
    stdin_open: true
    # TODO: Clarify further on the comment below.
  # Network configuration - use host network for VMware access
    network_mode: ${NETWORK_MODE:-host}
  # Resource limits
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMIT:-4G}
          cpus: ${CPU_LIMIT:-2.0}
        reservations:
          memory: ${MEMORY_RESERVATION:-2G}
          cpus: ${CPU_RESERVATION:-1.0}
  # Health check
    healthcheck:
      test: ["CMD", "packer", "version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
  # Restart policy
    restart: unless-stopped
    profiles:
      - packer

# AWS specific packer-builder service
  packer-aws:
    extends: packer-builder
    container_name: packer-aws-builder
    environment:
      - BUILD_PLATFORM=aws
    command: ["bash", "-c", "echo 'AWS Packer Builder Ready. Run: docker compose exec packer-aws packer/scripts/build/build-aws.sh --help'"]
    profiles:
      - packer
      - aws-only

# VMware specific packer-builder service
  packer-vmware:
    extends: packer-builder
    container_name: packer-vmware-builder
    environment:
      - BUILD_PLATFORM=vmware
    command: ["bash", "-c", "echo 'VMware Packer Builder Ready. Run: docker compose exec packer-vmware packer/scripts/build/build-vmware.sh --help'"]
    profiles:
      - packer
      - vmware-only

# TODO: Integrate and test Windmill with the API and stack.

networks:
  app-network:
    driver: bridge

volumes:
  packer-cache:
    driver: local
