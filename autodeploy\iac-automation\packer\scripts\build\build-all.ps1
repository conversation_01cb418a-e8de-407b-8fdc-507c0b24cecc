# Build All Images Script
# This script builds Windows Server images for both VMware and AWS

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("vmware", "aws", "both")]
    [string]$Platform = "both",

    [Parameter(Mandatory=$false)]
    [ValidateSet("2019", "2022", "both")]
    [string]$Version = "both",

    [Parameter(Mandatory=$false)]
    [switch]$DebugMode,

    [Parameter(Mandatory=$false)]
    [switch]$Parallel
)

Write-Host "Automated Windows Server Image Builder" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green
Write-Host "Platform: $Platform" -ForegroundColor Cyan
Write-Host "Version: $Version" -ForegroundColor Cyan
Write-Host "Parallel: $Parallel" -ForegroundColor Cyan
Write-Host ""

# Check prerequisites
Write-Host "Checking prerequisites..." -ForegroundColor Yellow

# Check if Packer is installed
try {
    $packerVersion = packer version
    Write-Host "✓ Packer version: $packerVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ ERROR: Packer is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check if we're in the right directory
if (-not (Test-Path "windows-server-2019") -or -not (Test-Path "windows-server-2022")) {
    Write-Host "✗ ERROR: Please run this script from the packer directory" -ForegroundColor Red
    exit 1
}

Write-Host "✓ Directory structure verified" -ForegroundColor Green

# Platform-specific checks
$buildVMware = $false
$buildAWS = $false

if ($Platform -eq "vmware" -or $Platform -eq "both") {
    $buildVMware = $true
    if (Test-Path "variables/vmware.pkrvars.hcl") {
        Write-Host "✓ VMware variables file found" -ForegroundColor Green
    } else {
        Write-Host "✗ WARNING: VMware variables file not found (variables/vmware.pkrvars.hcl)" -ForegroundColor Yellow
        Write-Host "  VMware builds will be skipped" -ForegroundColor Yellow
        $buildVMware = $false
    }
}

if ($Platform -eq "aws" -or $Platform -eq "both") {
    $buildAWS = $true
    if (Test-Path "variables/aws.pkrvars.hcl") {
        Write-Host "✓ AWS variables file found" -ForegroundColor Green
    } else {
        Write-Host "✗ WARNING: AWS variables file not found (variables/aws.pkrvars.hcl)" -ForegroundColor Yellow
        Write-Host "  AWS builds will be skipped" -ForegroundColor Yellow
        $buildAWS = $false
    }
    
    # Check AWS credentials if building AWS
    if ($buildAWS) {
        try {
            aws sts get-caller-identity | Out-Null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ AWS credentials verified" -ForegroundColor Green
            } else {
                throw "AWS CLI command failed"
            }
        } catch {
            Write-Host "✗ WARNING: AWS credentials not configured" -ForegroundColor Yellow
            Write-Host "  AWS builds will be skipped" -ForegroundColor Yellow
            $buildAWS = $false
        }
    }
}

if (-not $buildVMware -and -not $buildAWS) {
    Write-Host "✗ ERROR: No valid platforms to build" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Set debug logging if requested
if ($DebugMode) {
    $env:PACKER_LOG = "1"
    Write-Host "Debug logging enabled" -ForegroundColor Yellow
}

# Build function
function Start-Build {
    param(
        [string]$Platform,
        [string]$Version,
        [string]$ScriptPath
    )
    
    Write-Host "Starting $Platform Windows Server $Version build..." -ForegroundColor Cyan
    
    $startTime = Get-Date
    
    try {
        if ($Platform -eq "VMware") {
            & .\build-vmware.ps1 -Version $Version -DebugMode:$DebugMode
        } else {
            & .\build-aws.ps1 -Version $Version -DebugMode:$DebugMode
        }
        
        $endTime = Get-Date
        $duration = $endTime - $startTime
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "$Platform Windows Server $Version build completed successfully!" -ForegroundColor Green
            Write-Host "Duration: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor Cyan
            return $true
        } else {
            Write-Host "$Platform Windows Server $Version build failed!" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "ERROR: $Platform Windows Server $Version build failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Collect all builds to perform
$builds = @()

if ($buildVMware) {
    if ($Version -eq "2019" -or $Version -eq "both") {
        $builds += @{ Platform = "VMware"; Version = "2019" }
    }
    if ($Version -eq "2022" -or $Version -eq "both") {
        $builds += @{ Platform = "VMware"; Version = "2022" }
    }
}

if ($buildAWS) {
    if ($Version -eq "2019" -or $Version -eq "both") {
        $builds += @{ Platform = "AWS"; Version = "2019" }
    }
    if ($Version -eq "2022" -or $Version -eq "both") {
        $builds += @{ Platform = "AWS"; Version = "2022" }
    }
}

Write-Host "Planned builds:" -ForegroundColor Yellow
foreach ($build in $builds) {
    Write-Host "  - $($build.Platform) Windows Server $($build.Version)" -ForegroundColor White
}
Write-Host ""

# Execute builds
$results = @()
$overallStartTime = Get-Date

if ($Parallel -and $builds.Count -gt 1) {
    Write-Host "Running builds in parallel..." -ForegroundColor Yellow
    
    $jobs = @()
    foreach ($build in $builds) {
        $job = Start-Job -ScriptBlock {
            param($Platform, $Version, $DebugMode, $WorkingDir)
            Set-Location $WorkingDir

            if ($Platform -eq "VMware") {
                & .\build-vmware.ps1 -Version $Version -DebugMode:$DebugMode
            } else {
                & .\build-aws.ps1 -Version $Version -DebugMode:$DebugMode
            }

            return @{ Platform = $Platform; Version = $Version; Success = ($LASTEXITCODE -eq 0) }
        } -ArgumentList $build.Platform, $build.Version, $DebugMode, (Get-Location).Path
        
        $jobs += @{ Job = $job; Platform = $build.Platform; Version = $build.Version }
    }
    
    # Wait for all jobs to complete
    foreach ($jobInfo in $jobs) {
        $result = Receive-Job -Job $jobInfo.Job -Wait
        Remove-Job -Job $jobInfo.Job
        $results += $result
    }
} else {
    Write-Host "Running builds sequentially..." -ForegroundColor Yellow
    
    foreach ($build in $builds) {
        $success = Start-Build -Platform $build.Platform -Version $build.Version
        $results += @{ Platform = $build.Platform; Version = $build.Version; Success = $success }
        Write-Host ""
    }
}

# Summary
$overallEndTime = Get-Date
$overallDuration = $overallEndTime - $overallStartTime

Write-Host "=" * 50 -ForegroundColor Green
Write-Host "BUILD SUMMARY" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green

$successCount = 0
$totalCount = $results.Count

foreach ($result in $results) {
    $status = if ($result.Success) { "SUCCESS" } else { "FAILED" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    
    Write-Host "$($result.Platform) Windows Server $($result.Version): $status" -ForegroundColor $color
    
    if ($result.Success) {
        $successCount++
    }
}

Write-Host ""
Write-Host "Total: $successCount/$totalCount builds successful" -ForegroundColor Cyan
Write-Host "Overall duration: $($overallDuration.ToString('hh\:mm\:ss'))" -ForegroundColor Cyan

if ($successCount -eq $totalCount) {
    Write-Host ""
    Write-Host "🎉 All builds completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    if ($buildVMware) {
        Write-Host "  VMware: Check your vCenter for the new templates" -ForegroundColor White
    }
    if ($buildAWS) {
        Write-Host "  AWS: Check your AWS console for the new AMIs" -ForegroundColor White
    }
    Write-Host "  Update your Terraform configurations to use the new images" -ForegroundColor White
    exit 0
} else {
    Write-Host ""
    Write-Host "❌ Some builds failed. Check the output above for details." -ForegroundColor Red
    exit 1
}
