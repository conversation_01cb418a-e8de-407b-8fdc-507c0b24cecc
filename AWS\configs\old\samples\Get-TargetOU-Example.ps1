# Example PowerShell function showing how to use the simplified configuration

function Get-TargetOU {
    param(
        [string]$ConfigPath,
        [string]$BU,
        [string]$Environment,
        [string]$OSVersion,
        [string]$AppType
    )
    
    # Load configuration
    $config = Get-Content $ConfigPath | ConvertFrom-Json
    
    # Get environment configuration
    $envConfig = $config.$BU.environments.$Environment
    
    # Validate OS Version is supported
    if ($OSVersion -notin $envConfig.OS_VERSION) {
        throw "OS Version '$OSVersion' is not supported. Supported versions: $($envConfig.OS_VERSION -join ', ')"
    }
    
    # Build OU path using template
    $ouTemplate = $envConfig.ou_structure.template
    $baseOU = $ouTemplate -replace '\{VERSION\}', $OSVersion
    
    # If app type is provided, add app-specific OU
    if ($AppType -and $AppType -ne 'shared') {
        # Load app type configuration to get OU suffix
        $appTypeConfigPath = $config.app_type_configs.$AppType
        if ($appTypeConfigPath) {
            $appTypeConfig = Get-Content $appTypeConfigPath | ConvertFrom-Json
            $appTypeSuffix = $appTypeConfig.ou_suffix
            $targetOU = "OU=$appTypeSuffix,$baseOU"
        } else {
            $targetOU = $baseOU
        }
    } else {
        # For shared servers, use "Shared" as suffix
        $targetOU = "OU=Shared,$baseOU"
    }
    
    return $targetOU
}

# Usage examples:
# Get-TargetOU -ConfigPath "Sanlam_Config.json" -BU "SPF" -Environment "PRD" -OSVersion "2022" -AppType "mssql"
# Result: "OU=SQL Server,OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=sanlam,DC=co,DC=za"

# Get-TargetOU -ConfigPath "Sanlam_Config.json" -BU "SPF" -Environment "PRD" -OSVersion "2025" -AppType "shared"
# Result: "OU=Shared,OU=Server 2025,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=sanlam,DC=co,DC=za"

# Alternative approach - even simpler with just OS versions as numbers
function Get-TargetOU-Simple {
    param(
        [string]$BasePath,
        [string]$OSVersion,
        [string]$AppTypeSuffix = "Shared"
    )
    
    $serverOU = "OU=Server $OSVersion,OU=Windows Server,$BasePath"
    $targetOU = "OU=$AppTypeSuffix,$serverOU"
    
    return $targetOU
}

# Usage:
# Get-TargetOU-Simple -BasePath "OU=Servers,OU=SanlamLife,OU=Businesses,DC=sanlam,DC=co,DC=za" -OSVersion "2022" -AppTypeSuffix "SQL Server"
