import json
ebx_resp = json.loads(releaseVariables['ebx_response'])

ebx_json = {}
for key, value in ebx_resp.items():
    key1 = str(key).split("  ")[0].lower().replace(" ","_")
    ebx_json.update({key1 : value})
print(ebx_json)
releaseVariables['ebx_json'] = ebx_json

releaseVariables['os_version'] = releaseVariables['ebx_json']['operating_system']
releaseVariables['competency'] = releaseVariables['ebx_json']['competency']
releaseVariables['location'] = releaseVariables['ebx_json']['location']
releaseVariables['hosting_platform'] = releaseVariables['ebx_json']['vendor']
releaseVariables['ip'] = releaseVariables['ebx_json']['ipv4address']
releaseVariables['server_description'] = releaseVariables['ebx_json']['server_description']

if not releaseVariables['competency'] or not releaseVariables['ebx_json']['primary_techowner'] or not releaseVariables['ebx_json']['secondary_techowner'] or not releaseVariables['ebx_json']['app_owner']:
    releaseVariables["is_missing_annotations"] = True
    releaseVariables['decomm_proceed'] = False