# Configuration Structure Examples

## Approach 1: Separate configs per client per app_type

### File Structure:
```
configs/
├── Sanlam_SPF_Shared_Config.json
├── Sanlam_SPF_MSSQL_Config.json
├── Sanlam_SC_Shared_Config.json
├── Sanlam_SC_MSSQL_Config.json
└── Client2_BU_AppType_Config.json
```

### Usage:
```powershell
# Load configuration based on client, BU, and app_type
$configFile = "${CLIENT}_${BU}_${APP_TYPE}_Config.json"
$config = Get-Content $configFile | ConvertFrom-Json

# Get target OU
$targetOU = $config.environments.$ENV.target_ou.$OS_VERSION

# Apply app-specific settings
$diskConfig = $config.app_settings.disk_configuration
$firewallRules = $config.app_settings.firewall_rules
```

### Pros:
- Simple file naming convention
- All settings in one place per app type
- Easy to understand and maintain
- Direct access to all configuration

### Cons:
- Potential duplication of client/environment settings
- More files to manage as app types grow
- Changes to client settings require updates to multiple files

---

## Approach 2: Parent client config + separate app_type configs

### File Structure:
```
configs/
├── Sanlam_Config.json
├── Client2_Config.json
└── app_types/
    ├── Shared_AppType_Config.json
    ├── MSSQL_AppType_Config.json
    ├── IIS_AppType_Config.json
    └── Exchange_AppType_Config.json
```

### Usage:
```powershell
# Load client configuration
$clientConfig = Get-Content "${CLIENT}_Config.json" | ConvertFrom-Json

# Load app type configuration
$appTypeConfigPath = $clientConfig.app_type_configs.$APP_TYPE
$appTypeConfig = Get-Content $appTypeConfigPath | ConvertFrom-Json

# Build target OU
$baseOU = $clientConfig.$BU.environments.$ENV.ou_structure.os_versions.$OS_VERSION
$appTypeSuffix = $appTypeConfig.ou_suffix
$targetOU = "OU=$appTypeSuffix,$baseOU"

# Apply app-specific settings
$diskConfig = $appTypeConfig.disk_configuration
$firewallRules = $appTypeConfig.firewall_rules
```

### Pros:
- No duplication of client/environment settings
- App type configs are reusable across clients
- Easier to maintain app type standards
- Cleaner separation of concerns
- Fewer files to manage per client

### Cons:
- Slightly more complex to parse
- Requires loading two configuration files
- OU path construction logic needed

---

## Recommended Approach: Approach 2 (Parent + App Type)

### Reasons:
1. **Scalability**: As you add more app types (Exchange, SharePoint, etc.), you don't multiply client configs
2. **Maintainability**: App type standards are centralized and reusable
3. **Consistency**: Ensures all clients use the same app type configurations
4. **Flexibility**: Easy to override app type settings per client if needed
5. **Reduced Duplication**: Client-specific settings are only defined once

### Implementation Example:
```powershell
function Get-ServerConfiguration {
    param(
        [string]$Client,
        [string]$BU,
        [string]$Environment,
        [string]$AppType,
        [string]$OSVersion
    )
    
    # Load client configuration
    $clientConfigPath = "configs/${Client}_Config.json"
    $clientConfig = Get-Content $clientConfigPath | ConvertFrom-Json
    
    # Load app type configuration
    $appTypeConfigPath = $clientConfig.app_type_configs.$AppType
    $appTypeConfig = Get-Content $appTypeConfigPath | ConvertFrom-Json
    
    # Get environment settings
    $envConfig = $clientConfig.$BU.environments.$Environment
    
    # Build target OU
    $baseOU = $envConfig.ou_structure.os_versions.$OSVersion
    $appTypeSuffix = $appTypeConfig.ou_suffix
    $targetOU = "OU=$appTypeSuffix,$baseOU"
    
    # Combine configurations
    return @{
        ClientConfig = $clientConfig
        AppTypeConfig = $appTypeConfig
        EnvironmentConfig = $envConfig
        TargetOU = $targetOU
    }
}

# Usage
$config = Get-ServerConfiguration -Client "Sanlam" -BU "SPF" -Environment "PRD" -AppType "mssql" -OSVersion "2022"
```
