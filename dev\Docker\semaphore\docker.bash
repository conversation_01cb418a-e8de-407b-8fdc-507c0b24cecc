docker run --name semaphore \
-p 3000:3000 \
-e SEMAPHORE_DB_DIALECT=mysql \
-e SEMAPHORE_DB_HOST=srv009484.mud.internal.co.za \
-e SEMAPHORE_DB_NAME=semaphoredb \
-e SEMAPHORE_DB_USER=semaphoresvc \
-e SEMAPHORE_DB_PASS=%2Gk76!egXV7BF \
-e SEMAPHORE_ADMIN=admin \
-e SEMAPHORE_ADMIN_PASSWORD=changeme \
-e SEMAPHORE_ADMIN_NAME="Admin" \
-e SEMAPHORE_ADMIN_EMAIL=admin@localhost \
-v /data/docker_configs/semaphore/data:/var/lib/semaphore \
-v /data/docker_configs/semaphore/config:/etc/semaphore \
-v /data/docker_configs/semaphore/tmp:/tmp/semaphore \
--network semaphore_network \
-d semaphoreui/semaphore:v2.10.43