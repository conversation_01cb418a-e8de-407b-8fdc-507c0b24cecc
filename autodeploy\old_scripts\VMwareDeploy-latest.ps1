### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition#"D:\AutoDeploy\Scripts\PROD\Powershell" 
$config = Get-Content -Path "$currentPath\scriptConfigs.json" | ConvertFrom-Json
$AutoDeployModule = $config.AutoDeployModule

### Import module for connecting to APIs ###
Import-Module $AutoDeployModule

### Paths to credentials ##
$credPath = $config.CredPath
$cliCredsPath = "$credPath\cliCreds.xml"
$adProdCredsPath = "$credPath\prdDomainCreds_ADS.xml"
$adDevCredsPath = "$credPath\devDomainCreds.xml"
$adPpeCredsPath = "$credPath\ppeDomainCreds.xml"
$tempPassOut = $config.TempCredsPath

## unattendedfiles ##
$answerFilePath = $config.answerFilePath
$unattendedPRD = "$answerFilePath\AutoDeployPRD.xml"
$unattendedDEV = "$answerFilePath\AutoDeployDEV.xml"
$unattendedPPE = "$answerFilePath\AutoDeployPPE.xml"

### Hard coded identifiers ###
$jobStage = "VM_DEPLOY"
$hostingPlatform = "VMware"
$completeJobStatus = "COMPLETED"
$receivedJobStatus = "IN_PROGRESS"
$requestJobStatus = "PENDING"
$bcxComp = $config.BCXLinuxCompetency
$sgtComp = $config.SGTLinuxCompetency
$global:buildSuccess = $false
$global:finalResponse = ""
$global:osOnlyBuild = $false
$global:customSpecName = ""

Function VMDeploy_Management{
    $apiInputData = Get-Job -JobID "" -JobStatus $requestJobStatus -JobStage $jobStage -HostingPlatform $hostingPlatform

    if($apiInputData){
        Update-Job -JobID $apiInputData.request_jobid -JobStage $jobStage -JobStatus $receivedJobStatus -SuccessStatus $true -LogMessage "VM Deployment Started" -LogType "INFO" -IssueRef " " -IssueType " "
    }

    $cliCreds = Import-Clixml -Path $cliCredsPath
    $vCenterSuccess = vCenter_Connection -vCenterName $apiInputData.vc_node -credentials $cliCreds

    if($vCenterSuccess){

        $customSpecName = $apiInputData.vm_name + "_customization"
        ### remove os customization if already exists ###
        try {
            Remove-OSCustomizationSpec -OSCustomizationSpec $customSpecName -Confirm:$false -ErrorAction SilentlyContinue | Out-Null
        }catch{
            # DO NOTHING - Try catch to get rid of the expected error message when customization spec doesn't exist
        }

        $vmBuildSuccess = VMconfigSpec -buildSpecs $apiInputData

        if($vmBuildSuccess.Succeeded){
            $buildSuccess = $true
            $finalResponse = $vmBuildSuccess.Message
        }else{
            $buildSuccess = $false
            $finalResponse = $vmBuildSuccess.Message
        }

        ### remove os customization if already exists ###
        try {
            Remove-OSCustomizationSpec -OSCustomizationSpec $customSpecName -Confirm:$false -ErrorAction SilentlyContinue | Out-Null
        }catch{
            # DO NOTHING - Try catch to get rid of the expected error message when customization spec doesn't exist
        }

        Disconnect-VIServer * -Confirm:$false
    }else{
        $finalResponse = "Failed to connect to vCenter $($apiInputData.vc_node) using account $($cliCreds.Username)"
    }

    if($buildSuccess){
        Update-Job -JobID $apiInputData.request_jobid -JobStage $jobStage -JobStatus $completeJobStatus -SuccessStatus $buildSuccess -LogMessage $finalResponse -LogType "INFO" -IssueRef " " -IssueType " "
    }else{
        Update-Job -JobID $apiInputData.request_jobid -JobStage $jobStage -JobStatus $completeJobStatus -SuccessStatus $buildSuccess -LogMessage $finalResponse -LogType "ISSUE" -IssueRef " " -IssueType "VMDeploy"
    }

}

Function vCenter_Connection{
    param(
        [Parameter(Mandatory=$true)]$vCenterName,
        [Parameter(Mandatory=$true)][System.Management.Automation.PSCredential]$credentials
    )

    Try{
        Connect-VIServer -Server $vCenterName -Credential $credentials
        $vCenterConnected = $true
    }catch{
        $vCenterConnected = $false
    }

    $vCenterConnected
}

Function VMconfigSpec{
    param(
        [Parameter(Mandatory=$true)]$buildSpecs
    )

    ### put all information from jobspecs into variables###
        #Identity
        $jobID = $buildSpecs.request_jobid
        $serverName = $buildSpecs.vm_name
        $template = $buildSpecs.vm_template
        $domain = $buildSpecs.ad_suffix
        $environment = $buildSpecs.environment
        $description = $buildSpecs.vm_description
        $osType = $buildSpecs.os_version
        $appType = $buildSpecs.app_type
        $competency = $buildSpecs.competency
        
        #IP/Network
        $ipAddress = $buildSpecs.ip_address
        $subnetMask = $buildSpecs.ip_mask
        $gateway = $buildSpecs.ip_gateway
        $dnsIP = $buildSpecs.dns
        $dnsSuffix = $buildSpecs.dns_suffix
        $ipcidr = $buildSpecs.ip_cidr

        #location
        $targetEPG = $buildSpecs.epg_string
        $targetvmfolder = $buildSpecs.vmFolder

        if($buildSpecs.app_type -match "VDI"){
            $osOnlyBuild = $true
        }

        ### checks if build is in remediate state, sends to remediation cluster ###
        if($buildSpecs.has_compute){
            $targetCluster = $buildSpecs.target_cluster
            $targetHost = $buildSpecs.preferred_host

            if(!($buildSpecs.has_storage)){
                $osOnlyBuild = $true
            }
        }else{
            $targetCluster = $buildSpecs.remediation_cluster
            $targetHost = $buildSpecs.remediation_host
            $osOnlyBuild = $true
        }

        #compute resources
        $vCpu = $buildSpecs.vcpus
        $vMem = $buildSpecs.vram

        ### check if app is SQL then specifies vm sockets for numa aware app ###        
        if($buildSpecs.app_type -match "SQL"){
            $count = 2
        }else{
            $count = 1
        }
            $mod = $vCpu % $count
            while($mod -ne 0){
                $count++
                $mod = $vCpu % $count
            }
        $vmSockets = $count

        ### pull storage luns and calculate which LUN is OS LUN ###
        #storage resources
        $osSize = $buildSpecs.os_disk
        $tier2disks = $buildSpecs.t2storage
        $tier2datastore = $buildSpecs.t2_datastore
        $tier3disks = $buildSpecs.t3storage
        $tier3datastore = $buildSpecs.t3_datastore
        if($tier2datastore){
            $osDatastore = $tier2datastore
        }else{
            $osDatastore = $tier3datastore
        }

        ### create data object to pass to build VM function ###
        $deploymentData = [PSCustomObject]@{
            JobID = $jobID
            ServerName = $serverName
            Description = $description
            AppType = $appType
            OsType = $osType
            Template = $template
            domain = $domain
            environment = $environment
            competency = $competency

            ipAddress = $ipAddress
            subnetMask = $subnetMask
            gateway = $gateway
            dnsIP = $dnsIP
            dnsSuffix = $dnsSuffix
            cidr = $ipcidr

            vmCluster = $targetCluster
            vmHost = $targetHost
            EPG = $targetEPG
            Folder = $targetvmfolder

            vCPU = $vCpu
            vMem = $vMem
            sockets = $vmSockets

            osOnly = $osOnlyBuild
            osSize = $osSize
            osDatastore = $osDatastore
            tier2Datastore = $tier2datastore
            tier2Disks = $tier2disks
            tier3Datastore = $tier3datastore
            tier3Disks = $tier3disks
        }

$vmBuild = DeployVM -DeploymentSpec $DeploymentData

$task = "" | Select-Object Succeeded, Message
$task.Succeeded = $vmBuild.Succeeded
$task.Message = $vmBuild.Message
$task
}

Function DeployVM{
    param (
        [Parameter(Mandatory=$true)][Object]$DeploymentSpec
    )

    ### Create a random generated password of WIN OS local admin account ### 
    $tempAdmin = ("!@#$%^&*0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_abcdefghijklmnopqrstuvwxyz".tochararray() | Sort-Object {Get-Random})[0..8] -join ''
    ### save temp passwords as backup safety measure ###
    $tempAdminOut = [PSCustomObject]@{
        createdate = Get-Date
        JobID = $DeploymentSpec.JobID
        server = $DeploymentSpec.ServerName
        Password = $tempAdmin
    }
    $tempAdminOut | Export-CSV -path $tempPassOut -append

    try {

        if($DeploymentSpec.domain -match "dev"){
            $domainUnattend = Get-Content $unattendedDEV | Out-String
            $domainCreds = Import-Clixml $adDevCredsPath
        }elseif($DeploymentSpec.domain -match "ppe"){
            $domainUnattend = Get-Content $unattendedPPE | Out-String
            $domainCreds = Import-Clixml $adPpeCredsPath
        }else{
            $domainUnattend = Get-Content $unattendedPRD | Out-String
            $domainCreds = Import-Clixml $adProdCredsPath
        }

        if($DeploymentSpec.AppType -match "Appliance"){
            ### deploy vm shell - appliance ###
            New-VM -Name $DeploymentSpec.ServerName -VMHost $DeploymentSpec.vmHost -Datastore $DeploymentSpec.osDatastore -Location $DeploymentSpec.Folder -ErrorAction Stop | Out-Null
        }elseif($DeploymentSpec.OsType -match "Windows Server" -and $DeploymentSpec.AppType -notmatch "VDI"){
            ### deploy windows server and add to domain ###
            New-OSCustomizationSpec <#-CustomizationScript $domainUnattend#> -OrgName "SGT" -Domain $DeploymentSpec.domain -TimeZone 140 -DomainCredentials $domainCreds -OSType Windows -ChangeSid -Name $customSpecName -FullName "BCX" -Type Persistent -AdminPassword $tempAdmin -NamingScheme fixed -NamingPrefix $DeploymentSpec.ServerName -Description "Automated Deployment Usage"  -DnsSuffix $DeploymentSpec.dnsSuffix -Confirm:$false -ErrorAction Stop | Out-Null
            Get-OSCustomizationNicMapping -OSCustomizationSpec $customSpecName | Set-OSCustomizationNicMapping -Position 1 -IpMode UseStaticIP -IpAddress $DeploymentSpec.ipAddress -SubnetMask $DeploymentSpec.subnetMask -DefaultGateway $DeploymentSpec.gateway -Dns $DeploymentSpec.dnsIP -Confirm:$false -ErrorAction Stop | Out-Null
            #Get-ContentLibraryItem -Name $DeploymentSpec.Template | New-VM -Name $DeploymentSpec.ServerName -VMHost $DeploymentSpec.vmHost -Datastore $DeploymentSpec.osDatastore -Location $DeploymentSpec.Folder -ErrorAction Stop
            New-VM -Name $DeploymentSpec.ServerName -VMHost $DeploymentSpec.vmHost -Template $DeploymentSpec.Template  -Datastore $DeploymentSpec.osDatastore -Location $DeploymentSpec.Folder -OSCustomizationSpec $customSpecName -ErrorAction Stop | Out-Null
        }elseif($DeploymentSpec.OsType -like "Windows" -and $DeploymentSpec.AppType -match "VDI"){
            ### deploy VDI and add to domain ###            
            New-OSCustomizationSpec -OrgName "SGT" -Domain $DeploymentSpec.domain -TimeZone 140 -DomainCredentials $domainCreds -OSType Windows -ChangeSid -Name $customSpecName -FullName "BCX" -Type Persistent -AdminPassword $tempAdmin -NamingScheme fixed -NamingPrefix $DeploymentSpec.ServerName -Description "Automated Deployment Usage"  -DnsSuffix $DeploymentSpec.dnsSuffix -Confirm:$false -ErrorAction Stop | Out-Null
            Get-OSCustomizationNicMapping -OSCustomizationSpec $customSpecName | Set-OSCustomizationNicMapping -Position 1 -IpMode UseDhcp -Confirm:$false -ErrorAction Stop | Out-Null
            #Get-ContentLibraryItem -Name $DeploymentSpec.Template | New-VM -Name $DeploymentSpec.ServerName -VMHost $DeploymentSpec.vmHost -Datastore $DeploymentSpec.osDatastore -Location $DeploymentSpec.Folder -ErrorAction Stop
            New-VM -Name $DeploymentSpec.ServerName -VMHost $DeploymentSpec.vmHost -Template $DeploymentSpec.Template -Datastore $DeploymentSpec.osDatastore -OSCustomizationSpec $customSpecName -ErrorAction Stop | Out-Null
        }elseif($DeploymentSpec.OsType -match "RHEL"){
            ### deploy linux ### 
            #Get-ContentLibraryItem -Name $DeploymentSpec.Template | New-VM -Name $DeploymentSpec.ServerName -VMHost $DeploymentSpec.vmHost -Datastore $DeploymentSpec.osDatastore -Location $DeploymentSpec.Folder -ErrorAction Stop
            New-VM -Name $DeploymentSpec.ServerName -VMHost $DeploymentSpec.vmHost -Template $DeploymentSpec.Template -Datastore $DeploymentSpec.osDatastore -Location $DeploymentSpec.Folder -ErrorAction Stop | Out-Null
        }elseif($DeploymentSpec.OsType -match "shell"){
            ### Deploy VM Shell ###
            New-VM -Name $DeploymentSpec.ServerName -VMHost $DeploymentSpec.vmHost -Datastore $DeploymentSpec.osDatastore -Location $DeploymentSpec.Folder -ErrorAction Stop | Out-Null
        }else{
            ### DO NOTHING, NO APPLICABLE BUILD SELECTION
        }

        ### Get newly build vm to adjust specs ###
        $global:vm = Get-VM $DeploymentSpec.serverName

        ### adjust specs - nic, cpu, memory, notes ###
#        $vm | Set-VM -MemoryGB $DeploymentSpec.vMem -NumCpu $DeploymentSpec.vCPU -CoresPerSocket $($DeploymentSpec.vCPU/$DeploymentSpec.sockets) -Confirm:$false -ErrorAction Stop | Out-Null
        $vm | Set-VM -MemoryGB $DeploymentSpec.vMem -NumCpu $DeploymentSpec.vCPU -Confirm:$false -ErrorAction Stop | Out-Null
        $vm | Get-NetworkAdapter | Set-NetworkAdapter -NetworkName $DeploymentSpec.EPG -Confirm:$false -ErrorAction Stop | Out-Null
        $vm | Set-VM -Notes $DeploymentSpec.Description -Confirm:$false

        if(!$osOnlyBuild){
            ### add disks to vm shell ###
            $tier2Config = @()
            if($DeploymentSpec.tier2Disks){
                $t2Disks = ($DeploymentSpec.tier2Disks | Where-Object {$_ -ne "" }).Split(",")
            }
            foreach($t2Disk in $t2Disks){
                $tier2diskAdd = $vm | New-HardDisk -Datastore $DeploymentSpec.tier2Datastore -CapacityGB ([int]$t2Disk) -StorageFormat Thick -ErrorAction Stop
                $tier2Config += $tier2diskAdd
            }
        
            $tier3Config = @()
            if($DeploymentSpec.tier3Disks){
                $t3Disks = ($DeploymentSpec.tier3Disks | Where-Object {$_ -ne "" }).Split(",")
            }
            foreach($t3Disk in $t3Disks){
                $tier3diskAdd = $vm | New-HardDisk -Datastore $DeploymentSpec.tier3Datastore -CapacityGB ([int]$t3Disk) -StorageFormat Thick -ErrorAction Stop
                $tier3Config += $tier3diskAdd
            }
        }

        ### extend hard disk 1 if os only disk ###
        $hddPri = $vm | Get-HardDisk -Name "Hard disk 1"
        if($hddPri.CapacityGB -lt $DeploymentSpec.osSize){
            $hddPri | Set-HardDisk -CapacityGB $DeploymentSpec.osSize -Confirm:$false
        }

        ### Network config if BCX Linux Server ###
        if(($DeploymentSpec.competency).ToUpper() -eq ($bcxComp).ToUpper()){
            ### power on VM to join the domain ###
            $vm | Start-VM | Out-Null

            ### Sleep for initial OS bootup ###
            Start-Sleep -Seconds 180

            ### Variables to pass to VM Network config ###

            $vmName = $vm.Name
            $ip = $DeploymentSpec.ipAddress 
            $subnetMask = $DeploymentSpec.subnetMask 
            $gateway = $DeploymentSpec.gateway 
            $dns = $DeploymentSpec.dnsIP
            $cidr = "16"
            #$cidr = ($DeploymentSpec.cidr).split("/")[1]

            if($DeploymentSpec.OsType -match "RHEL8"){     
                ### invoke commands inside OS to connect to network ###
                $iplink = Invoke-VMScript -VM $vmName -ScriptText 'ip link | grep "[0-9]: e"' -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue
                $eth = $iplink.ScriptOutput -replace "^[0-9]: ", "" -replace ":", "" -split '<' | Select-Object -first 1
                $eth = $eth.Trim()

                Invoke-VMScript -VM $vmName -ScriptText "ip link set $eth up" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
                Invoke-VMScript -VM $vmName -ScriptText "echo $vmName > /etc/hostname" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
                Invoke-VMScript -VM $vmName -ScriptText "echo 'IPADDR=$ip' >> /etc/sysconfig/network-scripts/ifcfg-$eth" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
                Invoke-VMScript -VM $vmName -ScriptText "echo 'NETMASK=$subnetMask' >> /etc/sysconfig/network-scripts/ifcfg-$eth" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
                Invoke-VMScript -VM $vmName -ScriptText "echo 'GATEWAY=$gateway' >> /etc/sysconfig/network-scripts/ifcfg-$eth" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
                Invoke-VMScript -VM $vmName -ScriptText "rm /etc/sysconfig/network-scripts/ifcfg-ether" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
                Invoke-VMScript -VM $vmName -ScriptText "reboot" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
            }elseif($DeploymentSpec.OsType -match "RHEL9"){
                ### invoke commands inside OS to connect to network ###
                $iplink = Invoke-VMScript -VM $vmName -ScriptText 'ip link | grep "[0-9]: e"' -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue
                $eth = $iplink.ScriptOutput -replace "^[0-9]: ", "" -replace ":", "" -split '<' | Select-Object -first 1
                $eth = $eth.Trim()

                Invoke-VMScript -VM $vmName -ScriptText "ip link set $eth up" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
                Invoke-VMScript -VM $vmName -ScriptText "echo $vmName > /etc/hostname" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
                Invoke-VMScript -VM $vmName -ScriptText "nmcli connection delete $eth" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
                Invoke-VMScript -VM $vmName -ScriptText "nmcli connection add type ethernet con-name $eth ifname $eth ipv4.addresses $ip/$cidr ipv4.gateway $gateway ipv4.dns $dns ipv4.method manual ipv6.method disabled ipv4.dns-search `"mud.internal.co.za,sanlam.co.za,santam.co.za`"" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
                Invoke-VMScript -VM $vmName -ScriptText "reboot" -GuestUser root -GuestPassword "aq1sw2de3" -ErrorAction SilentlyContinue | Out-Null
            }            
            
            ### Sleep waiting for OS Reboot ###
            Start-Sleep -Seconds 60
        }
        
        ### if windows server/VDI, start-vm > join domain ###
        if($DeploymentSpec.AppType -notmatch "Appliance" -and $DeploymentSpec.OsType -match "Windows" ){
            ### power on VM to join the domain ###
            $vm | Start-VM | Out-Null
            "$(Get-date) - Sleep while waiting for domain join"
            ### 10min timer to allow domain join + policies to apply, etc ###
            Start-Sleep -Seconds 600
        }

        ### timeouts - if after 10min VM tools not responding (domain join still in progress/failed) extend timer by 5min to a total of 35min ###
        $failcount = 0            
        $toolsStatus = (Test-NetConnection $DeploymentSpec.ipAddress -Port 3389).TcpTestSucceeded
        if($toolsStatus){
            $successConfig = $true
        }else{
            $successConfig = $false
        }

        ### if VM is windows server, check test RDP port to see if server is accessible ###
        if($DeploymentSpec.OsType -like "Windows" -and $DeploymentSpec.AppType -notmatch "Appliance|VDI" ){
            while (($successConfig -eq $false) -and ($failcount -lt 5)) {
                Start-Sleep -Seconds 300
                $toolsStatus = (Test-NetConnection $DeploymentSpec.ipAddress -Port 3389).TcpTestSucceeded
                if(!$toolsStatus){
                    $failcount++
                }else{
                    $successConfig = $true
                }    
            }
        ### if VM is Integration Linux, test nothing as VM is offline ###
        }elseif($DeploymentSpec.competency -match $sgtComp){
            $successConfig = $true
        
        ### if VM is anything other than integration linux/windows server, check test an IP ping ###
        }else{
            while (($successConfig -eq $false) -and ($failcount -lt 5)) {
                Start-Sleep -Seconds 300
                $toolsStatus = (Test-NetConnection $DeploymentSpec.ipAddress).PingSucceeded
                if(!$toolsStatus){
                    $failcount++
                }else{
                    $successConfig = $true
                }    
            }
        }

        ### if 35min timer completes, domain join failed, throws error and kills deployment, putting to manual intervention ###
        if(!$successConfig){
            $buildStatus = "VM Join Domain timeout reached (35minutes). Domain join failed"
            $vm | Stop-VMGuest -Confirm:$false
        }else{
            $buildStatus = "VM deployed successfully"
        }

        ### if VDI extend inside OS C:\ to max ###
        if($DeploymentSpec.AppType -match "VDI" -and $buildStatus -match "VM deployed successfully"){            
            $cmdDiskExtend = '$size = Get-PartitionSupportedSize -DriveLetter C
                        Resize-Partition -DriveLetter C -Size $size.Sizemax'
            
            Invoke-VMScript -VM $vm -ScriptText $cmdDiskExtend -Verbose -GuestUser ".\Maverick" -GuestPassword $tempAdmin -ErrorAction Stop | Out-Null
        }

        ### Give VM a final restart before OS config ###
        if($buildStatus -match "VM deployed successfully" -and !($bcxLinux)){
            Start-Sleep -Seconds 300
            $vm | Restart-VMGuest -Confirm:$false
            Start-Sleep -Seconds 60
        }

        $deploySuccess = $true
        $response = $buildStatus

    }catch {
        $deploySuccess = $false
        $errorMessage = ($Error[0].exception).Message
        $response = ErrorHandling -errorMessage $errorMessage
    }

$task = "" | Select-Object Succeeded, Message
$task.Succeeded = $deploySuccess
$task.Message = $response
$task
}

Function ErrorHandling{
    param (
        [Parameter(Mandatory=$true)][String]$errorMessage
    )


    $errorMessage
}

VMDeploy_Management