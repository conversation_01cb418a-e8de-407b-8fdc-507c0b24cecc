# Docker Usage Guide

Complete guide for using the Docker-based Packer build environment.

## 🚀 Quick Start

### 1. Setup Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your credentials
nano .env
```

### 2. Build and Run
```bash
# Build the container
docker compose build

# Run interactive container
docker compose run --rm packer-builder bash

# Inside container, run builds
scripts/build/build-all.sh --help
scripts/build/build-aws.sh --version 2022
```

## 🔧 Service Profiles

### Main Services
```bash
# General purpose builder
docker compose run --rm packer-builder bash

# AWS-specific builds
docker compose --profile aws-only up packer-aws

# VMware-specific builds  
docker compose --profile vmware-only up packer-vmware

# Development environment
docker compose --profile development run --rm packer-dev

# CI/CD automated builds
docker compose --profile ci run --rm packer-ci
```

## 📋 Common Commands

### Build Commands
```bash
# Build all platforms and versions
docker compose run --rm packer-builder scripts/build/build-all.sh

# Build specific platform
docker compose run --rm packer-builder scripts/build/build-all.sh --platform aws
docker compose run --rm packer-builder scripts/build/build-all.sh --platform vmware

# Build specific version
docker compose run --rm packer-builder scripts/build/build-all.sh --version 2022

# Build with debug logging
docker compose run --rm packer-builder scripts/build/build-all.sh --debug

# Build in parallel
docker compose run --rm packer-builder scripts/build/build-all.sh --parallel

# Platform-specific builds
docker compose run --rm packer-builder scripts/build/build-aws.sh --version 2022 --debug
docker compose run --rm packer-builder scripts/build/build-vmware.sh --version both
```

### Container Management
```bash
# Build container with no cache
docker compose build --no-cache

# View container logs
docker compose logs packer-builder

# Execute commands in running container
docker compose exec packer-builder bash

# Stop all services
docker compose down

# Remove all containers and volumes
docker compose down -v --remove-orphans
```

## 🔍 Debugging

### Debug Mode
```bash
# Enable debug in environment
echo "PACKER_LOG=1" >> .env
echo "BUILD_DEBUG=true" >> .env

# Run with debug
docker compose run --rm packer-builder scripts/build/build-all.sh --debug

# Check logs
docker compose run --rm packer-builder tail -f logs/packer.log
```

### Container Debugging
```bash
# Check container environment
docker compose run --rm packer-builder env | grep -E "(AWS|VCENTER|PACKER)"

# Test Packer installation
docker compose run --rm packer-builder packer version

# Test AWS credentials
docker compose run --rm packer-builder aws sts get-caller-identity

# Validate templates
docker compose run --rm packer-builder packer validate windows-server-2022/aws.pkr.hcl
```

## 📊 Resource Management

### Monitor Resources
```bash
# Monitor container resources
docker stats

# Check disk usage
docker system df

# View container processes
docker compose top packer-builder
```

### Cleanup
```bash
# Clean up build artifacts
docker compose run --rm packer-builder rm -rf packer_cache/* logs/*

# Clean up Docker resources
docker system prune -f

# Remove unused volumes
docker volume prune -f
```

## 🔐 Security Best Practices

### Credential Management
```bash
# Use environment variables instead of hardcoded values
# Mount credentials as read-only volumes
# Use AWS IAM roles when possible
# Rotate credentials regularly
```

### Network Security
```bash
# Use custom networks for isolation
docker compose --profile development up --network packer-network

# Limit network access
# Use VPN for VMware access
# Restrict container capabilities
```

## 🚀 CI/CD Integration

### GitLab CI
```yaml
# .gitlab-ci.yml
build-images:
  image: docker:latest
  services:
    - docker:dind
  variables:
    DOCKER_DRIVER: overlay2
  before_script:
    - docker compose build
  script:
    - docker compose --profile ci run --rm packer-ci
  artifacts:
    reports:
      junit: logs/test-results.xml
    paths:
      - logs/
```

### GitHub Actions
```yaml
# .github/workflows/packer.yml
name: Build Packer Images
on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup environment
        run: |
          cp .env.example .env
          echo "AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }}" >> .env
          echo "AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }}" >> .env
      
      - name: Build images
        run: |
          docker compose build
          docker compose --profile ci run --rm packer-ci
```

### Jenkins Pipeline
```groovy
pipeline {
    agent any
    
    environment {
        AWS_ACCESS_KEY_ID = credentials('aws-access-key-id')
        AWS_SECRET_ACCESS_KEY = credentials('aws-secret-access-key')
    }
    
    stages {
        stage('Setup') {
            steps {
                sh 'cp .env.example .env'
                sh 'echo "AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}" >> .env'
                sh 'echo "AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}" >> .env'
            }
        }
        
        stage('Build') {
            steps {
                sh 'docker compose build'
                sh 'docker compose --profile ci run --rm packer-ci'
            }
        }
    }
    
    post {
        always {
            archiveArtifacts artifacts: 'logs/**/*', allowEmptyArchive: true
            sh 'docker compose down -v'
        }
    }
}
```

## 🔧 Advanced Configuration

### Custom Dockerfile
```dockerfile
# Extend the base image
FROM packer-builder:latest

# Add custom tools
RUN apt-get update && apt-get install -y \
    your-custom-tool \
    another-tool

# Add custom scripts
COPY custom-scripts/ /workspace/custom-scripts/
```

### Override Compose File
```yaml
# docker-compose.override.yml
version: '3.8'

services:
  packer-builder:
    environment:
      - CUSTOM_VAR=custom_value
    volumes:
      - ./custom-configs:/workspace/custom-configs
```

### Environment-Specific Configs
```bash
# Development
docker compose -f docker-compose.yml -f docker-compose.dev.yml up

# Production
docker compose -f docker-compose.yml -f docker-compose.prod.yml up

# Testing
docker compose -f docker-compose.yml -f docker-compose.test.yml up
```

## 📈 Performance Optimization

### Build Performance
```bash
# Use parallel builds
docker compose run --rm packer-builder ./build-all.sh --parallel

# Increase container resources
echo "MEMORY_LIMIT=8G" >> .env
echo "CPU_LIMIT=4.0" >> .env

# Use SSD storage for cache
echo "CACHE_PATH=/fast-storage/packer_cache" >> .env
```

### Network Performance
```bash
# Use host networking for better performance
echo "NETWORK_MODE=host" >> .env

# Use local registry for base images
docker compose build --build-arg BASE_IMAGE=localhost:5000/ubuntu:22.04
```

---

**Ready to build with Docker? Start with `docker compose run --rm packer-builder bash`!**

## 📁 Updated Directory Structure

After moving build scripts to `scripts/build/`:
```
autodeploy/packer/
├── scripts/
│   ├── build/                    # Build automation scripts
│   │   ├── build-all.ps1         # Windows comprehensive build
│   │   ├── build-all.sh          # Linux comprehensive build
│   │   ├── build-aws.ps1         # Windows AWS builds
│   │   ├── build-aws.sh          # Linux AWS builds
│   │   ├── build-vmware.ps1      # Windows VMware builds
│   │   ├── build-vmware.sh       # Linux VMware builds
│   │   └── build-hyperv.ps1      # Windows Hyper-V testing
│   ├── setup/                    # Setup scripts
│   ├── install-*.ps1             # Installation scripts
│   └── configure-*.ps1           # Configuration scripts
├── variables/                    # Variable files
├── windows-server-*/             # Packer templates
└── answer-files/                 # Unattended installation files
```
