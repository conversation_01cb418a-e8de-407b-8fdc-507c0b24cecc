# Packer Integration Quick Start Guide

This guide provides quick commands for using the integrated Packer functionality in the IAC automation repository.

## Prerequisites

1. **Configure Environment Variables**
   ```bash
   # Copy the example environment file
   cp packer/.env.example packer/.env
   
   # Edit the file with your specific values
   # At minimum, configure AWS credentials and/or VMware vSphere settings
   ```

2. **Ensure ISO Files are Available**
   ```bash
   # Place Windows Server ISO files in the packer/ISO directory
   # Or mount an external directory containing ISOs
   ```

## Quick Commands

### Start Packer Services

```bash
# Start all services including Packer
docker compose --profile packer up -d

# Start only AWS Packer builder
docker compose --profile aws-only up -d

# Start only VMware Packer builder
docker compose --profile vmware-only up -d
```

### Build Images - PowerShell (Recommended)

```bash
# Build all platforms and versions
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1

# Build only AWS images
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -Platform aws

# Build only VMware images
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -Platform vmware

# Build only Windows Server 2022
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -Version 2022

# Build with debug logging
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -DebugMode

# Build in parallel (faster)
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -Parallel

# Specific platform and version with debug
docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -Platform aws -Version 2022 -DebugMode
```

### Build Images - Linux Shell Scripts

```bash
# Build all platforms and versions
docker compose exec packer-builder packer/scripts/build/build-all.sh

# Build specific platform
docker compose exec packer-builder packer/scripts/build/build-all.sh --platform aws

# Build specific version
docker compose exec packer-builder packer/scripts/build/build-all.sh --version 2022

# Build with debug logging
docker compose exec packer-builder packer/scripts/build/build-all.sh --debug

# Build in parallel
docker compose exec packer-builder packer/scripts/build/build-all.sh --parallel

# Combined options
docker compose exec packer-builder packer/scripts/build/build-all.sh --platform vmware --version 2019 --debug
```

### Individual Platform Builds

```bash
# AWS-specific builds (PowerShell)
docker compose exec packer-builder pwsh -File packer/scripts/build/build-aws.ps1 -Version 2022

# VMware-specific builds (PowerShell)
docker compose exec packer-builder pwsh -File packer/scripts/build/build-vmware.ps1 -Version 2022

# AWS-specific builds (Shell)
docker compose exec packer-builder packer/scripts/build/build-aws.sh --version 2022

# VMware-specific builds (Shell)
docker compose exec packer-builder packer/scripts/build/build-vmware.sh --version 2022
```

## Monitoring and Troubleshooting

### Check Build Logs

```bash
# Follow live logs
docker compose exec packer-builder tail -f packer/logs/packer.log

# View recent logs
docker compose logs packer-builder

# Check container status
docker compose ps
```

### Interactive Shell Access

```bash
# Access the Packer container
docker compose exec packer-builder bash

# Access with PowerShell
docker compose exec packer-builder pwsh

# Check installed tools
docker compose exec packer-builder packer version
docker compose exec packer-builder terraform version
docker compose exec packer-builder pwsh --version
docker compose exec packer-builder aws --version
```

### Debug Commands

```bash
# Enable debug logging for next build
export PACKER_LOG=1

# Check Packer configuration
docker compose exec packer-builder packer validate packer/windows-server-2022/aws.pkr.hcl

# Test AWS credentials
docker compose exec packer-builder aws sts get-caller-identity

# Test VMware connectivity (if configured)
docker compose exec packer-builder ping $VCENTER_SERVER
```

## Environment Variables Reference

Key environment variables to configure in `packer/.env`:

```bash
# AWS (required for AWS builds)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=us-east-1

# VMware (required for VMware builds)
VCENTER_SERVER=your_vcenter_server
VCENTER_USERNAME=your_username
VCENTER_PASSWORD=your_password
VCENTER_DATACENTER=your_datacenter
VCENTER_CLUSTER=your_cluster
VCENTER_DATASTORE=your_datastore
VCENTER_NETWORK=your_network

# Build Configuration
BUILD_PLATFORM=both          # vmware, aws, or both
BUILD_VERSION=both           # 2019, 2022, or both
BUILD_DEBUG=false            # Enable debug logging
BUILD_PARALLEL=false         # Enable parallel builds
```

## Common Issues and Solutions

1. **Permission Errors**
   ```bash
   # Ensure proper permissions on mounted volumes
   sudo chown -R $USER:$USER packer/
   ```

2. **AWS Credential Issues**
   ```bash
   # Test AWS credentials
   docker compose exec packer-builder aws configure list
   docker compose exec packer-builder aws sts get-caller-identity
   ```

3. **VMware Connection Issues**
   ```bash
   # Test network connectivity
   docker compose exec packer-builder ping $VCENTER_SERVER
   docker compose exec packer-builder nslookup $VCENTER_SERVER
   ```

4. **ISO Not Found**
   ```bash
   # Check ISO directory
   ls -la packer/ISO/
   # Ensure ISO files are properly mounted or copied
   ```

5. **Build Failures**
   ```bash
   # Check detailed logs
   docker compose exec packer-builder cat packer/logs/packer.log
   # Enable debug mode for more information
   docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -DebugMode
   ```

## Cleanup

```bash
# Stop Packer services
docker compose --profile packer down

# Remove all containers and volumes
docker compose down -v

# Clean up build artifacts (optional)
rm -rf packer/logs/*
rm -rf packer/packer_cache/*
```

For more detailed information, see `packer/README.md`.
