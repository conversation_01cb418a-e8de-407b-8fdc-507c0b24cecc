Failed to connect at https://ad-api.dev.mud.internal.co.za/webhook/v1?key=18adecad-df09-47e2-ae05-de7686ae3156&script=dnschange.ps1&param=-reference EUP-83 -requester svcDevSlmDcsRelease -processor dev\svcDevDnsAutomation -task AddA -source server9.dev.mud.internal.co.za -destination *******.
Status: 500

Response: {"type":"https://tools.ietf.org/html/rfc7231#section-6.6.1","title":"Exception occurred in script execution.\n\t\t\t\t\t\t\t\t\t\t\n'./scripts/powershell\dnschange.ps1 -reference EUP-83 -requester svcDevSlmDcsRelease -processor dev\svcDevDnsAutomation -task AddA -source server9.dev.mud.internal.co.za -destination ******* failed. Output (stdOut): {\r\n "success": "true",\r\n "status": "NORMAL",\r\n "message": "Created A Record for server9.dev.mud.internal.co.za pointing to ******* in dev.mud.internal.co.za",\r\n "data": {\r\n "RecordName": "server9",\r\n "IPAddress": "*******",\r\n "HostName": "",\r\n "ZoneName": "dev.mud.internal.co.za",\r\n "RecordType": "A",\r\n "DNSServer": "SRV010699.dev.mud.internal.co.za",\r\n "Reference": "EUP-83",\r\n "Requester": "svcDevSlmDcsRelease"\r\n },\r\n "date": "2025-02-26 10:23:57"\r\n}\r\n' Error (stdErr): Get-DnsServerResourceRecord : Failed to get server9 record in dev.mud.internal.co.za zone on \r\nSRV010699.dev.mud.internal.co.za server.\r\nAt C:\API\webhookshell\scripts\powershell\dnschange.ps1:247 char:17\r\n+ ... $record = Get-DnsServerResourceRecord -ComputerName $script:DNSServ ...\r\n+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\n + CategoryInfo : ObjectNotFound: (server9:root/Microsoft/...rResourceRecord) [Get-DnsServerResourceRecord \r\n ], CimException\r\n + FullyQualifiedErrorId : WIN32 9714,Get-DnsServerResourceRecord\r\n \r\n","status":500,"traceId":"00-539629f9d5d26ec9fe9db7194e247d9d-60f2f8b6dc9392b7-00"}

Response headers: {'Transfer-Encoding': u'chunked', 'Server': u'Microsoft-IIS/10.0', 'Pragma': u'no-cache', 'Date': u'Wed, 26 Feb 2025 08:23:57 GMT', 'Cache-Control': u'no-cache,no-store', 'Expires': u'-1', 'Content-Type': u'application/problem+json; charset=utf-8'}

Exception during execution:
javax.script.ScriptException: SystemExit: 1 in <script> at line number 71