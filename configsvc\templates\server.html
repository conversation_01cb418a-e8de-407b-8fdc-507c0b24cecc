{% extends "base.html" %}
{% block content %}
    <p class="text-danger"><b>{{ server_data.messages }}</b></p>

    <!-- Tabs navs  data-mdb-tab-init   -->

    <ul class="nav nav-tabs">
        <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#tab_annotations" aria-current="page">Annotations</a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab_compute">Compute</a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab_disks">Disks</a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab_rates">Rates</a></li>
        <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#tab_getquote">Resources Quote</a></li>
      </ul>
    <!-- Tabs navs -->

    <!-- Tabs content -->
    <div class="tab-content" id="server_tabs_content">
        <div class="tab-pane fade show active" id="tab_annotations" role="tabpanel" aria-labelledby="tab_annotations">

            <table class="table">
                <thead>
                    <tr>
                        <th scope="col">Item</th> <th scope="col">Detail</th>            
                    </tr>
                </thead>
                <tbody>            
                    {% for key, value in server_data.data.annotations.items() %}
                    <tr>
                        <td><b>{{ key }}</b></td>  <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

        </div>
        <div class="tab-pane fade" id="tab_compute" role="tabpanel" aria-labelledby="tab_compute">
            <table class="table">
                <thead>
                    <tr>
                        <th scope="col">Item</th> <th scope="col">Detail</th>            
                    </tr>
                </thead>
                <tbody>            
                    {% for key, value in server_data.data.compute.items() %}
                    <tr>
                        <td><b>{{ key }}</b></td>  
                        <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="tab-pane fade" id="tab_disks" role="tabpanel" aria-labelledby="tab_disks">
                <p>Disks: {{ server_data.data.disks|length }}</p>
                <table class="table">
                    
                    <thead>
                        <tr>
                            {% for key in server_data.data.disks[0] %}
                                <th scope="col"> {{ key }} </th>        
                            {% endfor %}  
                        </tr>
                    </thead>
                    <tbody>                
                        
                        {% for i in range (0, server_data.data.disks|length) %}
                        <tr>
                            {% for key, value in server_data.data.disks[i].items() %}
                                <th class="font-weight-light" scope="col"> {{ value }} </th>        
                            {% endfor %}     
                        </tr>  
                        {% endfor %}  
                    </tbody>
                    
                </table>
        </div>
        <div class="tab-pane fade" id="tab_rates" role="tabpanel" aria-labelledby="tab_rates">
            <table class="table">
                <thead>
                    <tr>
                        <th scope="col">Item</th> <th scope="col">Detail</th>            
                    </tr>
                </thead>
                <tbody>            
                    {% for key, value in server_data.data.rates.items() %}
                    <tr>
                        <td>{{ key }}</td>  <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Portrait view -->
        <div class="tab-pane fade" id="tab_getquote" role="tabpanel" aria-labelledby="tab_getquote">
            <form action="/quote" method="post" name="add_resources">
                <div class="form-group">
                    <label for="enterserver"><b>Target Server</b></label>
                    <input type="text" class="form-control" name="server_name" value="{{ server_data.data.annotations['server_name'] }}" readonly>
                    <label for="enterserver" ><b>Reference Number</b></label>
                    <input type="text" required class="form-control" name="reference" id="reference" placeholder="Enter reference number"><p></p>
                    <label for="enterserver"><b>Server Count</b></label>
                    <input name="vm_count" id="vm_count" type="number"  min="1" max="4" value="1" placeholder="Enter Instances Count">
                    <label for="enterserver"><b>Add SRM Backup</b></label>
                    <input name="add_srm" id="add_srm" type="checkbox">
                    <hr width="100%" size="2">
                    <!-- Compute Resources -->
                    <p><b>Compute:</b></p>
                    <table class="table">
                        <thead>
                            <tr>
                                <th width="15%" scope="col">Item</th>
                                <th width="15%" scope="col">Current Capacity</th> 
                                <th width="15%" scope="col">Capacity to Add</th>        
                                <th width="55%" scope="col"></th>       
                            </tr>
                        </thead>
                        <tbody>           
                            <tr>
                                <td width="15%" scope="col">vCPUs</td>  
                                <td width="15%" scope="col">{{ server_data.data.compute['cpus'] }}</td>  
                                <td width="15%"><input name="add_vcpus" id="add_vcpus" type="number" min="0"  max="8" value="0" placeholder="To max 8 total"></td>
                                <td width="55%" scope="col"></td>  
                            </tr>
                            <tr>   
                                <td width="15%" scope="col">vRAM GB</td>    
                                <td width="15%" scope="col">{{ server_data.data.compute['memory_gb'] }}</td>  
                                <td width="15%"><input name="add_vram_gbs" id="add_vram_gbs" type="number" min="0"  max="64" value="0" placeholder="Enter GBs, to max 64GB"></td>
                    
                            </tr>
                        </tbody>
                    </table>

                    <p><b>Disks:</b> {{ server_data.data.disks|length }}</p>
                    <table class="table">                    
                        <thead>
                            <tr>                        
                                <th width="15%" scope="col">disk</th>
                                <th width="15%" scope="col">Current Capacity</th>
                                <th width="15%" scope="col">Capacity to Add</th>    
                                <th width="15%" scope="col">disk_type</th>  
                                <th width="15%" scope="col">mirror_type</th>  
                                <th width="25%" scope="col">mount(s)</th>
                            </tr>
                        </thead>
                        <tbody>              
                            
                            {% for i in range (0, server_data.data.disks|length) %}
                            <tr>
                                <td width="15%" scope="col">{{ server_data.data.disks[i]['disk'] }}</td>
                                <td width="15%" scope="col">{{ server_data.data.disks[i]['capacity_gb'] }}</td>
                                <td width="15%"><input name="add_disk_gbs" id="add_disk_gbs" type="number"  min="0"  max="4000" value="0" placeholder="Enter amount in GBs"></td>
                                <td width="15%" scope="col"><input  type="text" id="add_disk_types" name="add_disk_types" value="{{ server_data.data.disks[i]['disk_type'] }}" readonly></td>
                                <td width="15%" scope="col"><input  type="text" id="get_mirror_type" name="get_mirror_type" value="{{ server_data.data.disks[0]['mirror_type'] }}" readonly></td>  
                                <td width="25%" scope="col">{{ server_data.data.disks[i]['disk_path'] }}</td>

                            </tr>  
                            {% endfor %}  
                            <tr>
                                <td width="15%" scope="col">New disk {{ server_data.data.disks|length + 1 }}</td>
                                <td width="15%" scope="col">0</td>
                                <td width="15%"><input name="add_disk_gbs" id="add_disk_gbs" type="number"  min="0"  max="4000" value="0" placeholder="Enter amount in GBs"></td>
                                <td width="15%" scope="col">
                                    <select id="add_disk_types" name="add_disk_types">
                                        <option value="NONE" selected>NONE</option>
                                        <option value="DIAMOND">DIAMOND</option>
                                        <option value="SAPHIRE">SAPHIRE</option>
                                        <option value="QUARTZ">QUARTZ</option>
                                        <option value="RUBY">RUBY</option>
                                        <option value="AMBER">AMBER</option>
                                        <option value="EMERALD">EMERALD</option>
                                    </select>
                                </td>  
                                <td width="15%" scope="col"><input type="text" id="get_mirror_type" name="get_mirror_type" value="{{ server_data.data.disks[0]['mirror_type'] }}" readonly></td>

                            </tr>  

                        </tbody>
                        
                    </table>
                </div>
                <button type="submit" class="btn btn-primary" id="submit_resources">Get Quote</button>
            </form>
    </div>
    </div>
    <!-- Tabs content -->


{% endblock %}


   