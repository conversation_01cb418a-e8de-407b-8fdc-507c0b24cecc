# iac-automation

## Overview

This project provides a local automation stack for infrastructure-as-code and workflow testing using Docker Compose.

- **Terraform** (containerized, for IaC)
- **Packer** (Windows Server image building for VMware & AWS)
- **PowerShell 7.5.2** (Windows automation & Packer builds)
- **FastAPI** (containerized API to POST commands for Terraform and other tools)

---

## Prerequisites

- Docker & Docker Compose
- (Optional) Terraform CLI for local runs

---

## Project Structure (Needs updating)

```text:
iac-automation/
├── docker-compose.yml
├── main.tf
├── variables.tf
├── outputs.tf
├── iac-tf-pwsh.Dockerfile
├── zscaler_root.cer
├── LICENSE.txt
├── infrastructure/
│   ├── vm-spec.json
│   └── vm-spec_test.json
├── scripts/
│   └── pre-deploy/
│       ├── ad-validation.ps1
│       ├── connectivity-check.ps1
│       └── ipam-validation.ps1
├── packer/                          # Packer Windows Server image building
│   ├── scripts/build/               # Build automation scripts (PS1 & SH)
│   ├── variables/                   # Packer variable files
│   ├── windows-server-2019/         # Windows Server 2019 templates
│   ├── windows-server-2022/         # Windows Server 2022 templates
│   ├── answer-files/                # Windows unattended installation
│   ├── logs/                        # Build logs
│   ├── ISO/                         # ISO storage
│   └── README.md                    # Packer documentation
├── output/
   └── (generated deployment summary, logs, etc.)
```

---

## Quick Start

 > This section is in-progress.

1. **Clone the repo**

   ```bash
   <NAME_EMAIL>:bcxslm/iac-automation.git
   cd iac-automation
   ```

2. **Configure environment**
   - Edit `.env` for Terraform and service variables (no secrets for dev/testing).
   - Place vSphere credentials in `TF_VAR_vsphere_credentials` (see `variables.tf` for required variables).

3. **Start the stack**

   ```bash
   # Start core services
   docker compose up -d

   # Or start with Packer services
   docker compose --profile packer up -d
   ```

4. **Check services**

   ```bash
   docker compose ps
   ```

5. **Run Terraform**

   ```bash
   docker exec -it pwsh-terraform bash
   terraform init
   terraform plan
   terraform apply
   # Output and deployment summary will be written to ./output/
   ```

6. **Build Windows Server Images (Optional)**

   ```bash
   # Start Packer services
   docker compose --profile packer up -d

   # Build all images using PowerShell
   docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1

   # Build specific platform/version
   docker compose exec packer-builder pwsh -File packer/scripts/build/build-all.ps1 -Platform aws -Version 2022

   # Or use Linux shell scripts
   docker compose exec packer-builder packer/scripts/build/build-all.sh --platform vmware --version 2019
   ```

---

## Service Endpoints

- **Terraform**: via container shell
- **Packer**: via container shell (when using `--profile packer`)
- **FastAPI**: <http://localhost:3333/> or <http://localhost:3333/docs>

---

## Configuration

 > This section is in-progress.

- **VM specs**: Edit `infrastructure/vm-spec.json` (or `vm-spec_test.json` for test scenarios)
- **Terraform variables**: See `variables.tf` (override via `.env`, environment, or `TF_VAR_vsphere_credentials`)
- **Packer configuration**: Copy `packer/.env.example` to `packer/.env` and configure AWS/VMware credentials
- **Packer variables**: Edit files in `packer/variables/` for platform-specific settings
- **Certificates**: `zscaler_root.cer` for proxy/SSL testing
- **Output**: Deployment summaries and logs are written to `output/` after `terraform apply`

---

## CI/CD

 > This section is in-progress.

- **Build & push Docker image**: See `.github/workflows/build-push-dev.yaml`
  - Uses secrets `DOCKERHUB_USERNAME` and `DOCKERHUB_TOKEN`
  - Tags image as `dev` and pushes to Docker Hub

---

## Cleaning Up

 > This section is in-progress.

```bash
terraform destroy
docker compose down -v
```

---

## Troubleshooting

 > This section is in-progress.

- **Containers not running**: `docker compose ps`
- **Service not ready**: Wait longer, check logs (`docker compose logs <service>`)
- **SSL errors**: `allow_unverified_ssl = true` is set in Terraform (Only recommended in local environment)

---

## Notes

 > This section is in-progress.

- For development/testing only. Do not use default credentials or self-signed certificates in production.
- The `output/` directory is auto-created by Terraform for deployment summaries and logs.

---

## Credits

- [Terraform + Packer](https://hashicorp.com)
