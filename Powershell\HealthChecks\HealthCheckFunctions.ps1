﻿<#
    .SYNOPSIS
    Performs health checks and sends results to Teams channels

    .DESCRIPTION
    Runs curated health checks for various systems and sends formatted results to Teams channels

    .EXAMPLE
    .\HealthCheckFunctions.ps1

    .NOTES
    File Name: HealthCheckFunctions.ps1
    Author: <PERSON><PERSON>
    Version 1.1 - Updated: 23/05/2025
#>
#requires -Version 5
#requires -Modules PSTeams, PSPHPIPAM

#region Variables
$Script:CurrentDate = Get-Date -Format "dd-MM-yyyy"
$Script:TimeStamp = Get-Date
$Script:TeamsID = 'https://bcx.webhook.office.com/webhookb2/df110042-736f-4313-9fad-5a05b71ec2af@32acc968-ee0c-4a4b-a2a2-f578609a3785/IncomingWebhook/b86002499b9a4874a2c549808fbe3131/f7d1b729-259a-48af-a44d-0c79cc862e41/V21kb2G4Zj3NPxLcvwcDX3pYVwiaByIJ45tbeu8QT8-I41'
$Script:TeamsMessageTitle = "DevOps Daily Health Checks"
$Script:TeamsActivityTitle = "**DevOps Services**"
$Script:TeamsActivitySubtitle = "Daily Health Checks - $CurrentDate"
$Script:TeamsActivityImage = "https://pbs.twimg.com/profile_images/832257499772370944/kYwKP3xq_400x400.jpg"
$Script:ProxyServer = "http://serverproxy.mud.internal.co.za:8080"
$Script:UseProxy = $false  # Set to $false if you want to disable proxy temporarily

# Define text highlighting colors
$Script:Highlight = @{
    Failed = 'Red'
    Passed = 'Green'
}

# Create log directory if it doesn't exist
$Script:LogPath = "$PSScriptRoot\Logs"
if (-not (Test-Path -Path $LogPath)) {
    New-Item -Path $LogPath -ItemType Directory -Force | Out-Null
}
$Script:LogFile = Join-Path -Path $LogPath -ChildPath "HealthCheck_$CurrentDate.log"
#endregion Variables

#region Functions
function Write-Log {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [ValidateSet('Info', 'Warning', 'Error')]
        [string]$Level = 'Info'
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # Write to log file
    Add-Content -Path $LogFile -Value $logMessage
    
    # Also output to console with appropriate color
    switch ($Level) {
        'Info'    { Write-Host $logMessage }
        'Warning' { Write-Host $logMessage -ForegroundColor Yellow }
        'Error'   { Write-Host $logMessage -ForegroundColor Red }
    }
}

function Format-HealthStatus {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Text
    )
    
    $output = $false
    
    foreach ($entry in $Highlight.Keys) {
        $parts = $Text -split $entry
        if ($parts.count -gt 1) {
            Write-Host $parts[0] -NoNewline
            Write-Host $entry -ForegroundColor $Highlight.$entry
            $output = $true
            break
        }
    }
    
    if (-not $output) { 
        Write-Host $Text 
    }
}

function Get-SvcAccLogon {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$ServerName,
        
        [Parameter(Mandatory = $true)]
        [string]$ServiceAccount
    )
    
    try {
        Write-Log "Checking if $ServiceAccount is logged on $ServerName" -Level Info
        $user = qwinsta $ServiceAccount /server:$ServerName 2>$null
        
        if ($user -and $user -notmatch "No session exists|No entries in session directory") {
            $result = "$ServiceAccount is Logged On $ServerName - Passed"
            Write-Log $result -Level Info
            return $result
        }
        else {
            $result = "$ServiceAccount is Not Logged On $ServerName - Failed"
            Write-Log $result -Level Warning
            return $result
        }
    }
    catch {
        $errorMsg = "Error checking service account logon: $_"
        Write-Log $errorMsg -Level Error
        return "Couldn't Query Server $ServerName for $ServiceAccount status - Failed"
    }
}

function Test-IPAM {
    [CmdletBinding()]
    param()
    
    try {
        Write-Log "Testing IPAM connectivity and status" -Level Info
        $ipamServer = "ipam.sanlam.co.za"
        $phpSite = Test-Connection $ipamServer -Quiet -Count 1
        
        if ($phpSite) {
            $apiURL = "https://$ipamServer/ipam/api"
            $apiAppID = "automon"
            $apiKey = "2MFYKYtHs3BxGenwqVDeIvbO98g1U8PH"
            
            try {
                New-PhpIpamSession -UseStaticAppKeyAuth -PhpIpamApiUrl $apiURL -AppID $apiAppID -AppKey $apiKey | Out-Null
                $ipamSubnet = Get-PhpIpamSubnet -CIDR *********/22
                
                if ($ipamSubnet) {
                    $timeDiff = New-TimeSpan -Start $ipamSubnet.lastScan -End $Script:TimeStamp
                    
                    if ($timeDiff.Days -gt 0) {
                        $result = "The last scan date was $($timeDiff.Days) days, $($timeDiff.Hours) hours, $($timeDiff.Minutes) minutes ago - Failed"
                    }
                    else {
                        $result = "The last scan date was $($timeDiff.Days) days, $($timeDiff.Hours) hours, $($timeDiff.Minutes) minutes ago - Passed"
                    }
                }
                else {
                    $result = "There was an issue getting the subnet detail - Failed"
                }
            }
            catch {
                $result = "IPAM API connection failed: $($_.Exception.Message) - Failed"
                Write-Log $result -Level Error
            }
        }
        else {
            $result = "IPAM server $ipamServer is not reachable - Failed"
            Write-Log $result -Level Error
        }
        
        Write-Log "IPAM check result: $result" -Level Info
        return $result
    }
    catch {
        $errorMsg = "Error testing IPAM: $_"
        Write-Log $errorMsg -Level Error
        return "IPAM check failed with an exception - Failed"
    }
}

function Test-AutoDeploy {
    [CmdletBinding()]
    param()
    
    try {
        Write-Log "Testing AutoDeploy scheduled tasks" -Level Info
        $svcAccount = "svcscvmmadmin"
        $serverName = "srv009484"
        $failedTasks = @()
        
        try {
            $autoDeploySchedulers = Get-ScheduledTask -CimSession $serverName -TaskPath "\" -ErrorAction Stop | 
                Where-Object { $_.TaskName -match "AutoDeploy" -and $_.State -notmatch "disabled" } | 
                Get-ScheduledTaskInfo
                
            $today = Get-Date -Format dd/MM/yyyy
            
            foreach ($task in $autoDeploySchedulers) {
                $lastRunTime = Get-Date $task.LastRunTime -Format dd/MM/yyyy
                
                if ($lastRunTime -ne $today) {
                    $failedTasks += $task.TaskName
                }
            }
            
            if ($failedTasks.Count -gt 0) {
                $taskResult = ($failedTasks -join ", ") + " has not run yet today - Failed"
            }
            else {
                $taskResult = "All Schedulers Running as scheduled - Passed"
            }
        }
        catch {
            $taskResult = "Error connecting to $serverName to check scheduled tasks: $($_.Exception.Message) - Failed"
            Write-Log $taskResult -Level Error
        }
        
        $accountResult = Get-SvcAccLogon -ServerName $serverName -ServiceAccount $svcAccount
        $finalResult = "$taskResult`r`n$accountResult"
        Write-Log "AutoDeploy check completed" -Level Info
        
        return $finalResult
    }
    catch {
        $errorMsg = "Error testing AutoDeploy: $_"
        Write-Log $errorMsg -Level Error
        return "AutoDeploy check failed with an exception - Failed" 
    }
}

function Send-TeamsHealthReport {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Content,
        
        [Parameter(Mandatory = $false)]
        [string]$WebhookUrl = $Script:TeamsID,
        
        [Parameter(Mandatory = $false)]
        [string]$ProxyServer,
        
        [Parameter(Mandatory = $false)]
        [System.Management.Automation.PSCredential]$ProxyCredential
    )
    
    try {
        Write-Log "Preparing to send Teams message" -Level Info
        
        # Create Teams message card
        $teamsMessage = @{
            type       = "MessageCard"
            themeColor = "0078D7"
            title      = $Script:TeamsMessageTitle
            summary    = "Health Check Results for $Script:CurrentDate"
            sections   = @(
                @{
                    activityTitle    = $Script:TeamsActivityTitle
                    activitySubtitle = $Script:TeamsActivitySubtitle
                    activityImage    = $Script:TeamsActivityImage
                    text             = $Content
                }
            )
        }
        
        # Convert to JSON
        $body = $teamsMessage | ConvertTo-Json -Depth 4
        
        # Send message
        $params = @{
            Uri         = $WebhookUrl
            Method      = 'POST'
            Body        = $body
            ContentType = 'application/json'
        }
        
        # Add proxy settings if provided
        if ($ProxyServer) {
            Write-Log "Using proxy server: $ProxyServer" -Level Info
            $params['Proxy'] = $ProxyServer
            
            if ($ProxyCredential) {
                Write-Log "Using proxy credentials" -Level Info
                $params['ProxyCredential'] = $ProxyCredential
            }
        }
        
        $response = Invoke-RestMethod @params
        Write-Log "Teams message sent successfully" -Level Info
        return $true
    }
    catch {
        $errorMsg = "Failed to send Teams message: $_"
        Write-Log $errorMsg -Level Error
        return $false
    }
}

function Start-HealthChecks {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $false)]
        [ValidateSet('Bold', 'Table')]
        [string]$TeamsFormat = 'Bold'
    )
    
    Write-Log "Starting health checks" -Level Info
    
    # Run health checks
    $ipamResults = Test-IPAM
    $autoDeployResults = Test-AutoDeploy
    
    # Format results for Teams based on format preference
    if ($TeamsFormat -eq 'Bold') {
        # Method 2: Bold formatting for status
        $contentTeams = @"
**Health checks for phpIPAM:**
$(if ($ipamResults -match "- Passed") { 
    $ipamResults -replace '- Passed', '- **Passed**'
} else { 
    $ipamResults -replace '- Failed', '- **Failed**'
})

**Health checks for AutoDeploy:**
$(($autoDeployResults -split "`r`n" | ForEach-Object { 
    if ($_ -match "- Passed") { 
        $_ -replace '- Passed', '- **Passed**'
    } else { 
        $_ -replace '- Failed', '- **Failed**'
    }
}) -join "<br>")
"@
    }
    else {
        # Method 3: Table-based formatting
        # Extract IPAM check name and status
        $ipamCheck = $ipamResults -replace ' - (Passed|Failed)$', ''
        $ipamStatus = if ($ipamResults -match "- Passed$") { ":white_check_mark: Passed" } else { ":x: Failed" }
        
        # Process AutoDeploy results
        $autoDeployTable = ($autoDeployResults -split "`r`n" | ForEach-Object {
            $checkName = $_ -replace ' - (Passed|Failed)$', ''
            $status = if ($_ -match "- Passed$") { "[+] Passed" } else { "[-] Failed" }
            "| $checkName | $status |"
        }) -join "`n"
        
        $contentTeams = @"
**Health checks for phpIPAM:**

| Check | Status |
|-------|--------|
| $ipamCheck | $ipamStatus |

**Health checks for AutoDeploy:**

| Check | Status |
|-------|--------|
$autoDeployTable
"@
    }
    
    # Display results with color highlighting in console
    $contentTeams -split "`n" | ForEach-Object {
        Format-HealthStatus -Text $_
    }
    
    # Send to Teams
    Write-Log "Sending results to Teams" -Level Info
    $proxyParams = @{}
    if ($Script:UseProxy -and $Script:ProxyServer) {
        $proxyParams['ProxyServer'] = $Script:ProxyServer
    }
    
    $teamsSent = Send-TeamsHealthReport -Content $contentTeams @proxyParams
    
    if ($teamsSent) {
        Write-Log "Health check completed and sent to Teams successfully" -Level Info
    }
    else {
        Write-Log "Health check completed but failed to send to Teams" -Level Warning
    }
    
    return @{
        TeamsContent = $contentTeams
        TeamsSent    = $teamsSent
        Format       = $TeamsFormat
    }
}
#endregion Functions

# Execute health checks when script is run directly (not sourced)
if ($MyInvocation.InvocationName -ne '.') {
    Start-HealthChecks
}