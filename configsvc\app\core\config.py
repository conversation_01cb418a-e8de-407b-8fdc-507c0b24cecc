from pydantic_settings import BaseSettings, SettingsConfigDict
from functools import lru_cache
from fastapi import BackgroundTasks
import os
from dotenv import load_dotenv, find_dotenv
from app import logging
from rich import print

basedir = os.path.abspath(os.path.dirname(__file__))
class Settings(BaseSettings):
    
    print(f"BASEDIR: {basedir}")
    logging.info("Loading configs...")
    DOTENV: str = os.path.join(basedir,"env", ".env")
    print(f"ENV Path: {DOTENV}")
    logging.info(f"ENV: {DOTENV}")
    load_dotenv(DOTENV, verbose=True)
    # model_config = SettingsConfigDict(env_file=".env", extra="allow") # DOTENV

    name: str = "CPS Configs Service"
    endpoint: str = "/api/v1"

    ENV: str = os.getenv('ENV') #"development"
    # For DEBUG mode
    debug: bool = os.getenv('DEBUG')

    # API Publish

    UVICORN_HOST: str = os.getenv('UVICORN_HOST') #8000
    UVICORN_PORT: int = os.getenv('UVICORN_PORT') #8000
    # print(f"API on {API_HOST}:{API_PORT}")
    logging.info(f"API on {UVICORN_HOST}:{UVICORN_PORT}")

    # For database connection
    DB_WUSR: str = os.getenv('DB_WUSR')
    DB_WPWD: str = os.getenv('DB_WPWD') 	
    DB_RUSR: str = os.getenv('DB_RUSR')
    DB_RPWD: str = os.getenv('DB_RPWD') 	
    DB_NAME: str = os.getenv('DB_NAME') 
    DB_HOST: str = os.getenv('DB_HOST') 
    DB_PORT: int = os.getenv('DB_PORT') 

    SQLALCHEMY_DATABASE_WRITE_URI: str = f"mariadb+pymysql://{DB_WUSR}:{DB_WPWD}@{DB_HOST}:{DB_PORT}/{DB_NAME}" # str = SQLALCHEMY_DATABASE_URI.format(DB_WUSR, DB_WPWD, DB_HOST, DB_PORT, DB_NAME) #"mysql+aiomysql://fastapi:fastapi@localhost:3306/fastapi"
    SQLALCHEMY_DATABASE_READ_URI: str = f"mariadb+pymysql://{DB_RUSR}:{DB_RPWD}@{DB_HOST}:{DB_PORT}/{DB_NAME}" # SQLALCHEMY_DATABASE_URI.format(DB_RUSR, DB_RPWD, DB_HOST, DB_PORT, DB_NAME) #"mysql+aiomysql://fastapi:fastapi@localhost:3306/fastapi"
    
    # SQLALCHEMY_DATABASE_URI: str = f"mariadb+pymysql://{DB_WUSR}:{DB_WPWD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    DB_CONNECTION: str = f"mariadb+pymysql://{DB_WUSR}:***@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    logging.info(f"DB_URL = mariadb+pymysql://{DB_WUSR}:***@{DB_HOST}:{DB_PORT}/{DB_NAME}")
    SQLALCHEMY_TRACK_MODIFICATIONS: bool = False 

    # FTP Configs
    FTP_HOST: str =os.getenv('FTP_HOST')
    FTP_PORT: int =os.getenv('FTP_PORT')
    FTP_USER: str =os.getenv('FTP_USER')
    FTP_PWRD: str =os.getenv('FTP_PWRD')
    FTP_PATH: str =os.getenv('FTP_PATH')
    FTP_DEBUG: str =os.getenv('FTP_DEBUG')
    FTP_ENABLED: str =os.getenv('FTP_ENABLED')
    FTP_CONNECTION: str = f"{FTP_USER}:***@{FTP_HOST}:{FTP_PORT}{FTP_PATH}"

    DCS_PATH: str =os.getenv('DCS_PATH')
    DOCS_DIR: str = os.path.join(basedir,"docs")
    os.makedirs(DOCS_DIR, exist_ok=True)
    DCS_QUOTES_URL: str = os.getenv('DCS_QUOTES_URL')
    # JWT_SECRET_KEY: str = str(os.urandom(12)) #"fastapi"
    # JWT_ALGORITHM: str = "HS256"

    # Names Services ENVs
    MAX_COUNT: int = int(os.getenv('MAX_COUNT'))
    INIT_COUNTER: int = int(os.getenv('INIT_COUNTER'))
    INIT_REF: str = os.getenv('INIT_REF')
    OBJECT_TYPES: str = os.getenv('OBJECT_TYPES') #["SRV","POC","SDV","CLS","LST"]
    POP_FIELDS: str = os.getenv('POP_FIELDS') #["SRV","POC","SDV","CLS","LST"]


def get_Settings():
    env = os.getenv("ENV", "local")
    config_type = {
        "local": Settings()
    }
    # ,        "prod": ProductionConfig(),
    return Settings() #config_type[env]


config = Settings() #  get_Settings() # 