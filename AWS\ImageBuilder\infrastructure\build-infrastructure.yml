# AWS Image Builder Infrastructure Configuration
# Defines the build environment for creating custom Windows Server 2022 images

name: WindowsServer2022BuildInfrastructure
description: Infrastructure configuration for building Windows Server 2022 custom images

# Instance configuration for the build process
instanceTypes:
  - m5.large      # Primary choice - good balance of CPU/memory for Windows builds
  - m5.xlarge     # Fallback option for faster builds
  - m4.large      # Alternative option

# Instance profile with necessary permissions
instanceProfileName: EC2ImageBuilderInstanceProfile

# Security group configuration
securityGroupIds:
  - sg-0123456789abcdef0  # Replace with your security group ID that allows:
                          # - Outbound HTTPS (443) for downloading packages
                          # - Outbound HTTP (80) for package repositories
                          # - Outbound DNS (53) for name resolution
                          # - Outbound NTP (123) for time synchronization

# Subnet configuration
subnetId: subnet-0123456789abcdef0  # Replace with your private subnet ID
                                    # Should have internet access via NAT Gateway for downloads

# Key pair for troubleshooting (optional)
keyPair: your-key-pair-name  # Replace with your EC2 key pair name

# Terminate instance on failure
terminateInstanceOnFailure: true

# Enable instance metadata service
instanceMetadataOptions:
  httpTokens: required
  httpPutResponseHopLimit: 2

# Resource tags for the build instance
resourceTags:
  Name: ImageBuilder-WindowsServer2022-Build
  Environment: Production
  Project: CustomAMI
  Owner: Infrastructure Team
  Purpose: Image Building
  OS: Windows Server 2022

# Logging configuration
logging:
  s3Logs:
    s3BucketName: your-imagebuilder-logs-bucket  # Replace with your S3 bucket for logs
    s3KeyPrefix: windows-server-2022/

# SNS topic for build notifications (optional)
snsTopicArn: arn:aws:sns:us-east-1:123456789012:imagebuilder-notifications  # Replace with your SNS topic ARN
